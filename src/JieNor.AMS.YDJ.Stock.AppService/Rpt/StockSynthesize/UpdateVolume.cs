using JieNor.AMS.YDJ.Store.AppService.Service;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.StockSynthesize
{
    /// <summary>
    /// 库存综合查询：获取待出库量
    /// </summary>
    [InjectService]
    [FormId("rpt_stocksynthesize")]
    [OperationNo("UpdateVolume")]
    public class UpdateVolume : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            try
            {
                //同步商品体积数据
                var syncProductVolumeService = this.Context.Container.GetService<SyncProductVolumeService>();
                var syncResult = syncProductVolumeService.SyncProductVolume(this.Context, this.HtmlForm.Id);
            }
            finally
            {
                //刷新页面
                this.AddRefreshPageAction();
            }
        }
    }
}
