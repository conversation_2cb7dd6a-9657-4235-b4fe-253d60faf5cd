using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.StockSynthesize
{
    /// <summary>
    /// 库存综合查询：更新扩展数据
    /// </summary>
    [InjectService]
    [FormId("rpt_stocksynthesize")]
    [OperationNo("UpdateExtendData")]
    public class UpdateExtendData : AbstractOperationServicePlugIn, IOnceWarmUpService
    {
        /// <summary>
        /// 数据库服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        [InjectProperty]
        protected ILogServiceEx LogServiceEx { get; set; }

        /// <summary>
        /// 扩展数据表名
        /// </summary>
        private static readonly string _ExtendDataTableName = "T_STK_INVENTORYLIST_EXTENDDATA";

        static object lockObj = new object();

        /// <summary>
        /// 缓存组织对应的表信息
        /// </summary>
        static ConcurrentDictionary<string, string > _dctOrgTblCache = new ConcurrentDictionary<string, string>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 是否已运行过一次
        /// </summary>
        private bool IsRunOnce { get; set; }

        /// <summary>
        /// 热身服务
        /// </summary>
        /// <param name="ctx"></param>
        public void Execute(UserContext ctx)
        {
            BuildOrgTableInfo(ctx);
        }


        private void  BuildOrgTableInfo(UserContext ctx)
        {
            if (_dctOrgTblCache.Count > 0 || ctx==null)
            {
                return;
            }

            lock (lockObj)
            {
                var sql = @"/*dialect*/ select fmainorgid,count(fid) fcount 
                             from T_STK_INVENTORYLIST  
                             group by fmainorgid
                             having count(fid)>1000 ";
                var datas = this.DBService.ExecuteDynamicObject(ctx, sql);
                if (datas == null)
                {
                    return;
                }

                try
                {
                    Dictionary<string, string> maps = new Dictionary<string, string>();
                    var createSqls = new List<string>();
                    sql = @"/*dialect*/select [name] as ftablename from sysobjects with(nolock) where [name] like 't_stk_inv_x%'  and xtype='u' ";
                    var existTbls = this.DBService.ExecuteDynamicObject(ctx, sql);
                    foreach (var item in datas)
                    {
                        var orgId = Convert.ToString(item["fmainorgid"]);
                        var tblName = "t_stk_inv_x{0}".Fmt(orgId);
                        maps[orgId] = tblName;
                        var exist = existTbls.FirstOrDefault(f => Convert.ToString(f["ftablename"]).EqualsIgnoreCase(tblName));
                        if (exist == null)
                        {
                            createSqls.Add($@"/*dialect*/
                                                select fid, fmaterialid,fattrinfo, fattrinfo_e, fcustomdesc, funitid, fstockunitid, fstorehouseid, fstorelocationid,
	                                                    fstockstatus, flotno, fmtono, fownertype, fownerid, fqty, fstockqty, fqty as fusableqty,fstockqty as fstockusableqty,  
                                                        CAST(0 as decimal(23, 10)) as fintransitqty, CAST(0 as decimal(23, 10)) as freserveqty,
                                                        CAST(0 as decimal(23, 10)) as fstockintransitqty, CAST(0 as decimal(23, 10)) as fstockreserveqty
                                                into {tblName}
	                                            from t_stk_inventorylist i with(nolock) 
	                                            where  1=0 ; ");
                            createSqls.Add("/*dialect*/ALTER TABLE {0} ADD CONSTRAINT pk_inv_x{1} PRIMARY KEY CLUSTERED  (fid) ; ".Fmt(tblName, orgId));
                            createSqls.Add("/*dialect*/create index idx_inv_x{0}_mat on {1}(fmaterialid,fattrinfo_e,fcustomdesc) ; ".Fmt(orgId, tblName));
                        }
                    }
                    
                    if (createSqls.Count > 0)
                    {
                        this.DBServiceEx.ExecuteBatch(ctx, createSqls);
                    }

                    foreach (var item in maps)
                    {
                        _dctOrgTblCache[item.Key] = item.Value;
                    }
                }
                catch (Exception ex)
                {
                }
            }
        }


        protected override void InitializeServicePlugIn(OperationContext operCtx)
        {
            base.InitializeServicePlugIn(operCtx);

            BuildOrgTableInfo(operCtx.UserContext );
        }


        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            // 通过临时表生成数据
            var tmpTableName = "";
            if (_dctOrgTblCache.ContainsKey(this.Context.Company))
            {
                tmpTableName = _dctOrgTblCache[this.Context.Company];
            }
            else
            {
                tmpTableName = this.DBService.CreateTemporaryTableName(this.Context, false);
            }

            // 业务数据临时表名
            var tmpTableNameForBizData = this.DBService.CreateTemporaryTableName(this.Context, false);

            List<KeyValuePair<string, IEnumerable<SqlParam>>> sqls = new List<KeyValuePair<string, IEnumerable<SqlParam>>>();

            this.InitData(sqls, tmpTableName, tmpTableNameForBizData);

            this.UpdateReserveQty(sqls, tmpTableName);

            //this.ConvertQty(sqls, tmpTableName);

            this.UpdateData(sqls, tmpTableName);

            var strSql = sqls.Select(s => s.Key).JoinEx("\r\n", false);
            var sqlParams = sqls.SelectMany(s => s.Value).Distinct();

            this.DBServiceEx.Execute(this.Context, strSql, sqlParams);
        }
         

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitData(List<KeyValuePair<string, IEnumerable<SqlParam>>> sqls, string tmpTableName, string tmpTableNameForBizData)
        {
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
                new SqlParam("@ftopmainorgid", DbType.String, this.Context.TopCompanyId),
            };
            string sql = "";
            if (tmpTableName.StartsWith("#"))
            {
                //临时表
                sql = $@"/*dialect*/
                            select fid, fmaterialid,fattrinfo, fattrinfo_e, fcustomdesc, funitid, fstockunitid, fstorehouseid, fstorelocationid,
	                                fstockstatus, flotno, fmtono, fownertype, fownerid, fqty, fstockqty, fqty as fusableqty,fstockqty as fstockusableqty,  
                                    CAST(0 as decimal(23, 10)) as fintransitqty, CAST(0 as decimal(23, 10)) as freserveqty,
                                    CAST(0 as decimal(23, 10)) as fstockintransitqty, CAST(0 as decimal(23, 10)) as fstockreserveqty
                            into {tmpTableName}
	                        from t_stk_inventorylist i with(nolock) 
	                        where fmainorgid=@fmainorgid ; ";
                sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams));
                sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>("/*dialect*/create index #idx_{0}_fid on {0}(fid) ; ".Fmt(tmpTableName), sqlParams));
                sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>("/*dialect*/create index #idx_{0}_mat on {0}(fmaterialid,fattrinfo_e,fcustomdesc); ".Fmt(tmpTableName), sqlParams));
            }
            else
            {
                //物料表
                sql = $@"/*dialect*/
                            insert into {tmpTableName} (fid, fmaterialid,fattrinfo, fattrinfo_e, fcustomdesc, funitid, fstockunitid, fstorehouseid, fstorelocationid,
                                                        fstockstatus, flotno, fmtono, fownertype, fownerid, fqty, fstockqty, fusableqty,fstockusableqty,
                                                        fintransitqty, freserveqty, fstockintransitqty, fstockreserveqty) 
                            select fid, fmaterialid,fattrinfo, fattrinfo_e, fcustomdesc, funitid, fstockunitid, fstorehouseid, fstorelocationid,
	                                fstockstatus, flotno, fmtono, fownertype, fownerid, fqty, fstockqty, fqty as fusableqty,fstockqty as fstockusableqty,  
                                    CAST(0 as decimal(23, 10)) as fintransitqty, CAST(0 as decimal(23, 10)) as freserveqty,
                                    CAST(0 as decimal(23, 10)) as fstockintransitqty, CAST(0 as decimal(23, 10)) as fstockreserveqty 
	                        from t_stk_inventorylist i with(nolock) 
	                        where fmainorgid=@fmainorgid and not exists ( select 1 from {tmpTableName} x where x.fid=i.fid ) ;

                            update t set t.fqty=i.fqty, t.fstockqty=i.fstockqty, t.fusableqty=i.fqty, fstockusableqty=i.fstockqty,
                                         t.fintransitqty=0,freserveqty=0,fstockintransitqty=0,fstockreserveqty=0
                            from {tmpTableName} t
                            inner join t_stk_inventorylist i with(nolock) on t.fid=i.fid ;
                            ";

                //先执行，后续库存数据直接从tmpTableName里取
                this.DBServiceEx.Execute(this.Context, sql, sqlParams);
            }
             
            sql = $@"/*dialect*/
                    select * into {tmpTableNameForBizData} from
                    (
                        select pd.fmaterialid,pd.fattrinfo_e,pd.fcustomdes_e as fcustomdesc,sum(pd.fqty-pd.finstockqty+pd.freturnqty) fqty 
                        from t_ydj_poorderentry pd with(nolock)
                        inner join t_ydj_purchaseorder p with(nolock) on p.fid = pd.fid and p.fcancelstatus = '0' and p.fstatus = 'E' and p.fmainorgid in(@fmainorgid,@ftopmainorgid) -- and(p.fclosestatus = '' or p.fclosestatus = '0')
                         and pd.fclosestatus_e  in('0','2') 
                        group by pd.fmaterialid,pd.fattrinfo_e,pd.fcustomdes_e
                    ) t ; ";
            sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams));              
            sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>("create index #idx_{0}_mat on {0}(fmaterialid,fattrinfo_e,fcustomdesc)".Fmt(tmpTableNameForBizData), sqlParams));
             
            sql = $@"/*dialect*/
                    update t0 set fintransitqty=t1.fqty, fstockintransitqty=t1.fqty
                    from {tmpTableName} t0
                    inner join {tmpTableNameForBizData} t1 on t0.fmaterialid=t1.fmaterialid and t0.fattrinfo_e=t1.fattrinfo_e and t0.fcustomdesc=t1.fcustomdesc";
            sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(sql, new List<SqlParam>()));
        }

        /// <summary>
        /// 更新【预留量】以及【可用量】
        /// </summary>
        private void UpdateReserveQty(List<KeyValuePair<string, IEnumerable<SqlParam>>> sqls, string tmpTableName)
        {
            var allReserveObjs = GetReserveInfos();
            if (allReserveObjs == null || allReserveObjs.Count <= 0)
            {
                return;
            }

            var allInventoryObjs = GetInventoryInfos(tmpTableName);
            if (allInventoryObjs == null || allInventoryObjs.Count <= 0)
            {
                return;
            }

            var grpBy = allReserveObjs.GroupBy(f => new
            {
                fmaterialid = f.fmaterialid,
                fattrinfo_e = f.fattrinfo_e,
                //fname_e = f.fname_e,
                fcustomdesc = f.fcustomdesc,
                funitid = f.funitid,
                fstockstatus = f.fstockstatus,
                fmtono = f.fmtono,
                fstorehouseid = f.fstorehouseid,
            }).OrderByDescending(x => x.Key.fmtono)//按跟踪号进行排序，以便优先分配有跟踪号报表数据行
            .ThenByDescending(x => x.Key.fstorehouseid)
            .ToList();

            foreach (var item in grpBy)
            {
                //总的预留量（所有仓库仓位的总和）
                var allReserveQty = item.Sum(f => f.freserveqty);
                if (allReserveQty <= 0) continue;

                var levels = new[]
                    { Enu_StockCompareLevel.Two, Enu_StockCompareLevel.Three, Enu_StockCompareLevel.Four};
                var inventoryItems = new List<StkInventoryInfo>();
                foreach (var level in levels)
                {
                    if (allReserveQty <= 0) break;

                    inventoryItems = allInventoryObjs
                        .Where(x => SthItemComparate(item.First(), x, level))
                        .ToList();
                    if (inventoryItems.Count == 0) continue;

                    inventoryItems = inventoryItems
                        .Where(f => f.fqty - f.freserveqty > 0)
                        //按跟踪号进行排序，以便优先分配有跟踪号报表数据行
                        .OrderByDescending(x => x.fmtono)
                        //其次按可用量排序
                        .ThenByDescending(f => f.fqty - f.freserveqty)
                        .ToList();
                    if (inventoryItems.Count == 0) continue;

                    allReserveQty = UpdateReserveQty(inventoryItems, allReserveQty);
                }

                //如果到这个时候还是有预留量没有匹配完，没撤了，放最后一行吧
                if (allReserveQty > 0 && inventoryItems.Count > 0)
                {
                    var lastItem = inventoryItems.Last();
                    //库存基本单位已预留数量
                    var invReserveQty = lastItem.freserveqty;
                    lastItem.freserveqty = invReserveQty + allReserveQty;
                }
            }

            DataTable dt = new DataTable();
            dt.Columns.Add("fid");
            dt.Columns.Add("freserveqty", typeof(decimal));

            dt.BeginLoadData();
            foreach (var item in allInventoryObjs)
            {
                var reserveQty = item.freserveqty;
                if (reserveQty > 0)
                {
                    dt.Rows.Add(item.id, reserveQty);
                }
            }
            dt.EndLoadData();

            var reserveQtyTableName = "";

            if (dt.Rows.Count > 0)
            {
                reserveQtyTableName = this.DBService.CreateTempTableWithDataTable(this.Context, dt, 100, isPhysics: true);

                sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>("/*dialect*/create index #idx_{0}_fid on {0}(fid) ;".Fmt(reserveQtyTableName), new List<SqlParam>()));

                var sql = $@"/*dialect*/
                            update t set freserveqty=t2.freserveqty, fstockreserveqty=t2.freserveqty
                                , fusableqty=fqty-t2.freserveqty, fstockusableqty=fqty-t2.freserveqty
                            from {tmpTableName} t inner join {reserveQtyTableName} t2 on t.fid=t2.fid ;
                        ";
                sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(sql, new List<SqlParam>()));
            }

            allReserveObjs.Clear();
            allInventoryObjs.Clear();
            grpBy.Clear();

        }

        //        /// <summary>
        //        /// 单位换算
        //        /// </summary>
        //        private void ConvertQty(List<KeyValuePair<string, IEnumerable<SqlParam>>> sqls, string tmpTableName)
        //        {
        //            string strUpdateSql = "";

        //            // 单位不一致进行单位换算
        //            strUpdateSql += $@"/*dialect*/
        //update {tmpTableName} as u set (fstockreserveqty, fstockusableqty, fstockintransitqty) = 
        //(
        //    select top 1 
        //        (
        //            case when u30.fcvtrate=0 then 0 
        //            else t.freserveqty * u20.fcvtrate / u30.fcvtrate 
        //            end
        //        ) as fstockreserveqty,
        //        (
        //            case when u30.fcvtrate=0 then 0 
        //            else t.fusableqty * u20.fcvtrate / u30.fcvtrate 
        //            end
        //        ) as fstockusableqty,
        //        (
        //            case when u30.fcvtrate=0 then 0 
        //            else t.fintransitqty * u20.fcvtrate / u30.fcvtrate 
        //            end
        //        ) as fstockintransitqty
        //    from {tmpTableName} t
        //    inner join t_bd_materialunit u20 with(nolock) on u20.fid = t.fmaterialid and u20.funitid = t.funitid
        //    inner join t_bd_materialunit u30 with(nolock) on u30.fid = t.fmaterialid and u30.funitid = t.fstockunitid
        //    where u.fid = t.fid 
        //) where u.funitid<>u.fstockunitid;";

        //            //this.DBServiceEx.Execute(this.Context, strUpdateSql);
        //            sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(strUpdateSql, new List<SqlParam>()));
        //        }

        private void UpdateData(List<KeyValuePair<string, IEnumerable<SqlParam>>> sqls, string tmpTableName)
        {
            string sql = $@"/*dialect*/
					--新增，如果所有字段都相同则删除临时表,这部分实际上是无需覆写的
					delete inv 
					from T_STK_INVENTORYLIST_EXTENDDATA as t with (nolock) 
					inner join {tmpTableName} as inv on t.fid=inv.fid where t.fqty=inv.fqty and t.fstockqty=inv.fstockqty 
					and t.fusableqty=inv.fusableqty
					and t.fstockusableqty=inv.fstockusableqty
					and t.fintransitqty=inv.fintransitqty
					and t.fstockintransitqty=inv.fstockintransitqty
					and t.freserveqty=inv.freserveqty
					and t.fstockreserveqty=inv.fstockreserveqty; ";

            //再插入不存在的数据
            sql += $@"/*dialect*/
            insert into {_ExtendDataTableName}(fid, fmainorgid, fqty, fstockqty, fmaterialid, funitid, fstockunitid, fusableqty, fstockusableqty, fintransitqty, fstockintransitqty, freserveqty, fstockreserveqty)
            select fid, '{this.Context.Company}' as fmainorgid, fqty, fstockqty, fmaterialid, funitid, fstockunitid, fusableqty, fstockusableqty, fintransitqty, fstockintransitqty, freserveqty, fstockreserveqty 
            from {tmpTableName} t with(nolock)
            where not exists ( select 1 from {_ExtendDataTableName} x  with(nolock)   where x.fid=t.fid )  ;";

            /*dir  ……*/
            // 重置需要计算的字段
            sql += $@" /*dialect*/
                        update t set 
                            fqty=inv.fqty, fstockqty=inv.fstockqty 
                            , fusableqty=inv.fusableqty, fstockusableqty=inv.fstockusableqty 
                            , fintransitqty=inv.fintransitqty, fstockintransitqty=inv.fstockintransitqty 
                            , freserveqty=inv.freserveqty, fstockreserveqty=inv.fstockreserveqty
                            , fbizindbqty=0, findbqty=0
                        from {_ExtendDataTableName} as t
                        inner join {tmpTableName} as inv on t.fid=inv.fid; ";

            sqls.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(sql, new List<SqlParam>()));
        }

        /// <summary>
        /// 更新预留量
        /// </summary>
        /// <param name="inventoryItem">即时库存信息</param>
        /// <param name="beReserveQty">即将增加的预留量</param>
        /// <returns>实际增加的预留量</returns>
        private static decimal UpdateReserveQty(StkInventoryInfo inventoryItem, decimal beReserveQty)
        {
            //库存基本单位数量
            var invQty = inventoryItem.fqty;

            //库存基本单位已预留数量
            var invReserveQty = inventoryItem.freserveqty;

            //库存当前基本单位可用量
            var invUseQty = invQty - invReserveQty;

            //如果当前库存可用量小于等于0，则不能匹配
            if (invUseQty <= 0)
            {
                return 0;
            }

            //预留量=库存当前基本单位可用量和预留单基本单位预留量两者的最小数量
            var minQty = invUseQty >= beReserveQty ? beReserveQty : invUseQty;

            //库存基本单位已预留数量叠加最小数量
            inventoryItem.freserveqty = invReserveQty + minQty;

            return minQty;
        }

        /// <summary>
        /// 更新预留
        /// </summary>
        /// <param name="inventoryItems">即时库存信息</param>
        /// <param name="beReserveQty">即将增加的预留量</param>
        /// <returns>剩余预留量</returns>
        private static decimal UpdateReserveQty(List<StkInventoryInfo> inventoryItems, decimal beReserveQty)
        {
            foreach (var inventoryItem in inventoryItems)
            {
                if (beReserveQty <= 0)
                {
                    break;
                }

                var reserveQty = UpdateReserveQty(inventoryItem, beReserveQty);

                //总的预留量减少
                beReserveQty -= reserveQty;
            }

            return beReserveQty;
        }

        /// <summary>
        /// 获取预留信息
        /// </summary>
        /// <returns></returns>
        private List<StkReserveInfo> GetReserveInfos()
        {
            //加载有效的预留单明细行
            List<SqlParam> lstSqlParas = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
            };

            var reserveSql = $@"/*dialect*/
                                select 
	                                re.fentryid
	                                ,re.fparentid
	                                ,re.fmaterialid
	                                ,re.fattrinfo
                                    ,re.fattrinfo_e
	                                ,re.fcustomdesc
	                                ,re.funitid
	                                ,rd.fstockstatus
	                                ,re.fmtono
	                                ,rd.fstorehouseid
	                                ,rd.fdirection_d
	                                ,case rd.fdirection_d when 0 then rd.fqty_d else rd.fqty_d *-1 end as freserveqty 
                                from t_stk_reservebill r with(nolock) 
                                inner join t_stk_reservebillentry re with(nolock) on r.fid=re.fid and re.fqty >0 
                                inner join t_stk_reservebilldetail rd with(nolock) on rd.fentryid=re.fentryid 
                                where r.fmainorgid=@fmainorgid and r.fstatus='E' and r.fcancelstatus='0'
                                ";

            var allReserveObjs = this.DBService.ExecuteDynamicObject(this.Context, reserveSql, lstSqlParas);
            var results = new List<StkReserveInfo>();
            if (allReserveObjs != null && allReserveObjs.Any())
            {
                foreach (var item in allReserveObjs)
                {
                    results.Add(new StkReserveInfo()
                    {
                        fparentid = item["fparentid"] == null ? "" : item["fparentid"].ToString().Trim(),
                        fattrinfo = item["fattrinfo"] == null ? "" : item["fattrinfo"].ToString().Trim(),
                        fattrinfo_e = item["fattrinfo_e"] == null ? "" : item["fattrinfo_e"].ToString().Trim(),
                        fcustomdesc = item["fcustomdesc"] == null ? "" : item["fcustomdesc"].ToString().Trim(),
                        fentryid = item["fentryid"] == null ? "" : item["fentryid"].ToString().Trim(),
                        fmaterialid = item["fmaterialid"] == null ? "" : item["fmaterialid"].ToString().Trim(),
                        fmtono = item["fmtono"] == null ? "" : item["fmtono"].ToString().Trim(),
                        fstockstatus = item["fstockstatus"] == null ? "" : item["fstockstatus"].ToString().Trim(),
                        fstorehouseid = item["fstorehouseid"] == null ? "" : item["fstorehouseid"].ToString().Trim(),
                        funitid = item["funitid"] == null ? "" : item["funitid"].ToString().Trim(),
                        fdirection_d = item["fdirection_d"] == null ? "" : item["fdirection_d"].ToString().Trim(),
                        freserveqty = item["freserveqty"] == null ? 0 : Convert.ToDecimal(item["freserveqty"]),
                    });
                }
            }
            return results;
        }

        /// <summary>
        /// 获取库存信息
        /// </summary>
        /// <returns></returns>
        private List<StkInventoryInfo> GetInventoryInfos(string tmpTableName)
        {
            var rptSql = ""; 
            if(tmpTableName.StartsWith("#"))
            {
                rptSql = $@" /*dialect*/
                            select 
	                            r.fid
	                            ,r.fmaterialid
	                            ,r.fattrinfo
                                ,r.fattrinfo_e
	                            ,r.fcustomdesc
	                            ,r.funitid
	                            ,r.fstockstatus
	                            ,r.fmtono
	                            ,r.fstorehouseid
	                            ,r.fstorelocationid
	                            ,r.fqty
                            from T_STK_INVENTORYLIST r with(nolock)
                            where r.fmainorgid='{this.Context.Company}' and r.fqty>0  --只处理有库存的，与库存综合查询保持一致;
                            ";
            }
            else
            {
                rptSql = $@" /*dialect*/
                            select 
	                            r.fid
	                            ,r.fmaterialid
	                            ,r.fattrinfo
                                ,r.fattrinfo_e
	                            ,r.fcustomdesc
	                            ,r.funitid
	                            ,r.fstockstatus
	                            ,r.fmtono
	                            ,r.fstorehouseid
	                            ,r.fstorelocationid
	                            ,r.fqty
                            from {tmpTableName} r with(nolock)
                            where r.fqty>0  --只处理有库存的，与库存综合查询保持一致;
                            ";
            }

            var rptDt = this.HtmlForm.GetDynamicObjectType(this.Context);              
            var allInventoryObjs = this.DBService.ExecuteDynamicObject(this.Context, rptDt,  rptSql, new List<SqlParam>());

            var results = new List<StkInventoryInfo>();
            if (allInventoryObjs != null && allInventoryObjs.Any())
            {
                foreach (var item in allInventoryObjs)
                {
                    results.Add(new StkInventoryInfo()
                    {
                        id = item["id"] == null ? "" : item["id"].ToString().Trim(),
                        fattrinfo = item["fattrinfo"] == null ? "" : item["fattrinfo"].ToString().Trim(),
                        fattrinfo_e = item["fattrinfo_e"] == null ? "" : item["fattrinfo_e"].ToString().Trim(),
                        fcustomdesc = item["fcustomdesc"] == null ? "" : item["fcustomdesc"].ToString().Trim(),
                        fmaterialid = item["fmaterialid"] == null ? "" : item["fmaterialid"].ToString().Trim(),
                        fmtono = item["fmtono"] == null ? "" : item["fmtono"].ToString().Trim(),
                        fstockstatus = item["fstockstatus"] == null ? "" : item["fstockstatus"].ToString().Trim(),
                        fstorehouseid = item["fstorehouseid"] == null ? "" : item["fstorehouseid"].ToString().Trim(),
                        fstorelocationid = item["fstorelocationid"] == null ? "" : item["fstorelocationid"].ToString().Trim(),
                        funitid = item["funitid"] == null ? "" : item["funitid"].ToString().Trim(),
                        fqty = item["fqty"] == null ? 0 : Convert.ToDecimal(item["fqty"]),
                        freserveqty = item["freserveqty"] == null ? 0 : Convert.ToDecimal(item["freserveqty"]),
                    });
                }
            }
            return results;
        }


        private bool SthItemComparate(StkReserveInfo dyn1, StkInventoryInfo dyn2, Enu_StockCompareLevel level)
        {
            // 4：按 商品+辅助属性+定制说明+计量单位  匹配
            bool value = dyn1.fmaterialid.EqualsIgnoreCase(dyn2.fmaterialid)
                         && dyn1.fattrinfo_e.EqualsIgnoreCase(dyn2.fattrinfo_e)
                         && dyn1.fcustomdesc.EqualsIgnoreCase(dyn2.fcustomdesc)
                         && dyn1.funitid.EqualsIgnoreCase(dyn2.funitid);

            // 加【物流跟踪号】匹配
            if (level <= Enu_StockCompareLevel.Three)
            {
                value = value
                        && (dyn1.fmtono.EqualsIgnoreCase(dyn2.fmtono) ||
                            dyn2.fmtono.IsNullOrEmptyOrWhiteSpace());
            }

            // 加【仓库】+【库存状态】匹配
            if (level <= Enu_StockCompareLevel.Two)
            {
                value = value
                        && dyn1.fstorehouseid.EqualsIgnoreCase(dyn2.fstorehouseid)
                        && dyn1.fstockstatus
                            .EqualsIgnoreCase(dyn2.fstockstatus);
            }

            return value;
        }

    }







    internal class StkReserveInfo
    {
        public string fentryid;
        public string fparentid;
        public string fmaterialid;
        public string fattrinfo;
        public string fattrinfo_e;
        public string fcustomdesc;
        public string funitid;
        public string fstockstatus;
        public string fmtono;
        public string fstorehouseid;
        public string fdirection_d;
        public decimal freserveqty;
    }


    internal class StkInventoryInfo
    {
        public string id;
        public string fmaterialid;
        public string fattrinfo;
        public string fattrinfo_e;
        public string fcustomdesc;
        public string funitid;
        public string fstockstatus;
        public string fmtono;
        public string fstorehouseid;
        public string fstorelocationid;
        public decimal fqty;
        public decimal freserveqty;
    }

}

