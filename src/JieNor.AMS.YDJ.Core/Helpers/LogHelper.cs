using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Utils;

namespace JieNor.AMS.YDJ.Core.Helpers
{
    public class LogHelper
    {
        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="strContent"></param>
        public static void WriteLog(string fileName, string strContent)
        {
            //string basefilePath = "app_data/debuglog/"; //基础路径
            //string customfilePath = DateTime.Now.ToString("yyyy-MM-dd") + "/";
            //string finalfilePath = basefilePath + customfilePath;
            //string filesuffix = ".txt";//文件后缀
            //Write(finalfilePath, fileName, filesuffix, strContent);

            DebugUtil.WriteLogToFile(strContent, fileName);
        }

        //public static void Write(string filePath, string fileName, string filesuffix, string strContent)
        //{

        //    var path = filePath + fileName + filesuffix; ;
        //    var xpath = Path.GetDirectoryName(new Uri(Assembly.GetCallingAssembly().CodeBase).AbsolutePath);
        //    xpath = xpath.Replace(@"/", @"\");
        //    xpath = xpath.Substring(0, xpath.LastIndexOf(@"\"));
        //    DirectoryInfo codeFilePath = new DirectoryInfo(xpath);
        //    var FilePath = Path.Combine(xpath, path);
        //    var Dir = Path.GetDirectoryName(FilePath);
        //    if (!Directory.Exists(Dir))
        //    {
        //        Directory.CreateDirectory(Dir);
        //    }

        //    //共享写不占用文件，但在多线程同时操作的情况下写入信息有可能丢失
        //    using (var fs = File.Open(FilePath, FileMode.Create, FileAccess.Write, FileShare.Write))
        //    {
        //        using (var sw = new StreamWriter(fs))
        //        {
        //            sw.WriteLine(strContent);
        //        }
        //    }
        //}
    }
}
