using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    /// <summary>
    /// 预留更新设置参数
    /// </summary>
    [Serializable]
    public class ReserveUpdateSetting
    {
        /// <summary>
        /// 预留更新时的前置条件
        /// </summary>
        public string PreCondition { get; set; }

        /// <summary>
        /// 数量字段标识
        /// </summary>
        public string QtyFieldKey { get; set; }

        /// <summary>
        /// 预留维度字段映射表
        /// </summary>
        public List<ReserveFlexMap> FlexMaps = new List<ReserveFlexMap>();
    }
}