using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity.Customer
{
    /// <summary>
    /// 客户共享信息
    /// </summary>
    public class CustomerShareItem
    {
        /// <summary>
        /// 共享的部门
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 共享说明
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 共享用户（可选）
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 共享时间（可选）
        /// </summary>
        public DateTime? ShareTime { get; set; }
    }
}
