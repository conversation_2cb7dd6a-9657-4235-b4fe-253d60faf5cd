<!--页面菜单列表-->
<div class="page-menu-list tab_title">
	<b>团队</b>
	
</div>
<div class="Btn-menu">
</div>
<!--页面内容面板-->
<form action="###" class="form-horizontal">
	<div class="leftTab">
	    <div class="portlet box yellow-casablanca fill-top">
	        <div class="portlet-title">
	            <div class="caption">基本信息</div>
	            <div class="tools"><a href="###" class="collapse"></a></div>
	        </div>
	        <div class="portlet-body form">
	            <div class="form-body">
	            	<!--验证提示信息-->
			        <div class="alert alert-danger display-hide">
			            <button class="close" data-close="alert"></button>
			            页面中有一些带红色星号 * 的信息是必填项，请检查是否都有正确录入！
			        </div>
					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<label class="col-md-4 control-label"><span class="required">*</span>团队编号</label>
								<div class="col-md-7">
									<div class="input-icon right">
										<input type="text" class="form-control" name="fnumber" required="required">
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-md-4 control-label">团队人数</label>
								<div class="col-md-7">
									<input type="text" class="form-control" autocomplete="off" name="fmembercount" />
								</div>
							</div>
							<div class="form-group">
								<label class="col-md-4 control-label">负责区域<span class="required">*</span>	</label>
								<div class="col-md-7 cols">
									<div class="city-border-bottom input-group right">
										<div class="col-xs-12">
											<input type="city" province="fprovince" city="fcity" region="fregion" class="form-control" autocomplete="off"
												   name="fprovince" value="" required />
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-md-4 control-label"></label>
								<div class="col-md-7">
									<textarea class="form-control" autocomplete="off"
											  name="faddress" placeholder="请填写详细的地址" rows="3"></textarea>
								</div>
							</div>

							<div class="form-group">
								<label class="col-md-4 control-label"><span class="required">*</span>关联部门</label>
								<div class="col-md-7">
									<div class="input-icon right input-group">
										<i class="fa"></i>
										<input type="lookup" class="form-control" autocomplete="off" required
											   name="fdeptid" placeholder="关联部门" />
									</div>
								</div>
							</div>

						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label class="col-md-4 control-label"><span class="required">*</span>团队名称</label>
								<div class="col-md-7">
									<div class="input-icon right">
										<input type="text" class="form-control" name="fname" required="required">
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-md-4 control-label">情况说明</label>
								<div class="col-md-7">
									<textarea class="form-control" autocomplete="off"
											  name="fdescription" rows="5"></textarea>
								</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label class="col-md-4 control-label">团队类型</label>
								<div class="col-md-7">
									<div class="input-icon right">
										<select class="form-control select2me simple" name="fteamtype"></select>
									</div>
								</div>
							</div>
						</div>
					</div>
	            </div>
	        </div>
	    </div>
	    <div class="portlet box yellow-casablanca">
	        <div class="portlet-title">
	            <div class="caption">人员信息</div>
	            <div class="tools"><a href="###" class="collapse"></a></div>
	        </div>
	        <div class="portlet-body form">
	            <div class="form-body">
	                <div class="row">
	                	<div class="col-md-12">
                            <table entryid="fstaffentry" data-options="allowAdd:true,allowDel:true,allowEdit:true"></table>
                        </div>
	                </div>   
	            </div>
	        </div>
	    </div>
    </div>
</form>