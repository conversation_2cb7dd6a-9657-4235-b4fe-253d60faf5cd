<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>拒绝订单理由</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body id="refuse" class="bg-white">
    <div class="bar bar-header bar-white">
        <a class="bar-title-left blue back" onclick="Andoggy.finishPage()">取消</a>
        <div class="title">拒绝订单理由</div>
        <a class="bar-title-right blue" onclick="submit()">确定</a>
    </div>
    <ul class="content has-header">
        <li>
            <textarea id="reason-box" class="textarea" maxlength="20" oninput="this.value=this.value.substring(0, 20), DomTag('existnum').innerText = this.value.length"></textarea>
            <span class="grey"><span id="existnum">0</span><span>/20</span></span>
        </li>
        <li class="grey instructions">选中下方标签后自动带入到输入框中</li>
        <li id="reason">
            <div class="bg-grey col45 floatL lables">人手不够</div>
            <div class="bg-grey col45 floatR lables">技能不匹配</div>
            <div class="bg-grey col45 floatL lables">工期已排满</div>
        </li>
    </ul>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>

        //输入框中原有字数
        DomTag('existnum').innerText = String(DomTag('reason-box').value).Trim().length;
        //点击下面原因并赋值到输入框中
        var _lable = DomTag('#reason .lables');
        for (var i = 0; i < _lable.length; i++) {
            _lable[i].addEventListener('touchstart', function () {
                RemoveClass(this, 'bg-grey');
                AddClass(this, 'bg-blue');
                var val = String(DomTag('reason-box').value).Trim();
                DomTag('reason-box').value = (val + ' ' + this.innerText).substring(0, 20);
                DomTag('existnum').innerText = String(DomTag('reason-box').value).Trim().length;
            });

            _lable[i].addEventListener('touchend', function () {
                RemoveClass(this, 'bg-blue');
                AddClass(this, 'bg-grey');
            });


        }

        var fid = GetQueryString('fid');
       
        //填写完原因后提交原因数据到后台保存
        function submit() {
            if (String(DomTag('reason-box').value).Trim().length >= 4) {
                Ajax({
                    url: "/bill/ydj_service?operationno=setstatus04&format=json",
                    data: {
                        operationName: "队长拒单",
                        selectedRows: [{ PKValue: fid }],
                        operationNo:"setstatus04",
                        simpledata: { opid: "setstatus04", serstatus: "sersta03", 'content': DomTag('reason-box').value, }
                    },
                    callback: function () {
                        var Json = arguments[0];
                        var result = Json.operationResult.complexMessage;
                        if (String(Json.operationResult.isSuccess).Boolean()) {
                            Message.Alert('操作成功!', 2000)
                            setTimeout(function () {
                                var isdetail = String(GetQueryString("isdetail")).Boolean();
                                isdetail ? localStorage.setItem("Reload", "true") : localStorage.setItem("FunEvent", "TabExec(0)");
                                Andoggy.finishPage();
                            }, 2000)

                        } else {
                            if (Json.operationResult.complexMessage.errorMessages.length > 0) {
                                Message.Alert(Json.operationResult.complexMessage.errorMessages[0])
                            }
                        }
                    }
                });
            } else {
                Message.Alert("拒单理由字符不能少于4！");
            }
        };
    </script>
</body>
</html>
