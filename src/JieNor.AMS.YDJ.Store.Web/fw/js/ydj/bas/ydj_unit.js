///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/bas/ydj_unit.js
*/
; (function () {
    var ydj_unit = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************


        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.onBillInitialized = function (args) {
            var that = this;

        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcvtrate':
                    var isBaseUnit = that.Model.getValue({ id: 'fisbaseunit' });
                    if (!isBaseUnit) {
                        //基本单位的精度
                        var basUnitPrecision = 0;
                        var basUnitObj = that.Model.getValue({ id: 'fbaseunitid' });
                        if (basUnitObj && basUnitObj.fprecision)
                        {
                            basUnitPrecision = basUnitObj.fprecision;
                        }
                        //根据换算率计算精度
                        var precision = basUnitPrecision;
                        var cvtRate = e.value;
                        if (cvtRate > 0) {
                            var _precision = Math.ceil(Math.log10(cvtRate));
                            if (_precision > precision) precision = _precision;
                        }
                        if (precision < 0) precision = 0;
                        that.Model.setValue({ id: 'fprecision', value: precision });
                    }

                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_unit = window.ydj_unit || ydj_unit;
})();