; (function () {
    var bas_appletparameter = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                //保存操作后清除本地缓存数据
                case 'save':
                    localStorage.removeItem("storesysparam");
                    break;
            }
        };

        return _child;
    })(ParameterPlugIn);
    window.bas_appletparameter = window.bas_appletparameter || bas_appletparameter;
})();