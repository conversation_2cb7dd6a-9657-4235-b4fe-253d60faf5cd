; (function () {
    var bi_categorymapping = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }
        __extends(_child, _super);

        _child.prototype.onInitialized = function (e) {
            var that = this;
            var pageid = that.Model.viewModel.pageId;
            that.Model.blockUI({ id: '#page#' });

            yiAjax.p('/bill/bi_categorymapping?operationno=getmenuinfo', { simpledata: { formId: that.Model.viewModel.formId } },
                function (r) {
                    debugger;
                    that.Model.unblockUI({ id: '#page#' });
                    var encryptData = r.operationResult.srvData;
                    if (encryptData.openNewTabOrIFrame == 0) {
                        that.openIFrame(encryptData, pageid);
                    } else if (encryptData.openNewTabOrIFrame == 1) {
                        that.openNewTab(encryptData, pageid);
                    }
                    //if (encryptData.returnUrl) {
                    //    var url = encryptData.returnUrl + "&fmainorgid=" + Consts.loginCompany.id + "&ssoToken=" + Consts.ssoToken;

                    //    $("#" + pageid).append('<iframe  id="finebiview' + pageid + '" src="' + url + '" width="100%" style="margin:0px" frameborder=0></iframe>');
                    //    var iframe = $("#" + that.Model.viewModel.pageId).find("#finebiview" + pageid);
                    //    setInterval(function () {
                    //        iframe.attr('height', window.innerHeight - 150)
                    //    }, 100);

                    //    //$("#" + pageid).append('<iframe  id="finebiview" width="100%" style="margin:0px" frameborder=0/>');
                    //    //setTimeout(() => {
                    //    //    this.populateIframe(url, [["Authorization", "Bearer " + Consts.ssoToken]]);
                    //    //}, 0);
                    //    //var iframe = $("#" + that.Model.viewModel.pageId).find("#finebiview");
                    //    //setInterval(function () {
                    //    //    iframe.attr('height', window.innerHeight - 150)
                    //    //}, 100);
                    //}
                }
            );
        }

        _child.prototype.populateIframe = function (url, headers) {
            var xhr = new XMLHttpRequest();
            document.cookie = "fine_auth_token=" + Consts.ssoToken + "; path=/";
            xhr.open("GET", url, true);
            xhr.responseType = "text";
            xhr.setRequestHeader("Access-Control-Allow-Origin", "*"); // 允许任何源
            xhr.withCredentials = true;
            headers.forEach((header) => {
                xhr.setRequestHeader(header[0], header[1]);
            });
            xhr.send();
            xhr.onreadystatechange = () => {
                if (xhr.readyState === xhr.DONE) {
                    if (xhr.status === 200) {
                        let iframe = document.getElementById('finebiview');
                        iframe = iframe.contentWindow || (iframe.contentDocument.document || iframe.contentDocument);
                        iframe.document.open();
                        iframe.document.write(xhr.response);
                        iframe.document.close();
                    }
                }
            };
        };

        _child.prototype.openIFrame = function (encryptData, pageid) {
            var that = this;
            that.Model.unblockUI({ id: '#page#' });
            if (encryptData.returnUrl) {
                var url = encryptData.returnUrl + "&fmainorgid=" + Consts.loginCompany.id + "&ssoToken=" + Consts.ssoToken;

                $("#" + pageid).append('<iframe  id="finebiview' + pageid + '" src="' + url + '" width="100%" style="margin:0px" frameborder=0></iframe>');
                var iframe = $("#" + that.Model.viewModel.pageId).find("#finebiview" + pageid);
                setInterval(function () {
                    iframe.attr('height', window.innerHeight - 150)
                }, 100);
            }
        }

        _child.prototype.openNewTab = function (encryptData, pageid) {
            var that = this;
            that.Model.unblockUI({ id: '#page#' });
            if(encryptData.returnUrl){
                var url = encryptData.returnUrl + "&fmainorgid=" + Consts.loginCompany.id + "&ssoToken=" + Consts.ssoToken;
                window.open(url);
                that.Model.close();
            }
        }

        return _child;
    })(BasePlugIn);
    window.bi_categorymapping = window.bi_categorymapping || bi_categorymapping;
})();

