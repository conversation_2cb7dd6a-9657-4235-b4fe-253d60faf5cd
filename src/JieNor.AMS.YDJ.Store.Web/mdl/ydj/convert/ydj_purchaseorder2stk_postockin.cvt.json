{
  "Id": "ydj_purchaseorder2stk_postockin",
  "Number": "ydj_purchaseorder2stk_postockin",
  "Name": "采购订单生成采购入库单",
  "SourceFormId": "ydj_purchaseorder",
  "TargetFormId": "stk_postockin",
  "ActiveEntityKey": "fentity",
  "ControlFieldKey": "fqty",
  "SourceControlFieldKey": "fbizqty",
  "FilterString": "fqty+freturnqty>finstockqty and fstatus='E' and fenablenotice='0' and fcooeditstatus<>'3' and fcooeditstatus<>'4' and fclosestatus_e not in ('3','4') ",
  "RealtionFormIdFieldKey": "fsourceformid",
  "RelationFieldKey": "fsourceentryid",
  "Message": "入库失败：<br>1、采购订单必须是已审核状态！<br>2、至少要有一行商品明细没有完全入库(数量-入库数量+退货数量>0)！<br>3、启用收货通知时不能直接入库！<br>4、供方状态不可为删除！",
  "FieldMappings": [
    {
      "Id": "fstorehouseid",
      "Name": "仓库",
      "MapType": 1,
      "SrcFieldId": "''",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationid",
      "Name": "仓位",
      "MapType": 1,
      "SrcFieldId": "''",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'poinstock_billtype_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "入库日期",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffid",
      "Name": "收货人",
      "MapType": 1,
      "SrcFieldId": "@currentStaffId",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptid",
      "Name": "收货部门",
      "MapType": 1,
      "SrcFieldId": "@currentDeptId",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsupplierid",
      "Name": "供应商",
      "MapType": 0,
      "SrcFieldId": "fsupplierid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpostaffid",
      "Name": "采购员",
      "MapType": 0,
      "SrcFieldId": "fpostaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpodeptid",
      "Name": "采购部门",
      "MapType": 0,
      "SrcFieldId": "fpodeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsupplieraddr",
      "Name": "供方地址",
      "MapType": 0,
      "SrcFieldId": "fsupplieraddr",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_purchaseorder'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //表体商品明细字段映射
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //表体商品明细字段映射
    {
      "Id": "fresultbrandid",
      "Name": "业绩品牌",
      "MapType": 0,
      "SrcFieldId": "fresultbrandid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "采购单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fentrystaffid",
      "Name": "销售员",
      "MapType": 0,
      "SrcFieldId": "fentrystaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 1,
      "SrcFieldId": "fmaterialid.fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位应收数量",
      "MapType": 1,
      "SrcFieldId": "fqty-finstockqty+freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fqty",
      "Name": "基本单位实收数量",
      "MapType": 1,
      "SrcFieldId": "fqty-finstockqty+freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderqty",
      "Name": "采购订单数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "成交单价",
      "MapType": 1,
      "SrcFieldId": "fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "famount",
      "Name": "成交金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-finstockqty+freturnqty)*fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fpoprice",
      "Name": "采购单价",
      "MapType": 1,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoamount",
      "Name": "金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-finstockqty+freturnqty)*fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatus",
      "Name": "库存状态",
      "MapType": 1,
      "SrcFieldId": "'311858936800219137'",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fnote",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_purchaseorder'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoorderno",
      "Name": "采购订单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoorderinterid",
      "Name": "采购订单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoorderentryid",
      "Name": "采购订单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsoorderno",
      "Name": "销售合同编号",
      "MapType": 0,
      "SrcFieldId": "fsoorderno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsoorderinterid",
      "Name": "销售合同内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderinterid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsoorderentryid",
      "Name": "销售合同分录内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderentryid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomer",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomer",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fhqderno",
      "Name": "总部销售合同号",
      "MapType": 0,
      "SrcFieldId": "fhqderno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsupplierorderno",
      "Name": "供货方订单号",
      "MapType": 0,
      "SrcFieldId": "fsupplierorderno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "frenewalflag",
      "Name": "焕新订单标记",
      "MapType": 0,
      "SrcFieldId": "frenewalflag",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpiecesendtag",
      "Name": "一件代发标记",
      "MapType": 0,
      "SrcFieldId": "fpiecesendtag",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freceptionid",
      "Name": "收货扫描任务内码",
      "MapType": 1,
      "SrcFieldId": "''",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freceptionentryid",
      "Name": "收货扫描任务明细内码",
      "MapType": 1,
      "SrcFieldId": "''",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}