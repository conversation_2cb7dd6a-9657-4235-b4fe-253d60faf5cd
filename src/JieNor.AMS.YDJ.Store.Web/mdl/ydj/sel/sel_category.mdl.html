<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="sel_category" el="3" basemodel="bd_basetmpl" cn="选配类别" isolate="0" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_sel_category" pn="fbillhead" cn="选配类别">

        <!--重写基类模型中的部分字段属性-->
        <input el="100" id="fdescription" cn="备注" visible="0" />

        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fseltypeid" fn="fseltypeid" pn="fseltypeid" cn="型号" refid="sel_type" lix="20" />
        <input group="基本信息" el="100" ek="fbillhead" id="fattribute" fn="fattribute" pn="fattribute" cn="辅助属性" visible="-1" lix="25" width="200" len="2000" sbm="true" lock="-1"/>

        <table id="fentity" el="52" pk="fentryid" tn="t_sel_categoryentry" pn="fentity" cn="明细信息" kfks="fpropid">
            <tr>
                <th el="106" ek="fentity" visible="-1" id="fpropid" fn="fpropid" pn="fpropid" cn="属性" width="200" refid="sel_prop" lix="30" must="1"></th>
                <th el="106" ek="fentity" id="fdefaultpropvalueid" fn="fdefaultpropvalueid" pn="fdefaultpropvalueid" cn="默认值" refid="sel_propvalue" width="240" visible="-1" lix="31" lock="0" esp="false" ffks="fnumber,fname"></th>
                <th el="116" ek="fentity" id="fiswhetherinforce" fn="fiswhetherinforce" pn="fiswhetherinforce" width="100" cn="是否生效" visible="-1" lix="35"></th>
                <th el="116" ek="fentity" id="fiscontrolmust" fn="fiscontrolmust" pn="fiscontrolmust" width="100" cn="是否必录" visible="-1" lix="40"></th>
                <th el="101" ek="fentity" id="fdisplayseq" fn="fdisplayseq" pn="fdisplayseq" cn="生成顺序" width="100" visible="-1" lix="50" defval="1"></th>
            </tr>
        </table>
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" clear></li>
            <li el="11" id="save_valid_fnumber" cn="保存时编码唯一" vid="500" data="fmainorgid,fnumber" precon=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="forbid" op="forbid" opn="禁用">
            <li el="11" id="forbid_valid_ref" cn="已经被引用，不允许禁用" vid="514"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fname','fnumber','fseltypeid','fpropid','fiswhetherinforce','fiscontrolmust','fdefaultpropvalueid','fdisplayseq']}" permid=""></ul>
        <ul el="10" ek="fbillhead" id="UpdateAuxPropValueSet" op="UpdateAuxPropValueSet" opn="更新辅助属性顺序" data="" permid="ydj_updateauxpropvalueset"></ul>
    </div>
    <div id="permList">
        <ul el="12" id="ydj_updateauxpropvalueset" cn="更新辅助属性顺序" order="50"></ul>
    </div>
</body>
</html>