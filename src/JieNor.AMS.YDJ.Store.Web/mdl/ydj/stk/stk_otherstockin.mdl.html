<!--
其它入库单

-->

<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_otherstockin" basemodel="base_stkbilltmpl" el="1" cn="其它入库单" ubl="1">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_otherstockin" pn="fbillhead" cn="其它入库单">

        <!--修改基类字段名-->
        <input group="基本信息" el="112" id="fdate" cn="入库日期" visible="-1" lix="5" />
        <input lix="1" group="基本信息" el="112" id="fplandate" fn="fplandate" cn="计划入库日期" defval="@currentshortdate" visible="-1"  uaul="true" />
        <input group="基本信息" el="106" id="fstockstaffid" cn="收货人" visible="-1" lix="40" must="1" defVal="@currentStaffId" />

        <input group="基本信息" el="106" ek="fbillhead" id="fstockdeptid" fn="fstockdeptid" pn="fstockdeptid" cn="收货部门" visible="-1" refid="ydj_dept" lock="0" lix="30" must="1" />

        <input group="基本信息" el="106" ek="fbillhead" id="fdeptid" fn="fdeptid" pn="fdeptid" visible="-1" cn="部门"
               lock="0" copy="1" lix="50" notrace="true" ts="" refid="ydj_dept" filter="" reflvt="0" dfld="" must="1" />
        <input group="基本信息" el="107" ek="fbillhead" id="fdeptnumber" fn="fdeptnumber" pn="fdeptnumber" cn="部门编码" visible="1150" ctlfk="fdeptid" dispfk="fnumber" />
        <input group="基本信息" el="106" ek="fbillhead" id="fsupplierid" fn="fsupplierid" pn="fsupplierid" cn="供应商" visible="-1" refid="ydj_supplier" lix="20" />

        <input group="物流信息" el="106" ek="fbillhead" id="fcarrierid" fn="fcarrierid" pn="fcarrierid" cn="承运公司" visible="1150" refid="ydj_supplier" filter="ftype='suppliertype_03'" lix="1000" />

        <input group="物流信息" el="100" ek="fbillhead" id="fdeliverybillno" fn="fdeliverybillno" pn="fdeliverybillno" cn="物流单号" visible="1150" copy="0" lix="1010" />
        <input group="物流信息" el="111" ek="FBillHead" id="fdeliveryvoucher" fn="fdeliveryvoucher" ts="" cn="物流凭据" visible="1150" copy="0" lix="1020" />
        <input group="基本信息" el="122" ek="fbillhead" visible="-1" id="ftype" fn="ftype" pn="ftype" refId="bd_enum" dfld="fenumitem" ts="" cg="入库类型" defval="''" cn="入库类型" lix="14" />

        <input group="基本信息" el="100" ek="fbillhead" id="fdeliveryman" fn="fdeliveryman" pn="fdeliveryman" cn="配送员" visible="1150" />
        <input type="text" id="fcreatorid" el="118" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fcreatorid" pn="fcreatorid" cn="创建人" visible="-1" copy="0" xlsin="0" lix="2" />
        <input type="datetime" id="fcreatedate" el="119" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" cn="创建日期" width="130" visible="-1" copy="0" xlsin="0" lix="3" />
        <input group="基本信息" el="116" ek="fbillhead" id="fispdatask" fn="fispdatask" pn="fispdatask" visible="1150" cn="是否PDA作业" lock="-1" copy="0" lix="70" notrace="true" ts="" />

        <input group="基本信息" el="116" ek="fbillhead" id="fcreatescantask" fn="fcreatescantask" pn="fcreatescantask" visible="1150" cn="已生成PDA扫描任务" lock="-1" copy="0" lix="70" notrace="true" ts="" />
    </div>

    <!--入库明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_stk_otherstockinentry" pn="fentity" cn="入库明细" kfks="fmaterialid,fqty">
        <tr>
            <!--修改基类字段属性-->
            <!--以下字段暂时锁定-->
            <th el="100" ek="fentity" id="flotno" cn="批号" lock="-1" must="0"></th>
            <th el="100" ek="fentity" id="fmtono" cn="物流跟踪号" lock="0" must="0"></th>
            <th el="149" ek="fentity" id="fownertype" cn="货主类型" lock="0" must="0">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th lix="40" el="132" ek="fentity" id="fattrinfo" cn="辅助属性" nstdfk="funstdtype" visible="1150" lock="0" copy="0" width="120"></th>
            <th el="150" ek="fentity" id="fownerid" cn="货主" lock="0" must="0"></th>

            <th el="109" ek="fentity" id="fbizunitid" cn="入库单位"></th>

            <th el="103" ek="fentity" id="fplanqty" fn="fplanqty" cn="基本单位应收数量"></th>
            <th el="103" ek="fentity" id="fbizplanqty" fn="fbizplanqty" cn="应收数量"></th>

            <th el="103" ek="fentity" id="fqty" cn="基本单位实收数量" lock="-1"></th>
            <th el="103" ek="fentity" id="fbizqty" cn="实收数量" must="1"></th>
            <th el="103" ek="fentity" id="frecpackqty" fn="frecpackqty" pn="frecpackqty" cn="实收包数" visible="1150" lock="-1" copy="0"></th>

            <!--慕思新增字段，标准定制非标定制-->
            <th lix="270" el="116" ek="fentity" visible="1150" id="funstdtype" fn="funstdtype" pn="funstdtype" cn="是否非标" ctlfk="fmaterialid" dispfk="funstdtype" width="90" copy="0" lock="0" canchange="true" refValueType="116"></th>
            <th lix="290" el="100" ek="fentity" visible="1150" id="fpartscombnumber" fn="fpartscombnumber" pn="fpartscombnumber" cn="配件组合号" lock="-1"></th>
            <th lix="300" el="107" ek="fentity" visible="1150" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" cn="选配类别" ctlfk="fmaterialid" dispfk="fselcategoryid" lock="-1"></th>
            <th lix="310" el="107" ek="fentity" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="0" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>
            <th lix="320" el="116" ek="fentity" id="fdostandard" fn="fdostandard" pn="fdostandard" cn="已转标准品" visible="1150" lock="-1" defval="false"></th>


            <th el="104" ek="fentity" id="fprice" cn="单价" lock="0" notrace="false"></th>
            <th el="105" ek="fentity" id="famount" cn="金额" lock="0" notrace="false"></th>
            <th group="入库明细" el="104" ek="fentity" id="fcostprice" cn="单位成本(加权平均)" lock="0"></th>
            <th group="入库明细" el="105" ek="fentity" id="fcostamt" cn="成本(加权平均)" lock="0"></th>

            <th lix="800" el="102" ek="fentity" visible="1150" id="fsumpushreceiveqty" fn="fsumpushreceiveqty" pn="fsumpushreceiveqty" cn="累计下推收货作业数量" lock="-1" copy="0" />

            <th el="100" lix="299" ek="fentity" id="fvolumeunit" fn="fvolumeunit" pn="fvolumeunit" visible="1086" cn="体积单位" lock="-1" must="0" uaul="true"></th>
            <th el="102" lix="300" ek="fentity" id="ftotalvolume" fn="ftotalvolume" pn="ftotalvolume" visible="1086" cn="总体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
            <th el="102" lix="301" ek="fentity" id="fsinglevolume" fn="fsinglevolume" pn="fsinglevolume" visible="1086" cn="单位体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" cn="部门和供应商不能同时为空" data="{'expr':'(fdeptid!=\'\' and fdeptid!=\' \') or (fsupplierid!=\'\' and fsupplierid!=\' \')','message':'部门和供应商不能同时为空！'}"></li>
            <!--<li el="11" vid="3008" cn="允许选配的商品明细的辅助属性不能为空" data="{'productFieldKey':'fmaterialid'}"></li>-->

            <li el="17" sid="1002" cn="反写售后维修单其它入库数量" data="{
                'sourceFormId':'aft_repairorder',
                'sourceControlFieldKey':'fbizinstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fsourcetype=\'aft_repairorder\'',
                'writebackFieldKey':'fbizinstockqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'fbizinstockqty&gt;fbizqty',
                'excessMessage':'其他入库库数量不允许超过维修数量！'
                }"></li>
        </ul>
        <ul el="10" id="submit" op="submit" opn="提交">
            <li el="11" vid="510" cn="部门和供应商不能同时为空" data="{'expr':'(fdeptid!=\'\' and fdeptid!=\' \') or (fsupplierid!=\'\' and fsupplierid!=\' \')','message':'部门和供应商不能同时为空！'}"></li>
        </ul>
        <ul el="10" id="audit" op="audit" opn="审核">
            <li el="11" vid="510" cn="部门和供应商不能同时为空" data="{'expr':'(fdeptid!=\'\' and fdeptid!=\' \') or (fsupplierid!=\'\' and fsupplierid!=\' \')','message':'部门和供应商不能同时为空！'}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="17" sid="2000" cn="审核时更新库存" data="{'factor':1,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写其它入库申请单入库数量" data="{
                'sourceFormId':'stk_otherstockinreq',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'finstockqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'finstockqty&gt;fqty',
                'excessMessage':'其它入库数量不允许超过入库申请数量！'
                }"></li>

        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="17" sid="2000" cn="反审核时更新库存" data="{'factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus!=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写其它入库申请单入库数量" data="{
                'sourceFormId':'stk_otherstockinreq',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'finstockqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'finstockqty&gt;fqty',
                'excessMessage':'其它入库数量不允许超过入库申请数量！'
                }"></li>
            <li el="11" vid="3015" cn="反审核增加校验如果对应的《收货扫描任务》单据头【任务状态】=”已完成”时, 不允许反审核" data=""></li>
        </ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="17" sid="2000" cn="删除时更新库存" data="{'factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus!=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写其它入库申请单入库数量" data="{
                'sourceFormId':'stk_otherstockinreq',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'finstockqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'finstockqty&gt;fqty',
                'excessMessage':'其它入库数量不允许超过入库申请数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写售后维修单其它入库数量为0" data="{
                'sourceFormId':'aft_repairorder',
                'sourceControlFieldKey':'fbizinstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fsourcetype=\'aft_repairorder\'',
                'writebackFieldKey':'fbizinstockqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>

        <ul el="10" ek="fentity" id="queryinventory" op="queryinventory" opn="库存查询" permid="fw_queryinventory"
            data="{
                'parameter':{
                    'fieldMaps':{'fcostprice':'fprice'}
                }
            }"></ul>
    </div>

</body>
</html>

