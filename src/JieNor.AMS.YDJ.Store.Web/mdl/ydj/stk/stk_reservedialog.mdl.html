<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_reservedialog" el="0" cn="预留设置">

    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="基本信息">
        <div el="149" ek="fbillhead" id="freserveobjecttype" pn="freserveobjecttype" cn="预留对象类型" dataviewname="v_bd_reserveobject" visible="-1" lock="-1" defVal="'ydj_customer'">
            <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
            <dataSourceDesc formId="ydj_staff" filter="" caption="员工"></dataSourceDesc>
            <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
        </div>
        <input el="150" ek="fbillhead" id="freserveobjectid" pn="freserveobjectid" ctlfk="freserveobjecttype" cn="预留对象" visible="-1" lock="-1" />
        <input el="100" ek="fbillhead" id="fdescription" fn="fdescription" cn="预留说明" visible="-1" />

        <!--预留需求方信息-->
        <input el="140" ek="fbillhead" id="fdemandformid" pn="fdemandformid" cn="需求单类型" visible="-1" lock="-1" />
        <input el="141" ek="fbillhead" id="fdemandbillno" pn="fdemandbillno" cn="需求单编号" visible="-1" lock="-1" />
        <input el="141" ek="fbillhead" id="fdemandbillpkid" pn="fdemandbillpkid" cn="需求单ID" visible="0" lock="-1" />

        <select el="122" id="fsourcestatus" ek="fbillhead" fn="fsourcestatus" pn="fsourcestatus" cn="源单状态" cg="数据状态" refid="bd_enum" dfld="fenumitem" lix="50" lock="-1" visible="96"></select>

        <input el="101" ek="fbillhead" id="freserveday" cn="销售意向预留日期控制" desc="销售意向预留X天后，如未成交则自动释放" lock="-1" />

        <input el="112" ek="fbillhead" id="fdefautdate" pn="fdefautdate" cn="默认预留日期至" width="100" visible="0" lock="-1" />

        <input el="100" ek="fbillhead" id="freservepkid" pn="freservepkid" cn="对应的预留单PKID" visible="0" lock="-1" />

        <input el="116" ek="fbillhead" id="fcancelstatus" pn="fcancelstatus" cn="需求单作废状态" visible="0" lock="-1" />

        <input el="100" ek="fbillhead" id="feditrows" pn="feditrows" cn="当前选中的需求单的明细行信息" visible="0" lock="-1" />

        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid" fn="fstaffid" pn="fstaffid" cn="销售员" refid="ydj_staff" defVal="@currentStaffId" apipn="saleMan" dfld="fname" canchange="true" lix="21" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="门店名称" refid="ydj_dept" reflvt="1" defVal="@currentDeptId" apipn="saleDept" canchange="true" lix="22" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="faddress" fn="faddress" pn="faddress" cn="客户地址" apipn="address" canchange="true" lix="23" sbm="true" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fmanualnumber" fn="fmanualnumber" pn="fmanualnumber" cn="手工单号" width="150" lix="24" />
        <input group="基本信息" el="100" ek="FBillHead" visible="-1" id="fsourcemark" fn="fsourcemark" pn="fsourcemark" cn="源单备注" copy="1" lix="25" notrace="true" ts="" canchange="true" sbm="true" />

    </div>

    <table id="fentry" el="52" pk="fentryid" pn="fentry" cn="需求明细">
        <tr>
            <th el="107" ek="fentry" id="fmtrlnumber" pn="fmtrlnumber" cn="商品编码" ctlfk="fmaterialid" dispfk="fnumber" width="100" visible="-1" lock="-1"></th>
            <th el="106" ek="fentry" id="fmaterialid" pn="fmaterialid" cn="商品" refid="ydj_product" dfld="fname,fspecifica" sformid="" width="120" visible="-1" lock="-1"></th>
            <th el="107" ek="fentry" id="fmtrlmodel" pn="fmtrlmodel" cn="规格型号" ctlfk="fmaterialid" dispfk="fspecifica" width="120" visible="-1" lock="-1"></th>
            <th el="106" ek="fentry" id="fattrinfo_e" pn="fattrinfo_e" cn="辅助属性" refid="bd_auxpropvalue_ext" visible="-1" lock="-1"></th>
            <th el="132" ek="fentry" id="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid" width="120" visible="0" lock="-1"></th>
            <th el="100" ek="fentry" len="2000" id="fcustomdesc" pn="fcustomdesc" cn="定制说明" width="120" visible="-1" lock="-1"></th>
            <th lix="121" el="107" ek="fentry" id="fcustom" fn="fcustom" pn="fcustom" visible="0" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>
            <th el="106" ek="fentry" id="fresultbrandid" pn="fresultbrandid" cn="业绩品牌" refid="ydj_series" visible="-1" lock="-1"></th>
            <th el="109" ek="fentry" id="funitid" pn="funitid" cn="基本单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" width="75" visible="-1" lock="-1"></th>
            <th el="109" ek="fentry" id="fbizunitid" pn="fbizunitid" cn="单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" width="60" visible="-1" lock="-1"></th>

            <th el="103" ek="fentry" id="fbizplanqty" pn="fbizplanqty" cn="需求数量" ctlfk="fbizunitid" basqtyfk="fplanqty" width="75" visible="-1" lock="-1"></th>
            <th el="103" ek="fentry" id="fbizqty" pn="fbizqty" cn="预留量" ctlfk="fbizunitid" basqtyfk="fqty" width="75" visible="-1" lock="-1"></th>
            <th el="112" ek="fentry" id="freservedateto" pn="freservedateto" cn="预留日期至" width="100" visible="0" lock="-1"></th>

            <th el="103" ek="fentry" id="fbizcanreserveqty" pn="fbizcanreserveqty" cn="可预留量" ctlfk="fbizunitid" basqtyfk="fcanreserveqty" width="75" visible="-1" lock="-1"></th>
            <th el="103" ek="fentry" id="fbizinventoryqty" pn="fbizinventoryqty" cn="库存数量" ctlfk="fbizunitid" basqtyfk="finventoryqty" width="75" visible="-1" lock="-1"></th>

            <th el="100" ek="fentry" id="fmtono" pn="fmtono" cn="物流跟踪号" width="125" visible="-1" lock="-1"></th>

            <th el="103" ek="fentry" id="fqty" pn="fqty" cn="基本单位预留量" ctlfk="funitid" width="115" visible="-1" lock="-1"></th>
            <th el="103" ek="fentry" id="fcanreserveqty" pn="fcanreserveqty" cn="基本单位可预留量" ctlfk="funitid" width="125" visible="-1" lock="-1"></th>
            <th el="103" ek="fentry" id="finventoryqty" pn="finventoryqty" cn="基本单位库存数量" ctlfk="funitid" width="125" visible="-1" lock="-1"></th>
            <th el="103" ek="fentry" id="fplanqty" pn="fplanqty" cn="基本单位需求数量" ctlfk="funitid" width="125" visible="-1" lock="-1"></th>


            <th el="100" ek="fentry" id="fdemandentryid" pn="fdemandentryid" cn="对应的需求单明细内码" visible="0"></th>

            <th el="100" ek="fentry" id="freserveentryid" pn="freserveentryid" cn="对应的预留单的行PKID" visible="0" lock="-1" />

            <!--行关闭标记，用于控制是否可以编辑预留数据-->
            <th el="100" ek="fentry" id="fclosestatus" pn="fclosestatus" cn="行关闭状态" visible="0" lock="-1" lix="300" />
        </tr>
    </table>
     
    <table id="fdetail" el="53" pek="fentry" pk="fdetailid" tn="" pn="fdetail" cn="预留跟踪">
        <tr>
            <th el="106" ek="fdetail" id="fstorehouseid" cn="仓库" refid="ydj_storehouse" sformid="" visible="-1" width="100"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <!-- <th el="153" ek="fdetail" id="fstorelocationid" cn="仓位" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="-1" width="100"></th> -->
            <th el="106" ek="fdetail" id="fstockstatus" pn="fstockstatus" cn="库存状态" refid="ydj_stockstatus" defVal="'311858936800219137'" sformid="" dfld="fcolor" width="80" visible="-1"></th>

            <th el="103" ek="fdetail" id="fbizqty_d" pn="fbizqty_d" cn="预留量" ctlfk="fbizunitid_d" basqtyfk="fqty_d" width="75" visible="-1"></th>
            <th el="103" ek="fdetail" id="fqty_d" pn="fqty_d" cn="基本单位预留量" ctlfk="funitid_d" width="115" visible="-1" lock="-1"></th>
            <th el="112" ek="fdetail" id="freservedateto_d" pn="freservedateto_d" cn="预留日期至" width="100" visible="-1"></th>
            <th el="152" ek="fdetail" id="fdirection_d" pn="fdirection_d" visible="1150" cn="预留方向" width="75"
                lock="-1" copy="0" lix="170" notrace="true" ts="" vals="'0':'增','1':'减'" defval="'0'"></th>

            <th el="103" ek="fdetail" id="fbizcanreserveqty_d" pn="fbizcanreserveqty_d" cn="可预留量" ctlfk="fbizunitid_d" basqtyfk="fcanreserveqty_d" width="75" visible="-1" lock="-1"></th>
            <th el="103" ek="fdetail" id="fbizinventoryqty_d" pn="fbizinventoryqty_d" cn="库存数量" ctlfk="fbizunitid_d" basqtyfk="finventoryqty_d" width="75" visible="-1" lock="-1"></th>
            <th el="103" ek="fdetail" id="fcanreserveqty_d" pn="fcanreserveqty_d" cn="基本单位可预留量" ctlfk="funitid_d" width="125" visible="-1" lock="-1"></th>
            <th el="103" ek="fdetail" id="finventoryqty_d" pn="finventoryqty_d" cn="基本单位库存数量" ctlfk="funitid_d" width="125" visible="-1" lock="-1"></th>

            <th el="152" ek="fdetail" id="fopdesc" pn="fopdesc" visible="1150" cn="预留操作" width="75" lock="-1" copy="0" lix="170" notrace="true" ts="" defval="'0'"
                vals="'0':'增加预留','1':'减少预留','2':'手工释放','3':'预留转移','4':'单据关闭释放','5':'单据作废释放','6':'自动释放'"></th>

            <th el="100" ek="fdetail" id="freservenote" pn="freservenote" cn="预留操作说明" width="125" visible="-1"></th>
            <th el="113" ek="fdetail" id="foptime" pn="foptime" cn="操作日期" defval="@currentlongdate" lix="26" visible="-1" format="yyyy-MM-dd HH:mm:ss" lock="-1" />
            <th el="118" ek="fdetail" id="fopuserid" refId="Sec_User" dfld="FName" fn="fopuserid" pn="fopuserid" cn="操作人" visible="-1" copy="0" lock="-1" xlsin="0" lix="250" />

            <th el="106" ek="fdetail" id="fmaterialid_d" fn="fmaterialid_d" pn="fmaterialid_d" visible="0" cn="商品" lock="-1" copy="1" lix="10" notrace="true" ts="" refid="ydj_product" reflvt="0"></th>
            <th el="109" ek="fdetail" id="fbizunitid_d" fn="fbizunitid_d" cn="单位" lix="60" ctlfk="fmaterialid_d" refid="ydj_unit" sformid="" visible="0" width="80"></th>
            <th el="109" ek="fdetail" id="funitid_d" fn="funitid_d" cn="基本单位" lix="110" ctlfk="fmaterialid_d" refid="ydj_unit" sformid="" visible="0" width="80"></th>

            <th el="100" ek="fdetail" id="freservetrancepkid" pn="freservetrancepkid" cn="对应的预留单的预留跟踪行PKID" visible="0" lock="-1" />

            <!--预留供方来源信息，为空就是来源即时库存-->
            <th el="140" ek="fdetail" id="ffromformid" pn="ffromformid" cn="预留来源对象" visible="0" lock="-1" />
            <th el="141" ek="fdetail" id="ffrombillno" pn="ffrombillno" cn="来源单编号" ctlfk="ffromformid" visible="0" lock="-1" />
            <th el="141" ek="fdetail" id="ffrombillpkid" pn="ffrombillpkid" cn="来源单ID" ctlfk="ffromformid" visible="0" lock="-1" />

            <th el="100" ek="fdetail" id="ftemptranceid" pn="ftemptranceid" cn="临时id" visible="0" lock="-1" />

            <!--行关闭标记，用于控制是否可以编辑预留数据-->
            <th el="100" ek="fdetail" id="fclosestatus_d" pn="fclosestatus_d" cn="行关闭状态" visible="0" lock="-1" />
            <th el="100" ek="fdetail" id="freservedateto_old" pn="freservedateto_old" cn="旧预留日期" visible="0" lock="-1" />

        </tr>
    </table>

    <div id="opList">
        <ul el="10" ek="fentry" id="queryinventory" op="queryinventory" opn="库存查询" permid=""
            data="{}"></ul>
    </div>
</body>
</html>