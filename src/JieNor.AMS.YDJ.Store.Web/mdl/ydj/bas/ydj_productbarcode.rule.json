{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",

  //定义表单锁定规则
  "lockRules": [
    
  ],

  //定义表单可见性规则
  "visibleRules": [
    {
      "id": "hide_productid",
      "expression": "other:.cproductid,.cimage$|fbillformid=='ydj_suit'"
    },
    {
      "id": "show_productid",
      "expression": "other:$.cproductid,.cimage|fbillformid=='ydj_product'"
    },
    {
      "id": "hide_suitid",
      "expression": "other:.csuitid$|fbillformid=='ydj_product'"
    },
    {
      "id": "show_suitid",
      "expression": "other:$.csuitid|fbillformid=='ydj_suit'"
    }
  ],

  //定义表单计算规则
  "calcRules": [
    //选择商品时，携带出相关信息
    { "expression": "funitid=fproductid__funitid" },
    { "expression": "foriginplace=fproductid__foriginplace" },
    { "expression": "fbrandid=fproductid__fbrandid" }
  ]
} 