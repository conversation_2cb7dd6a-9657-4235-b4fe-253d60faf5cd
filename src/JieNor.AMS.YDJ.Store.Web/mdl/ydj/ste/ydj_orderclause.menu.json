{"base": "/mdl/bill.menu.json", "common": [{"id": "tbNew", "visible": "false"}, {"id": "tbCopy", "visible": "false"}, {"id": "tbSubmit", "visible": "true"}, {"id": "tbUnsubmit", "visible": "true"}, {"id": "tb<PERSON><PERSON><PERSON>", "visible": "true"}, {"id": "tb<PERSON>na<PERSON>t", "visible": "true"}, {"id": "tbCancel", "visible": "false"}, {"id": "tbUncancel", "visible": "false"}, {"id": "tbLookauditlog", "visible": "false"}], "listmenu": [{"id": "tbDelete", "visible": "true"}], "billmenu": [{"id": "tbPull", "visible": "false"}, {"id": "tbCancel", "visible": "false"}]}