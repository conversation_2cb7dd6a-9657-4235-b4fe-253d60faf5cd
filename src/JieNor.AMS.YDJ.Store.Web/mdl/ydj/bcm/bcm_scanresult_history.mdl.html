<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    扫描记录
    此业务数据不支持手工新增，只能由备码或收货打包环节生成此数据，生成这个数据后，还要支持下一阶段的扫码确认。
    扫码确认后，并提交，将会生成一个从通知类生成出入库单据的处理任务，表示
-->
<html lang="en">
<head>
</head>
<body id="bcm_scanresult_history" basemodel="bcm_scanresult" el="1" cn="历史条码扫描记录" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bcm_scanresult_history" pn="fbillhead" cn="历史条码扫描记录">
        <input group="基本信息" el="106" ek="FBillHead" id="fbarcode" fn="fbarcode" pn="fbarcode" visible="-1" cn="条码"
               lock="-1" copy="0" lix="1" notrace="true" ts="" refid="bcm_barcodemaster_history" filter="" reflvt="0" dfld="" defls="true" />
    </div>

    <!--商品明细(取条码主档商品明细相关数据)---->
    <table id="fentity" el="52" pk="fentryid" tn="t_bcm_scanresultproductentry_history" pn="fentity" cn="商品明细">
        <tr>

        </tr>
    </table>

    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="">
        </ul>
    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">

    </div>
</body>
</html>