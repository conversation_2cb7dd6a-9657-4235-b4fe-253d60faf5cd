{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "fpublishstatus_0",
      "expression": "field:$*;menu:tbUnpublish$tbPublish|fpublishstatus=='0'"
    },
    {
      "id": "fpublishstatus_1",
      "expression": "field:*$fenddate;menu:tbPublish$tbUnpublish|fpublishstatus=='1'"
    },
    //此项规则表示：单据状态=D 时，所有字段可用，反审核 提交 操作不可用，其他操作可用
    {
      "id": "fstatus_D",
      "expression": "field:*$fdescription,fenddate;menu:tbUnaudit,tbSubmit$*|fstatus=='D'"
    },
    //此项规则表示：单据状态=E 时，所有字段不可用，审核 提交 操作不可用，其他操作可用
    {
      "id": "fstatus_E",
      "expression": "field:*$fdescription,fenddate;menu:tbSubmit,tbAudit$*|fstatus=='E'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [
  ],

  //定义表单计算规则
  "calcRules": [
  ]
}