using JieNor.AMS.YDJ.MS.API.DTO.CostCenter;
using JieNor.AMS.YDJ.MS.API.DTO.OpeningBank;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.CostCenter
{
    /// <summary>
    /// 成本中心：同步接口
    /// </summary>
    public class CostCenterController : BaseController<CostCenterDTO>
    {
        protected HtmlForm HtmlForm { get; set; }
        protected string FormId => "ydj_zycostcenter";
        protected override bool IsAsync => false;
        protected override string UniquePrimaryKey => "fnumber";
        protected override string BizObjectFormId => this.FormId;
        protected BaseResponse<MuSiData> ResponseData { get; set; } = new BaseResponse<MuSiData>();

        public override object Execute(CostCenterDTO dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            if (!Valid(dto)) return this.ResponseData;

            this.ResponseData.Code = 200;
            this.ResponseData.Success = true;
            this.ResponseData.Message = "操作成功！";
            this.ResponseData.Data.Flag = MuSiFlag.SUCCESS.ToString();

            var allNumbers = dto.Data.Select(s => s.fnumber);

            try
            {
                Save(dto.Data);
            }
            catch (Exception e)
            {
                this.LogService.Error(e);

                this.ResponseData.Data.ErrorMsgs.Clear();
                this.ResponseData.Data.FailedNumbers.Clear();
                this.ResponseData.Data.SucceedNumbers.Clear();

                this.ResponseData.Success = false;
                this.ResponseData.Data.FailedNumbers.AddRange(allNumbers);
                this.ResponseData.Data.ErrorMsgs.Add(e.Message);
            }

            if (!this.ResponseData.Success)
            {
                this.ResponseData.Code = 400;
                this.ResponseData.Message = string.Join("\r\n", this.ResponseData.Data.ErrorMsgs);
                this.ResponseData.Data.Flag = this.ResponseData.Data.SucceedNumbers.Any()
                    ? MuSiFlag.PARTSUCCESS.ToString()
                    : MuSiFlag.FAIL.ToString();
            }

            this.Request.SetBillNo(MSKey.BillNo, "");
            this.Request.SetBillNo(MSKey.SuccessNumber, this.ResponseData.Data.SucceedNumbers.JoinEx(",", false));
            this.Request.SetBillNo(MSKey.FailNumber, this.ResponseData.Data.FailedNumbers.JoinEx(",", false));

            return this.ResponseData;
        }

        private void Save(List<CostCenterData> datas)
        {
            List<DynamicObject> dataEntities = new List<DynamicObject>();

            var dm = this.Container.GetService<IDataManager>();
            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
            dm.InitDbContext(this.Context, dt);

            var existsBillDatas = GetExistsBillDatas(datas, dm);
            dataEntities.AddRange(existsBillDatas);

            var newDatas = datas.Where(d => !existsBillDatas.Any(s =>
                Convert.ToString(s["fnumber"]).EqualsIgnoreCase(d.fnumber))).ToList();

            var newBillDatas = CreateNewBillDatas(newDatas, dt);
            dataEntities.AddRange(newBillDatas);

            FillData(dataEntities, datas);

            var saveOption = new Dictionary<string, object>
            {
                { "NotRefreshNumber", true },
                { "IsMuSiSync", true },
                { "IgnoreCheckPermssion", true },
                { "IsReturnBillUniqueValidationDetailErrorMessage", true }
            };

            foreach (var grp in dataEntities.GroupBy(s => Convert.ToString(s["forgid"])))
            {
                var number = grp.Key;
                var agentCtx = this.Context.CreateAgentDBContextByNo(number);
                agentCtx = this.Context.CreateTopOrgDBContext();

                var result = this.HttpGateway.InvokeBillOperation(agentCtx, this.HtmlForm.Id, grp.ToList(), "save", saveOption);

                var numbers = grp.Select(s => Convert.ToString(s["fnumber"]));
                if (result.IsSuccess)
                {
                    this.ResponseData.Data.SucceedNumbers.AddRange(numbers);
                }
                else
                {
                    this.ResponseData.Data.FailedNumbers.AddRange(numbers);
                    this.ResponseData.Data.ErrorMsgs.Add(result.ToString());
                    this.ResponseData.Success = false;
                }
            }
        }

        private List<DynamicObject> GetExistsBillDatas(List<CostCenterData> datas, IDataManager dm)
        {
            DataTable dtTemp = new DataTable();
            var flds = "fnumber".SplitKey();
            foreach (var flexKey in flds)
            {
                dtTemp.Columns.Add(flexKey, typeof(string));
            }

            dtTemp.BeginLoadData();
            foreach (var data in datas)
            {
                var values = new List<string>();
                foreach (var flexKey in flds)
                {
                    values.Add(data.fnumber ?? "");
                }
                dtTemp.LoadDataRow(values.ToArray(), true);
            }
            dtTemp.EndLoadData();

            using (var tran = Context.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var dbSvc = this.Container.GetService<IDBService>();
                var tempTable = dbSvc.CreateTempTableWithDataTable(this.Context, dtTemp, 100, flds, null, false);

                string filter = $"exists(select top 1 fnumber from {tempTable} tmp where tmp.fnumber=t0.fnumber)";

                var sql = @"select {0} from {1} t0 with(nolock) where {2} ".Fmt(this.HtmlForm.HeadEntity.PkFieldName, this.HtmlForm.HeadEntity.TableName, filter);
                var reader = this.Context.ExecuteReader(sql, new List<SqlParam>());
                var billDatas = dm.SelectBy(reader, OperateOption.Create(), false).OfType<DynamicObject>().ToList();

                tran.Complete();

                return billDatas;
            }
        }

        private List<DynamicObject> CreateNewBillDatas(List<CostCenterData> datas, DynamicObjectType dt)
        {
            List<DynamicObject> billDatas = new List<DynamicObject>();
            if (datas == null || datas.Count == 0)
            {
                return billDatas;
            }

            foreach (var data in datas)
            {
                var billData = dt.CreateInstance() as DynamicObject;
                billData["fnumber"] = data.fnumber;
                billDatas.Add(billData);
            }

            return billDatas;
        }

        private void FillData(List<DynamicObject> billDatas, List<CostCenterData> datas)
        {
            foreach (var billData in billDatas)
            {
                var fnumber = Convert.ToString(billData["fnumber"]);
                var data = datas.FirstOrDefault(s => (s.fnumber ?? "").EqualsIgnoreCase(fnumber));

                if (data == null)
                {
                    continue;
                }

                billData["fnumber"] = data.fnumber;
                billData["fname"] = data.fname;
                billData["forgid"] = data.forgid;
            }
        }

        private bool Valid(CostCenterDTO dto)
        {
            if (dto.Data == null || dto.Data.Count == 0)
            {
                this.ResponseData.Code = 400;
                this.ResponseData.Message = "参数data不能为空！";
                this.ResponseData.Success = false;
                this.ResponseData.Data.ErrorMsgs.Add(this.ResponseData.Message);
                return false;
            }

            List<string> numbers = new List<string>();
            List<string> errorMsgs = new List<string>();

            foreach (var entry in dto.Data)
            {
                string fnumber = entry.fnumber;
                if (string.IsNullOrWhiteSpace(fnumber))
                {
                    errorMsgs.Add("参数fnumber不能为空！");
                }
                else
                {
                    numbers.Add(fnumber);
                }
            }

            if (errorMsgs.Any())
            {
                this.ResponseData.Code = 400;
                this.ResponseData.Message = string.Join("\r\n", errorMsgs);
                this.ResponseData.Success = false;
                this.ResponseData.Data.ErrorMsgs = errorMsgs;
                this.ResponseData.Data.Flag = MuSiFlag.FAIL.ToString();
                this.ResponseData.Data.FailedNumbers = numbers;
                return false;
            }

            return true;
        }

        protected override Dictionary<string, string> CreateDistributedLocks(CostCenterDTO dto)
        {
            Dictionary<string, string> dicResult = new Dictionary<string, string>();
            foreach (var item in dto.Data)
            {
                var number = item.fnumber;
                if (dicResult.ContainsKey($"DistributedLock:{this.FormId}:{number}"))
                {
                    continue;
                }
                dicResult.Add($"DistributedLock:{this.FormId}:{number}", $"成本中心 {number} 正在锁定中，请稍后再操作！");
            }
            return dicResult;
        }
    }
}