using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO.TransferOrderApply;
using JieNor.AMS.YDJ.MS.API.Filter;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;


namespace JieNor.AMS.YDJ.MS.API.Controller.TransferOrderApply
{
    /// <summary>
    /// 转单申请：审批通过接口
    /// </summary>
    //[RepeatedRequestFilter("ydj_transferorderapply", 30)] //对于高并发此方法不够全面，弃用
    public class TransferOrderApplyAuditController : BaseController<TransferOrderApplyAuditDTO>
    {
        public HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "ydj_transferorderapply"; }
        }

        protected string OperationName
        {
            get { return "审批通过"; }
        }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "billNo";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(TransferOrderApplyAuditDTO dto)
        {
            var resp = new BaseResponse<object>();

            if (!Valid(dto, resp)) return resp;

            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            // 经销商
            string agentId = dto.ReceiverAgentId;
            var billData = new List<Dictionary<string, object>>();
            var agentCtx = this.Context.CreateAgentDBContext(agentId);
            var transferOrderApply = agentCtx.LoadBizDataByNo(this.FormId, "fbillno", new[] { dto.BillNo }).FirstOrDefault();
            var agentNos = new List<string>();
            if (dto.ShipperAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                agentNos.Add(dto.ShipperAgentNo);
            }
            if (dto.TargetAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                agentNos.Add(dto.TargetAgentNo);
            }
            if (agentNos.Count > 0)
            {
                var queryAgentIdByNosSql = @"SELECT fid as agentid,fnumber as agentno FROM dbo.T_BAS_AGENT WHERE fnumber in ('{0}')".Fmt(string.Join("','", agentNos));
                var dm = this.Context.Container.GetService<IDBService>();
                var agentIds = dm.ExecuteDynamicObject(this.Context, queryAgentIdByNosSql).ToList();
                if (agentIds.Count > 0)
                {
                    dto.TargetAgentId = Convert.ToString(agentIds.FirstOrDefault(t => Convert.ToString(t["agentno"]) == dto.TargetAgentNo)?["agentid"]);
                    dto.ShipperAgentId = Convert.ToString(agentIds.FirstOrDefault(t => Convert.ToString(t["agentno"]) == dto.ShipperAgentNo)?["agentid"]);
                }
            }

            if (transferOrderApply != null)
            {
                var fentry = transferOrderApply["fentry"] as DynamicObjectCollection;
                if (fentry == null || fentry.Count == 0)
                {
                    resp.Message = $"接单方经销商【{dto.ReceiverAgentNo}】的转单申请单【{dto.BillNo}】的商品明细行不存在！";
                    resp.Code = 200;
                    resp.Success = true;
                    return resp;
                }

                MapBillData(billData, transferOrderApply, dto, fentry);
            }
            else
            {
                resp.Message = $"转单申请单【{dto.BillNo}】不存在！";
                resp.Code = 200;
                resp.Success = true;
                return resp;
            }

            // 向麦浩系统发送请求
            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(agentCtx, new CommonBillDTO()
            {
                FormId = this.FormId,
                OperationNo = "MSAudit",
                BillData = billData.ToJson()
            });

            var result = response?.OperationResult;
            resp = result.ToResponseModel<object>();

            return resp;
        }

        private static void MapBillData(List<Dictionary<string, object>> billData, DynamicObject transferOrderApply, TransferOrderApplyAuditDTO entry,
            DynamicObjectCollection fentry)
        {
            billData.Add(new Dictionary<string, object>
            {
                {"id", Convert.ToString(transferOrderApply["id"])},
                {"ftransferstatus", entry.TransferStatus},
                {"fpassdate", entry.PassDate},
                {"freceiveragentcontactperson", entry.ReceiverAgentContactPerson},
                {"freceivercontactpersonphone", entry.ReceiverContactPersonPhone},

                {"ftargetagentid", entry.TargetAgentId},
                {"ftargetagentno", entry.TargetAgentNo},
                {"ftargetagentname", entry.TargetAgentName},

                {"fshipperagentid", entry.ShipperAgentId},
                {"fshipperagentno", entry.ShipperAgentNo},
                {"fshipperagentname", entry.ShipperAgentName},

                {"fshipperdeliverid", entry.ShipperDeliverId},
                {"fshipperdeliverno", entry.ShipperDeliverNo},
                {"fshipperdelivername", entry.ShipperDeliverName},

                {"fshipperagentcontactperson", entry.ShipperAgentContactPerson},
                {"fshippercontactpersonphone", entry.ShipperContactPersonPhone},
                {"fdistributionmethod", entry.DistributionMethod},
                {"fcostprice", entry.CostPrice},
                {"fotherfee", entry.OtherFee},
                {"freceiveragentdistribution", entry.ReceiverAgentDistribution},
                {"ftargetagentdistribution", entry.TargetAgentDistribution},
                {"freceiveragentamount", entry.ReceiverAgentAmount},
                {"ftargetagentamount", entry.TargetAgentAmount},
                {
                    "fentry", new List<Dictionary<string, object>>
                    {
                        new Dictionary<string, object>
                        {
                            {"id", fentry.First()["id"]},
                            {"fhqapprover", entry.HQApprover},
                            {"fapprovecomment", entry.ApproveComment}
                        }
                    }
                },
                { "fremark",entry.Remark },
                { "fisoffline",entry.IsOffline },
                { "fapplicantstaffid",entry.Apver },
                { "fapplicantphone",entry.ApverPhone },
                { "fwithin",entry.ManualNo },
                { "fcustomeraddress",entry.CustomerAddress }
            });
        }

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Valid(TransferOrderApplyAuditDTO dto, BaseResponse<object> resp)
        {
            if (dto.BillNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数billNo不能为空！";
                resp.Success = false;

                return false;
            }
            List<string> errorMsgs = new List<string>();

            var agentNos = new List<string> { dto.ShipperAgentNo, dto.TargetAgentNo };
            var agents = this.Context.LoadBizDataByNo("bas_agent", "fnumber", agentNos.Distinct());

            //// 接单方经销商
            //var receiverAgent = agents.FirstOrDefault(s =>
            //    Convert.ToString(s["fnumber"]).EqualsIgnoreCase(dto.ReceiverAgentNo));

            // 获取接单方业务经销商
            var receiverAgentId = this.Container.GetService<IAgentService>().GetBizAgentIdByNo(this.Context, dto.ReceiverAgentNo);
            if (receiverAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add($"接单方经销商【{dto.ReceiverAgentNo}】不存在！"); ;
            }
            else
            {
                var receiverAgent = this.Context.LoadBizDataById("bas_agent", receiverAgentId);
                dto.ReceiverAgentId = Convert.ToString(receiverAgent["id"]);
                dto.ReceiverAgentName = Convert.ToString(receiverAgent["fname"]);
            }

            //线下是没有发货经销商&目标经销商账号
            if (!dto.IsOffline)
            {
                // 发货经销商
                var shipperAgent = agents.FirstOrDefault(s =>
                    Convert.ToString(s["fnumber"]).EqualsIgnoreCase(dto.ShipperAgentNo));
                if (shipperAgent == null)
                {
                    errorMsgs.Add($"发货经销商【{dto.ShipperAgentNo}】不存在！");
                }
                else
                {
                    dto.ShipperAgentId = Convert.ToString(shipperAgent["id"]);
                    dto.ShipperAgentName = dto.ShipperAgentName.IsNullOrEmptyOrWhiteSpace() ? Convert.ToString(shipperAgent["fname"]) : dto.ShipperAgentName;
                }

                // 目标经销商
                var targetAgent = agents.FirstOrDefault(s =>
                    Convert.ToString(s["fnumber"]).EqualsIgnoreCase(dto.TargetAgentNo));
                if (targetAgent == null)
                {
                    errorMsgs.Add($"目标经销商【{dto.TargetAgentNo}】不存在！");
                }
                else
                {
                    dto.TargetAgentId = Convert.ToString(targetAgent["id"]);
                    dto.TargetAgentName = dto.TargetAgentName.IsNullOrEmptyOrWhiteSpace() ? Convert.ToString(targetAgent["fname"]) : dto.TargetAgentName;
                }

                //送达方
                var delivers = this.Context.LoadBizDataByNo("bas_deliver", "fnumber", new List<string>() { dto.ShipperDeliverNo });
                if (delivers == null || delivers.Count == 0)
                {
                    errorMsgs.Add($"发货方送达方【{dto.ShipperDeliverNo}】不存在！");
                }
                else
                {
                    var deliver = delivers.FirstOrDefault();
                    dto.ShipperDeliverId = Convert.ToString(deliver["id"]);
                    dto.ShipperDeliverName = dto.ShipperDeliverName.IsNullOrEmptyOrWhiteSpace() ? Convert.ToString(deliver["fname"]) : dto.ShipperDeliverName;
                }
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;

                return false;
            }

            return true;
        }

        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected override void SetNumbers()
        {
            this.Request.SetBillNo(MSKey.BillNo, (this.Request.Dto as TransferOrderApplyAuditDTO)?.BillNo);
        }

        protected override Dictionary<string, string> CreateDistributedLocks(TransferOrderApplyAuditDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.BillNo}", $"转单申请单 {dto.BillNo} 正在锁定中，请稍后再操作！" }
            };
        }

        #region 注释：批量处理
        ///// <summary>
        ///// 处理请求
        ///// </summary>
        ///// <param name="dto"></param>
        ///// <returns></returns>
        //public object Any(TransferOrderApplyAuditDTO dto)
        //{
        //    base.InitializeOperationContext(dto);

        //    var resp = new BaseResponse<List<SuccessBillData>>
        //    {
        //        Data = new List<SuccessBillData>()
        //    };

        //    if (!Valid(dto, resp)) return resp;

        //    this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

        //    // 分组执行 
        //    foreach (var group in dto.Data.GroupBy(s => s.ReceiverAgentId))
        //    {
        //        // 经销商
        //        string agentNo = group.Key;

        //        var billData = new List<Dictionary<string, object>>();
        //        var billNos = group.Select(s => s.BillNo).Distinct();
        //        var agentCtx = CreateAgentUserContext(agentNo);
        //        var transferOrderApplys = agentCtx.LoadBizDataByNo(this.FormId, "fbillno", billNos);

        //        foreach (var entry in group)
        //        {
        //            var transferOrderApply = transferOrderApplys.FirstOrDefault(s => Convert.ToString(s["fbillno"]).EqualsIgnoreCase(entry.BillNo));
        //            if (transferOrderApply != null)
        //            {
        //                var fentry = transferOrderApply["fentry"] as DynamicObjectCollection;
        //                if (fentry == null || fentry.Count == 0)
        //                {
        //                    resp.ErrorMessages.Add($"接单方经销商【{entry.ReceiverAgentNo}】的转单申请单【{entry.BillNo}】的商品明细行不存在！");
        //                    continue;
        //                }

        //                MapBillData(billData, transferOrderApply, entry, fentry);
        //            }
        //            else
        //            {
        //                resp.ErrorMessages.Add($"转单申请单【{entry.BillNo}】不存在！");
        //                continue;
        //            }
        //        }

        //        Invoke(agentCtx, agentNo, billData, group, resp);
        //    }

        //    if (resp.Data.Any())
        //    {
        //        resp.Success = true;
        //        resp.Code = 200;
        //        resp.Message = resp.ErrorMessages.Any() ? $"【{this.HtmlForm.Caption}】部分{this.OperationName}成功！" : $"【{this.HtmlForm.Caption}】{this.OperationName}成功！";
        //    }
        //    else
        //    {
        //        resp.Success = false;
        //        resp.Code = 500;
        //        resp.Message = $"【{this.HtmlForm.Caption}】{this.OperationName}失败！";
        //    }

        //    return resp;
        //}

        ///// <summary>
        ///// 执行
        ///// </summary>
        ///// <param name="agentCtx"></param>
        ///// <param name="agentNo"></param>
        ///// <param name="billData"></param>
        ///// <param name="entrys"></param>
        ///// <param name="resp"></param>
        //private void Invoke(UserContext agentCtx, string agentNo, List<Dictionary<string, object>> billData, IEnumerable<TransferOrderApplyAuditDTO.TransferOrderApplyAuditEntry> entrys, BaseResponse<List<SuccessBillData>> resp)
        //{
        //    if (billData == null || billData.Count == 0) return;

        //    // 向麦浩系统发送请求
        //    var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(agentCtx, new CommonBillDTO()
        //    {
        //        FormId = this.FormId,
        //        OperationNo = "MSOrderApplyAuditSync",
        //        PageId = Guid.NewGuid().ToString("N"),
        //        BillData = billData.ToJson()
        //    });

        //    var result = response?.OperationResult;
        //    // 出现错误，后续不执行
        //    if (result == null)
        //    {
        //        foreach (var item in entrys)
        //        {
        //            resp.ErrorMessages.Add($"接单经销商【{item.ReceiverAgentNo}】转单申请单【{item.BillNo}】{this.OperationName}失败！");
        //        }

        //        return;
        //    }

        //    // 加载错误信息
        //    resp.ErrorMessages.AddRange(result.ComplexMessage.ErrorMessages);
        //    // 执行成功，填充执行成功的单据
        //    if (result.IsSuccess)
        //    {
        //        var srvData = result.SrvData as List<InternalSuccessBillData>;
        //        if (srvData != null)
        //        {
        //            resp.Data.AddRange(srvData.Select(s => new SuccessBillData
        //            {
        //                AgentNo = agentNo,
        //                BillNo = s.BillNo
        //            }));
        //        }
        //    }
        //}

        //private static void MapBillData(List<Dictionary<string, object>> billData, DynamicObject transferOrderApply, TransferOrderApplyAuditDTO.TransferOrderApplyAuditEntry entry,
        //    DynamicObjectCollection fentry)
        //{
        //    billData.Add(new Dictionary<string, object>
        //    {
        //        {"id", Convert.ToString(transferOrderApply["id"])},
        //        {"ftransferstatus", entry.TransferStatus},
        //        {"fapprovedate", entry.PassDate},
        //        {"freceiveragentcontactperson", entry.ReceiverAgentContactPerson},
        //        {"freceivercontactpersonphone", entry.ReceiverContactPersonPhone},

        //        {"ftargetagentid", entry.TargetAgentId},
        //        {"ftargetagentname", entry.TargetAgentName},

        //        {"fshipperagentid", entry.ShipperAgentId},
        //        {"fshipperagentname", entry.ShipperAgentName},

        //        {"fshipperagentcontactperson", entry.ShipperAgentContactPerson},
        //        {"fshippercontactpersonphone", entry.ShipperContactPersonPhone},
        //        {"fdistributionmethod", entry.DistributionMethod},
        //        {"fcostprice", entry.CostPrice},
        //        {"fotherfee", entry.OtherFee},
        //        {"freceiveragentdistribution", entry.ReceiverAgentDistribution},
        //        {"ftargetagentdistribution", entry.TargetAgentDistribution},
        //        {"freceiveragentamount", entry.ReceiverAgentAmount},
        //        {"ftargetagentamount", entry.TargetAgentAmount},
        //        {
        //            "fentry", new List<Dictionary<string, object>>
        //            {
        //                new Dictionary<string, object>
        //                {
        //                    {"id", fentry.First()["id"]},
        //                    {"fhqapprover", entry.HQApprover},
        //                    {"fapprovecomment", entry.ApproveComment}
        //                }
        //            }
        //        }
        //    });
        //}

        ///// <summary>
        ///// 校验
        ///// </summary>
        ///// <param name="dto"></param>
        ///// <param name="resp"></param>
        ///// <returns></returns>
        //private bool Valid(TransferOrderApplyAuditDTO dto, BaseResponse<List<SuccessBillData>> resp)
        //{
        //    if (dto.Data == null || dto.Data.Count == 0)
        //    {
        //        resp.Code = 400;
        //        resp.Message = "参数data不能为空！";
        //        resp.Success = false;

        //        return false;
        //    }

        //    var agentNos = new List<string>();
        //    agentNos.AddRange(dto.Data.Select(s => s.ReceiverAgentNo));
        //    agentNos.AddRange(dto.Data.Select(s => s.ShipperAgentNo));
        //    agentNos.AddRange(dto.Data.Select(s => s.TargetAgentNo));

        //    var agents = this.Context.LoadBizDataByNo("bas_agent", "fnumber", agentNos.Distinct());

        //    string errorMsg = string.Empty;
        //    foreach (var entry in dto.Data)
        //    {
        //        // 接单方经销商
        //        var receiverAgent = agents.FirstOrDefault(s =>
        //            Convert.ToString(s["fnumber"]).EqualsIgnoreCase(entry.ReceiverAgentNo));
        //        if (receiverAgent == null)
        //        {
        //            errorMsg += $"接单方经销商【{entry.ReceiverAgentNo}】不存在！";
        //        }
        //        else
        //        {
        //            entry.ReceiverAgentId = Convert.ToString(receiverAgent["id"]);
        //            entry.ReceiverAgentName = Convert.ToString(receiverAgent["fname"]);
        //        }

        //        // 发货经销商
        //        var shipperAgent = agents.FirstOrDefault(s =>
        //            Convert.ToString(s["fnumber"]).EqualsIgnoreCase(entry.ShipperAgentNo));
        //        if (shipperAgent == null)
        //        {
        //            errorMsg += $"发货经销商【{entry.ShipperAgentNo}】不存在！";
        //        }
        //        else
        //        {
        //            entry.ShipperAgentId = Convert.ToString(shipperAgent["id"]);
        //            entry.ShipperAgentName = Convert.ToString(shipperAgent["fname"]);
        //        }

        //        // 目标经销商
        //        var targetAgent = agents.FirstOrDefault(s =>
        //            Convert.ToString(s["fnumber"]).EqualsIgnoreCase(entry.TargetAgentNo));
        //        if (targetAgent == null)
        //        {
        //            errorMsg += $"目标经销商【{entry.TargetAgentNo}】不存在！";
        //        }
        //        else
        //        {
        //            entry.TargetAgentId = Convert.ToString(targetAgent["id"]);
        //            entry.TargetAgentName = Convert.ToString(targetAgent["fname"]);
        //        }
        //    }

        //    if (!errorMsg.IsNullOrEmptyOrWhiteSpace())
        //    {
        //        resp.Code = 400;
        //        resp.Message = errorMsg;
        //        resp.Success = false;

        //        return false;
        //    }

        //    return true;
        //}
        #endregion
    }
}