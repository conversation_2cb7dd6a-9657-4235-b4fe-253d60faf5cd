using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MS.API.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：非标审核
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("MSUnstdTypeAudit")]
    public class MSUnstdTypeAudit : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string orderNo = this.GetQueryOrSimpleParam("orderNo", "");
            if (orderNo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数orderNo不能为空！");
            }

            var entryDataStr = this.GetQueryOrSimpleParam("entryData", "");
            if (entryDataStr.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数entryData不能为空！");
            }

            JArray entryData = null;
            try
            {
                entryData = JArray.Parse(entryDataStr);
            }
            catch (Exception ex)
            {
                throw new BusinessException("参数entryData格式不正确！");
            }

            if (entryData == null || entryData.Count == 0)
            {
                throw new BusinessException("参数entryData不为空！");
            }

            var purchaseOrder = this.Context.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new string[] { orderNo })
                ?.FirstOrDefault();
            if (purchaseOrder == null)
            {
                throw new WarnException("采购订单不存在，请检查！");
            }

            var entrys = purchaseOrder["fentity"] as DynamicObjectCollection;

            bool needSave = false;

            foreach (var data in entryData)
            {
                var tranId = data.GetJsonValue("ftranid", "");

                var entry = entrys.FirstOrDefault(s => Convert.ToString(s["ftranid"]).EqualsIgnoreCase(tranId));
                if (entry == null) continue;

                needSave = true;

                entry["funstdtypecomment"] = data.GetJsonValue("funstdtypecomment", "");

                string status = data.GetJsonValue("funstdtypestatus", "");
                entry["funstdtypestatus"] = status;

                #region Task#36562 采购订单非标审批通过接口，价格相关字段不做赋值

                //decimal qty = Convert.ToDecimal(entry["fbizqty"]);
                //decimal price = decimal.Round(data.GetJsonValue("fprice", 0M), 2);
                //decimal distrate = 10;//decimal.Round(data.GetJsonValue("fdistrate", 10M), 2);
                //decimal distamount = 0;//decimal.Round(data.GetJsonValue("fdistamount", 0M), 2);
                //decimal amount = qty * price;
                //decimal dealamount = amount - distamount;
                //decimal dealprice = decimal.Round(dealamount / qty, 2);

                //decimal qty = Convert.ToDecimal(entry["fbizqty"]);
                //decimal price = decimal.Round(data.GetJsonValue("fprice", 0M), 2);

                //// 使用当前的折率
                //decimal distrate = Convert.ToDecimal(entry["fdistrate"]);
                //decimal amount = qty * price;

                //// 成交单价=采购价*折率/10
                //decimal dealprice = decimal.Round(price * distrate / 10, 2);
                //// 成交金额=成交单价*数量
                //decimal dealamount = dealprice * qty;
                //// 折扣额=金额-成交金额
                //decimal distamount = amount - dealamount;

                //entry["fprice"] = price;
                //entry["famount"] = amount;
                ////entry["fdistrate"] = distrate;
                //entry["fdistamount"] = distamount;
                //entry["fdealamount"] = dealamount;
                //entry["fdealprice"] = dealprice;

                #endregion

                //二级分销取价改造，接收总部给的非标审批价格更新到采购订单的【总部零售价】上
                decimal hqPrice = decimal.Round(data.GetJsonValue("fprice", 0M), 2);
                if (hqPrice > 0) 
                {
                    entry["fhqretailprice"] = hqPrice;
                }
            }

            if (!needSave)
            {
                throw new WarnException("非标审批的商品行不存在，请检查！");
            }

            // 因为不需要更新价格，所以不用模拟计算
            //this.Container.GetService<IPurchaseOrderService>().CalculateSettlement(this.Context, purchaseOrder);

            //var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { purchaseOrder }, "save", new Dictionary<string, object>());
            //result.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}保存失败！");

            // 直接保存数据库
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(purchaseOrder);

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "操作成功！";
        }
    }
}
