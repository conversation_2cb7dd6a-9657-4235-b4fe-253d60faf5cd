using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MS.API.Utils
{
    public class PurchaseOrderUtil
    {
        /// <summary>
        /// 计算金额信息（接口使用）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="purchaseOrder"></param>
        public static void Calcuate(UserContext userCtx, DynamicObject purchaseOrder)
        {
            var entities = purchaseOrder["fentity"] as DynamicObjectCollection;
            foreach (var entity in entities)
            {
                decimal qty = Convert.ToDecimal(entity["fbizqty"]);
                decimal price = Convert.ToDecimal(entity["fprice"]);
                decimal distrate = 0;
                //当数量大于0 折扣额才有意义，不然金额为0，折扣额不为0的话，成交金额为负数就不对了。
                decimal distamount = 0M;
                decimal amount = qty * price;
                decimal dealprice = 0M;
                decimal dealamount = 0M;

                if (qty != 0)
                {
                    distamount = Convert.ToDecimal(entity["fdistamount"]);
                    dealamount = amount - distamount;
                    dealprice = qty == 0 ? price : dealamount / qty;
                    distrate = price == 0 ? 10 : dealprice / price * 10;
                }

                entity["fprice"] = price;
                entity["famount"] = amount;
                entity["fdistrate"] = distrate;
                entity["fdistamount"] = distamount;
                entity["fdealamount"] = dealamount;
                entity["fdealprice"] = dealprice;
            }

            userCtx.Container.GetService<IPurchaseOrderService>().CalculateSettlement(userCtx, purchaseOrder);
        }
    }
}
