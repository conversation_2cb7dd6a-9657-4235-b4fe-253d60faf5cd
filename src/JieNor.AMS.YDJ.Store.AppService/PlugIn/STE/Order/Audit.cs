using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockPick;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.MerChant;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：审核
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// “可用”库存状态常量
        /// </summary>
        private const string KCZT_SYS_01 = "311858936800219137";
        private IDefaultValueCalculator defCalService { get; set; } = null;
        private IPrepareSaveDataService preSaveService { get; set; } = null;
        private HtmlForm purOrderForm { get; set; } = null;
        private Dictionary<string, string> DicChangeStatus { get; set; }
        private int ftoppiecesendtag { get; set; }
        private int fmanagemodel { get; set; }

        /// <summary>
        /// 当前二级经销商是否不管理库存
        /// </summary>
        private bool IsNotMgrInv { get; set; }


        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype" });
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fcustomer = Convert.ToString(newData["fcustomerid"]);
                bool isnull = this.CheckIsNull(newData);
                if (!isnull)
                {
                    if (fcustomer.IsNullOrEmptyOrWhiteSpace())
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("客户不能为空！"));
            IOrderService orderService = this.Container.GetService<IOrderService>();

            decimal auditminpercentage = 0;
            var spService = this.Container.GetService<ISystemProfile>();
            string sysProfileValue = spService.GetProfile(this.Context, "fw", $"bas_storesysparam_parameter");
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (sysProfileValue.IsNullOrEmptyOrWhiteSpace())
                {
                    return true;
                }
                //任务37793 修改为单据类型里取数
                var billTypeService = this.Container.GetService<IBillTypeService>();
                var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, this.HtmlForm, Convert.ToString(newData["fbilltype"]));
                if (paramSetObj != null)
                {
                    decimal.TryParse(Convert.ToString(paramSetObj["fauditminpercentage"]), out auditminpercentage);
                }
                else
                {
                    return true;
                }

                var freceivable = Convert.ToDecimal(newData["freceivable"]);
                var fsumamount = Convert.ToDecimal(newData["fsumamount"]);
                var minAmount = fsumamount * auditminpercentage / 100;
                return freceivable >= minAmount;
            }).WithMessage("确认已收金额必须达到订单总金额的{0}%才能审核。", (billObj, propObj) => auditminpercentage));

            //【确认金额】大于等于【首款额】才能审核
            decimal firstamount = 0;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var freceivable = Convert.ToDecimal(newData["freceivable"]);
                var ffirstamount = Convert.ToDecimal(newData["ffirstamount"]);
                firstamount = Math.Round(ffirstamount, 2);
                return freceivable >= ffirstamount;
            }).WithMessage("确认收款未达到首款额{0}元，不允许提交审核。", (billObj, propObj) => firstamount));

            string errMsg = "";

            // 审核变更校验
            //获取是否可以超额收款参数
            var profileService = this.Container.GetService<ISystemProfile>();
            string stockParamJson = profileService.GetProfile(this.Context, "fw", "bas_storesysparam_parameter");
            // 销售退换货允许变更合同
            var returnmodifyorder = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "freturnmodifyorder", false);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var changeStatus = Convert.ToString(newData["fchangestatus"]);
                var status = Convert.ToString(newData["fstatus"]);
                if (status == "D")
                {
                    if (changeStatus == "1")
                    {
                        errMsg = $"{this.HtmlForm.Caption}【{newData["fbillno"]}】变更状态不是“变更已提交”，请先提交变更后再审核！";
                        return false;
                    }
                    if (changeStatus == "3")
                    {

                        JObject stockParam = null;
                        if (!string.IsNullOrWhiteSpace(stockParamJson))
                        {
                            stockParam = JObject.Parse(stockParamJson);
                        }
                        var fcanexcess = false;
                        var property = stockParam?.Property("fcanexcess");
                        if (property != null)
                        {
                            fcanexcess = (bool)property.Value;
                        }

                        //如果不允许超额收款则需要检验是否超额
                        if (fcanexcess == false)
                        {
                            //单据状态=创建 且 变更状态=变更中的数据，最终保存时需要校验合法性
                            //已收
                            decimal freceivable = Convert.ToDecimal(newData["freceivable"]);
                            //订单总额
                            decimal fsumamount = Convert.ToDecimal(newData["fsumamount"]);
                            if (fsumamount < freceivable)
                            {
                                errMsg = "订单总额不能小于已收款金额，请检查!";
                                return false;
                            }
                        }
                    }
                }

                var fentry = newData["fentry"] as DynamicObjectCollection;
                var outspotEntry = fentry.FirstOrDefault(o =>
                {
                    return Convert.ToBoolean(o["fisoutspot"])
                     && Convert.ToString(o["fdeliverymode"]).EqualsIgnoreCase("1")
                     && (o["fstorehouseid"].IsNullOrEmptyOrWhiteSpace() || o["fstockstatus"].IsNullOrEmptyOrWhiteSpace());
                });
                if (outspotEntry != null)
                {
                    errMsg = "商品明细行为出现货且提货方式为自提时，仓库和库存状态不能为空！";
                    return false;
                }

                //如果 数量<已发货数 or 已出库数，则校验不通过 提示：“订单数量不能小于已关联下游单据的数量，请检查”
                foreach (var row in fentry)
                {
                    var fqty = Convert.ToDouble(row["fqty"]);
                    var fdeliveryqty = Convert.ToDouble(row["fdeliveryqty"]);
                    var foutqty = Convert.ToDouble(row["foutqty"]);
                    var freturnqty = Convert.ToDouble(row["freturnqty"]);
                    var flag = fqty < foutqty - freturnqty;
                    //如果开启参数，则将【销售退货中数量】纳入公式
                    if (returnmodifyorder)
                    {
                        //销售退换中数量
                        var returningQty = Convert.ToDouble(row["fbizreturningqty"]);
                        flag = fqty < foutqty - freturnqty - returningQty;
                    }
                    if (fqty < fdeliveryqty || flag)
                    {
                        errMsg = "订单数量不能小于已关联下游单据的数量，请检查";
                        return false;
                    }
                }

                return true;
            }).WithMessage("{0}", (billObj, propObj) => errMsg));

            ////效验 《门店系统参数设置》参数=>销售合同收款审核前, 必须收到全款  默认是 false
            //    var profileService = this.Container.GetService<ISystemProfile>();
            //    string stockParamJson = profileService.GetProfile(this.Context, "fw", "bas_storesysparam_parameter");
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    //获取是否必须收到全款 fpaymentmustbeget 参数

            //    JObject stockParam = null;
            //    if (!string.IsNullOrWhiteSpace(stockParamJson))
            //    {
            //        stockParam = JObject.Parse(stockParamJson);
            //    }
            //    var fpaymentmustbeget = false;
            //    var property = stockParam?.Property("fpaymentmustbeget");
            //    if (property != null)
            //    {
            //        fpaymentmustbeget = (bool)property.Value;
            //    }
            //    //如果是必须收到全款
            //    if (fpaymentmustbeget == true)
            //    {
            //        //已收
            //        decimal freceivable = Convert.ToDecimal(newData["freceivable"]);
            //        //订单总额
            //        decimal fsumamount = Convert.ToDecimal(newData["fsumamount"]);
            //        if (freceivable < fsumamount)
            //        {
            //            errMsg = $"审核失败,审核前必须收到全款,已收款 {Math.Round(freceivable, 2)} 小于成交金额 {Math.Round(fsumamount, 2)} ,请客户尽快付款 !";
            //            return false;
            //        }
            //    }
            //    return true;
            //}).WithMessage("{0}", (billObj, propObj) => errMsg));

            // 检查非标审批状态
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fbilltype = newData["fbilltype_ref"] as DynamicObject;

                var v6swjBillTypename = Convert.ToString(fbilltype["fname"]);
                var fomsservice = Convert.ToBoolean(newData["fomsservice"]);
                if (v6swjBillTypename == "v6定制柜合同" && fomsservice)
                {
                    return true;
                }
                return orderService.CheckUnstdStatus(this.Context, newData, out errMsg);
            }).WithMessage("{0}", (billObj, propObj) => errMsg));

            // 检查可编辑非标统一零售价是否可下单
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return orderService.CheckNoOrders(this.Context, newData, out errMsg);
            }).WithMessage("{0}", (billObj, propObj) => errMsg));
            //检查所有商品行【定制订单进度】都为“流程完成”
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fbilltype = newData["fbilltype_ref"] as DynamicObject;

                var v6swjBillTypename = Convert.ToString(fbilltype["fname"]);
                var fomsservice = Convert.ToBoolean(newData["fomsservice"]);
                if (v6swjBillTypename == "v6定制柜合同" && fomsservice)
                {
                    var fentry = newData["fentry"] as DynamicObjectCollection;
                    return !CheckOMSprogress(fentry, out errMsg);
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errMsg));
        }


        private bool CheckIsNull(DynamicObject data)
        {
            string fphone = Convert.ToString(data["fphone"]);
            string temp = Convert.ToString(data["fbilltype"]);
            string findenttype = Convert.ToString(data["findenttype"]);
            if (CheckIsV6Order(temp))
            {
                if (findenttype.Equals("Y") || findenttype.Equals("S") || findenttype.Equals("H"))
                {
                    //可以为空
                    return true;
                }
                else return false;//不可以为空
            }
            else
                return false;//不可以为空
        }
        /// <summary>
        /// 是否为v6定制柜合同
        /// </summary>
        /// <returns></returns>
        private bool CheckIsV6Order(string id)
        {
            bool v6OrNot = false;
            var svc = Context.Container.GetService<IBillTypeService>();
            var billTypeInfos = svc.GetBillTypeInfors(Context, "ydj_order");
            var billTypeInfo = billTypeInfos.FirstOrDefault(f => f.fid == id);
            if (billTypeInfo == null)
            {
                return v6OrNot;
            }

            v6OrNot = billTypeInfo.fname.EndsWithIgnoreCase("v6定制柜合同") || (!billTypeInfo.fprimitivename.IsNullOrEmptyOrWhiteSpace() && billTypeInfo.fprimitivename.EndsWithIgnoreCase("v6定制柜合同"));

            //var sqlText = string.Format(@"select fname from t_bd_billtype with(nolock) where fid='{0}' ", id);
            //using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
            //{
            //    while (reader.Read())
            //    {
            //        string str = reader.GetValueToString("fname");
            //        if (str.EndsWith("v6定制柜合同"))
            //        {
            //            v6OrNot = true;
            //            break;
            //        }
            //    }
            //}

            return v6OrNot;
        }


        /// <summary>
        /// 获取自定义参数
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billTypeService"></param>
        /// <param name="orderForm"></param>
        /// <param name="billType"></param>
        /// <returns></returns>
        private static DynamicObject GetDefaultBillTypeParam(UserContext userCtx, IBillTypeService billTypeService, HtmlForm orderForm, DynamicObject billType)
        {
            if (billType == null) return null;

            string billTypeId = Convert.ToString(billType["id"]);

            return billTypeService.GetBillTypeParamSet(userCtx, orderForm, billTypeId);
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys != null && e.DataEntitys.Any())
            {
                this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, false);
                var agent = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "ftoppiecesendtag,fmanagemodel");
                this.ftoppiecesendtag = Convert.ToInt32(agent["ftoppiecesendtag"]);
                this.fmanagemodel = Convert.ToInt32(agent["fmanagemodel"]);
                if (this.fmanagemodel == 1)
                {
                    foreach (var item in e.DataEntitys)
                    {
                        var fentrylist = item["fentry"] as DynamicObjectCollection;
                        SetPiecesTag(item, fentrylist);
                    }
                }
            }
            DicChangeStatus = GetSoureChangeStatus(e.DataEntitys);
            // 注意：要在执行事务前操作，避免变更状态已更新
        }

        private Dictionary<string, string> GetSoureChangeStatus(DynamicObject[] dataEntitys)
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            foreach (var dataEntity in dataEntitys)
            {
                var billno = Convert.ToString(dataEntity["fbillno"]);
                var soureChangeStatus = Convert.ToString(dataEntity["fchangestatus"]);
                dic.Add(billno, soureChangeStatus);
            }
            return dic;
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            //添加变更记录
            AddChangeFollowerRecord(e.DataEntitys);
        }

        /// <summary>
        /// 添加变更记录  '0':'正常','1':'变更中','2':'变更完成','3':'变更已提交'
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void AddChangeFollowerRecord(DynamicObject[] dataEntitys)
        {
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_followerrecord");
            var formDt = htmlForm.GetDynamicObjectType(this.Context);
            List<DynamicObject> followerRecordObjs = new List<DynamicObject>();
            foreach (var dataEntity in dataEntitys)
            {
                var billno = Convert.ToString(dataEntity["fbillno"]);
                string fchangestatus = DicChangeStatus[billno];
                // 如果不是“变更已提交”，则无需添加变更记录
                if (!fchangestatus.EqualsIgnoreCase("3"))
                {
                    continue;
                }
                //如果有没有变更记录，则无需创建跟进记录
                var fdescription = GetChangeDes(Convert.ToString(dataEntity["fbillno"]));
                if (fdescription.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                var customerobj = dataEntity["fcustomerid_ref"] as DynamicObject;
                var fcontacts = "";
                if (!customerobj.IsNullOrEmptyOrWhiteSpace())
                {
                    fcontacts = Convert.ToString(customerobj["fname"]);
                }
                if (fcontacts.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var followerRecordObj = new DynamicObject(formDt);
                followerRecordObj["fcustomerid"] = dataEntity["fcustomerid"];
                followerRecordObj["fcontacts"] = fcontacts;
                followerRecordObj["fphone"] = dataEntity["fphone"];
                followerRecordObj["ffollowtime"] = BeiJingTime.Now;
                followerRecordObj["ffollowerid"] = this.Context.UserId;
                followerRecordObj["fdeptid"] = dataEntity["fdeptid"];
                followerRecordObj["fstaffid"] = dataEntity["fstaffid"];
                followerRecordObj["ftype"] = "6";       // 默认是其他
                followerRecordObj["fdescription"] = fdescription;
                followerRecordObj["fobjecttype"] = "objecttype34";  // 变更
                followerRecordObj["fobjectid"] = dataEntity["id"];
                followerRecordObj["fobjectno"] = dataEntity["fbillno"];
                followerRecordObj["fsourcetype"] = followerRecordObj["frelatedbilltype"] = "ydj_order";
                followerRecordObj["fsourcenumber"] = followerRecordObj["frelatedbillno"] = dataEntity["fbillno"];
                followerRecordObj["ftranid"] = dataEntity["ftranid"];
                followerRecordObjs.Add(followerRecordObj);
            }
            if (followerRecordObjs.Any())
            {
                var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_followerrecord", followerRecordObjs, "save", new Dictionary<string, object>());
                result.ThrowIfHasError(true, "添加跟进记录失败！");
            }
        }

        /// <summary>
        /// 获取变更详细说明
        /// </summary>
        /// <param name="v"></param>
        /// <returns></returns>
        private string GetChangeDes(string fbillno)
        {
            var sql = "select top 1 * from t_ydj_order_chg where fsourcenumber=@fbillno and fmainorgid=@fmainorgid and FFormId like 'ydj_order_chg%' order by fcreatedate desc";
            List<SqlParam> pars = new List<SqlParam>();
            pars.Add(new SqlParam("@fbillno", DbType.String, fbillno));
            pars.Add(new SqlParam("@fmainorgid", DbType.String, this.Context.Company));
            var res = DBService.ExecuteDynamicObject(this.Context, sql, pars);

            if (res == null || !res.Any())
            {
                return "";
            }
            decimal fdealamount = Convert.ToDecimal(res[0]["fdealamount"]);
            decimal fdealamount_chg = Convert.ToDecimal(res[0]["fdealamount_chg"]);
            if (fdealamount == fdealamount_chg)//如果成交金额没有变
            {
                return "合同已变更";
            }
            return $@"合同已变更,成交金额由{Math.Round(fdealamount, 2)}元变更为{Math.Round(fdealamount_chg, 2)}元";
            // throw new NotImplementedException();
        }

        /// <summary>
        /// 启用OMS定制时，检查商品 定制订单进度
        /// </summary>
        /// <param name="entry">商品明细</param>
        /// <param name="errorMsg">错误提示</param>
        /// <returns></returns>
        private bool CheckOMSprogress(DynamicObjectCollection entry, out string errorMsg)
        {
            UserContext userCtx = this.Context;
            errorMsg = "";
            //至少要有一行商品明细
            if (entry == null || entry.Count <= 0) return false;
            //'-1':'单据作废','10':'待接单','20':'待审图','25':'待方案确认','30':'待拆单审核','40':'待报价审核','50':'流程完成'
            foreach (DynamicObject item in entry)
            {
                int.TryParse(Convert.ToString(item["fomsprogress"]), out var fomsprogress);
                if (fomsprogress == -1)
                    continue;
                if (fomsprogress != 50)
                {
                    errorMsg += $"对不起，第 {Convert.ToString(item["fseq"])}行 商品【定制订单进度】不为”流程完成”或”单据作废”，禁止提交审核！\r\n";
                }
            }
            if (errorMsg == "")
                return false;
            else return true;

        }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "onloadsyncinfo":
                    //LoadSyncInfo(e);
                    IOrderLoadSyncInfoService service = this.Container.GetService<IOrderLoadSyncInfoService>();
                    var result = service.LoadSyncInfo(this.Context, e.EventData as Tuple<string, DynamicObject>, this.Option);
                    e.Cancel = result.Item1;
                    e.Result = result.Item2;
                    break;
                case "parseconvertinfo":
                    parseConvertInfo(e);
                    break;
                case "onsyncsendcomplete":
                    //协同返回信息时，把错误信息装入错误消息
                    var _eventData = e.EventData as Tuple<string, DynamicObject, CommonBillDTOResponse, TargetSEP>;
                    var invokeResult = _eventData.Item3?.OperationResult;
                    if (invokeResult != null && !invokeResult.IsSuccess)
                    {
                        var errorMsgs = invokeResult.ComplexMessage.ErrorMessages;
                        if (errorMsgs?.Count > 0)
                        {
                            this.Result.ComplexMessage.ErrorMessages.AddRange(errorMsgs);
                            throw new BusinessException("同步数据失败！");
                        }

                        List<string> simpleMessage = new List<string>();
                        simpleMessage.Add(invokeResult.SimpleMessage);
                        if (simpleMessage.Count() > 0)
                        {
                            this.Result.ComplexMessage.ErrorMessages.AddRange(simpleMessage);
                            throw new BusinessException("同步数据失败！");
                        }
                    }
                    UpdateSelection(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 加载协同请求信息
        /// </summary>
        /// <param name="e"></param>
        private void LoadSyncInfo(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, DynamicObject>;
            if (eventData == null || eventData.Item2 == null) return;
            var dataEntity = eventData.Item2;

            //返回不需要打包的实体主键Id，比如：商品明细主键Id
            var productEntrys = dataEntity["fentry"] as DynamicObjectCollection;
            Dictionary<string, List<string>> notPackEntityPkId = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);
            notPackEntityPkId["fentry"] = productEntrys
                .Where(o => !Convert.ToString(o["foperationmode"]).EqualsIgnoreCase("1"))
                .Select(o => Convert.ToString(o["id"]))
                .ToList();

            var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, productForm.GetDynamicObjectType(this.Context));

            //商品明细根据商品对应的发布企业分组
            var groupEntry = productEntrys
                .Where(o => !o["fproductid"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(o["foperationmode"]).EqualsIgnoreCase("1"))
                .GroupBy(k =>
                {
                    var publishcid = "";
                    var publishpid = "";
                    var product = dm.Select(k["fproductid"]) as DynamicObject;
                    if (product != null)
                    {
                        var publishField = productForm?.GetField(productForm?.PublishCIdFldKey) as HtmlCompanyField;
                        publishcid = publishField?.DynamicProperty?.GetValue<string>(product);
                        publishpid = publishField?.ProductIdDynamicProperty?.GetValue<string>(product);
                    }
                    return $"{publishcid}#{publishpid}";
                });

            var supplierService = this.Container.GetService<ISupplierService>();

            Dictionary<TargetSEP, DynamicObject> syncData = new Dictionary<TargetSEP, DynamicObject>();
            foreach (var group in groupEntry)
            {
                var groupKeys = group.Key.Split('#');
                var target = supplierService.GetSyncTargetSEP(this.Context, groupKeys[0], groupKeys[1]);
                if (target == null) continue;

                var newEntity = dataEntity.Clone() as DynamicObject;
                var newEntityEntry = newEntity["fentry"] as DynamicObjectCollection;
                newEntityEntry.Clear();
                foreach (var entry in group)
                {
                    newEntityEntry.Add(entry);
                }
                syncData.Add(target, newEntity);
            }

            if (syncData.Count > 0)
            {
                e.Cancel = true;
                e.Result = new Dictionary<string, object>
                {
                    { "syncData", syncData },
                    { "simpleData", new Dictionary<string, string>() },
                    { "notPackEntityPkId", notPackEntityPkId }
                };
            }
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            if (this.Context.IsSecondOrg)
            {
                // 当前二级经销商信息
                var currentAgent = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fisnotmgrinv");
                this.IsNotMgrInv = Convert.ToString(currentAgent?["fisnotmgrinv"]) == "1";
            }
            //处理【初始辅助属性】反写
            var orderService = this.Container.GetService<IOrderService>();
            orderService.writeAttrinfoFirst(this.Context, this.HtmlForm, e.DataEntitys, 1);

            //出现货自动预留
            OutSpotAutoReserve(e.DataEntitys);

            var reqiureCount = 0;

            foreach (var dataEntity in e.DataEntitys)
            {
                var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    if (Convert.ToString(entry["foperationmode"]).EqualsIgnoreCase("1")
                        && Convert.ToBoolean(entry["fisreqiured"]) == false)
                    {
                        //更新为“已协同要货”
                        entry["fisreqiured"] = true;
                        reqiureCount++;
                    }
                }
                DocumentStatusHelper.CalcOrderCloseStatus(dataEntity, this.Context, string.Empty, !IsNotMgrInv);
            }

            //自动下推库存调拨单或销售出库单或采购订单
            AutoPush(e.DataEntitys);

            //if (reqiureCount > 0)//去掉，否则关闭状态不会更新
            //{
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);
            //}

            updateCostAccounting(e.DataEntitys);
            rewriteChannel(e.DataEntitys);
            rewritePurSalPrice(e.DataEntitys);

            FinishCustomerRecord(e.DataEntitys);
            HandleCustomer(e.DataEntitys);

            //ChangeResellOrder(e.DataEntitys);

            // 反写合同关联的二级经销商采购订单【总部合同状态】为“已终审”
            ResellerHelper.WriteBackPurchaseOrders(
                this.OperationContext,
                e.DataEntitys,
                OneLvOrderSratusConst.FinalAudited);
        }



        /// <summary>
        /// 预留更新
        /// </summary>
        private void OutSpotAutoReserve(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            this.Option.SetVariableValue("UpdateReserveDate", true);
            var result = ReserveUtil.UpdateReserve(this.Context, this.HtmlForm, dataEntities, this.Option);
            this.Result.MergeResult(result);
        }



        private void updateCostAccounting(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var costIds = dataEntities.Select(x => Convert.ToString(x["fcostid"])).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();
            if (costIds == null || costIds.Count <= 0)
            {
                return;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_costaccounting");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var costEntities = dm.Select(costIds).OfType<DynamicObject>().Where(x => Convert.ToString(x["forderchangestatus"]) == "2").ToArray();

            if (costEntities == null || costEntities.Length <= 0)
            {
                return;
            }

            foreach (var costEntity in costEntities)
            {
                //设置合同变更中
                costEntity["forderchangestatus"] = "3";
            }

            dm.Save(costEntities);
        }

        private void AutoPush(DynamicObject[] dataEntitys)
        {
            //先将出现货的商品明细下推生成库存调拨单，从而反写销售合同的物流跟踪号
            AutoPushInventoryTransfer(dataEntitys);

            var spService = this.Container.GetService<ISystemProfile>();
            var fautostockout = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fautostockout", false);
            if (Convert.ToBoolean(dataEntitys[0]["fneedtransferorder"]))
                return;
            if (fautostockout)
            {
                //再将“出现货”且“自提”的商品明细自动下推生成销售出库单，这样出库单也将有物流跟踪号
                AutoPushSoStockOut(dataEntitys);
            }
        }

        /// <summary>
        /// 将“出现货”且“自提”的商品明细自动下推生成销售出库单（自动 保存，提交，审核）
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void AutoPushSoStockOut(DynamicObject[] dataEntitys)
        {
            var soStockOutForm = this.MetaModelService.LoadFormModel(this.Context, "stk_sostockout");
            var soStockOutBills = new List<DynamicObject>();

            var convertService = this.Container.GetService<IConvertService>();

            StockPickSetting setting = new StockPickSetting
            {
                ActiveEntityKey = "fentity",
                QtyFieldKey = "fqty",
                PlanQtyFieldKey = "fplanqty",
                StockQtyFieldKey = "fstockqty",
                PriceFieldKey = "",
                AmountFieldKey = ""
            };
            var stockPickService = this.Container.GetService<IStockPickService>();

            foreach (var dataEntity in dataEntitys)
            {
                //是否存在“出现货”且“自提”的商品明细行
                var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                var outspotEntry = entrys.Where(o =>
                {
                    return Convert.ToBoolean(o["fisoutspot"]) && Convert.ToString(o["fdeliverymode"]).EqualsIgnoreCase("1") &&
                           (Convert.ToDecimal(o["fqty"]) - Convert.ToDecimal(o["foutqty"]) + Convert.ToDecimal(o["freturnqty"])) > 0;
                }).ToList();
                if (outspotEntry == null || outspotEntry.Count == 0) continue;

                var SelectedRows = outspotEntry.Select(x => new SelectedRow
                {
                    PkValue = Convert.ToString(dataEntity["Id"]),
                    EntityKey = "fentry",
                    EntryPkValue = Convert.ToString(x["Id"])
                }).ToConvertSelectedRows();

                var result = convertService.Push(this.Context, new BillConvertContext()
                {
                    RuleId = "ydj_order2stk_sostockout.auto",
                    SourceFormId = this.HtmlForm.Id,
                    TargetFormId = soStockOutForm.Id,
                    SelectedRows = SelectedRows,
                });
                var convertResult = result.SrvData as ConvertResult;
                if (convertResult.TargetDataObjects != null && convertResult.TargetDataObjects.Count() > 0)
                {
                    var pickResult = stockPickService.Picking(this.Context, setting, soStockOutForm, convertResult.TargetDataObjects, this.Option);
                    pickResult.ThrowIfHasError(true, "库存拣货出现意外错误！");

                    soStockOutBills.AddRange(convertResult.TargetDataObjects);
                }
            }

            if (soStockOutBills.Count > 0)
            {
                var invokeSave = this.Gateway.InvokeBillOperation(this.Context,
                    soStockOutForm.Id,
                    soStockOutBills,
                    "save",
                    new Dictionary<string, object>());
                invokeSave?.ThrowIfHasError(true, $"自动生成销售出库单失败！");

                if (invokeSave.IsSuccess)
                {
                    var invokeSubmit = this.Gateway.InvokeBillOperation(this.Context,
                        soStockOutForm.Id,
                        soStockOutBills,
                        "submit",
                        new Dictionary<string, object>());
                    invokeSubmit?.ThrowIfHasError(true, $"自动提交销售出库单失败！");

                    if (invokeSubmit.IsSuccess)
                    {
                        var invokeAudit = this.Gateway.InvokeBillOperation(this.Context,
                            soStockOutForm.Id,
                            soStockOutBills,
                            "audit",
                            new Dictionary<string, object>());
                        invokeAudit?.ThrowIfHasError(true, $"自动审核销售出库单失败！");
                    }
                }
            }
        }

        /// <summary>
        /// 将“出现货”的商品明细自动下推生成库存调拨单（自动 保存，提交，审核）
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void AutoPushInventoryTransfer(DynamicObject[] dataEntitys)
        {
            //销售管理参数：出现货商品自动更新物流跟踪号
            var sysProfile = this.Container.GetService<ISystemProfile>();
            var outspotUpdateMtono = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "foutspotupdatemtono", false);
            if (!outspotUpdateMtono) return;

            var downForm = this.MetaModelService.LoadFormModel(this.Context, "stk_inventorytransfer");
            var downBills = new List<DynamicObject>();
            var convertService = this.Container.GetService<IConvertService>();

            //批量联查已推的调拨单
            var entryIds = dataEntitys.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                                      .Where(x => Convert.ToBoolean(x["fisoutspot"]) &&
                                                  !Convert.ToString(x["fmtono"]).IsNullOrEmptyOrWhiteSpace())
                                      .Select(x => Convert.ToString(x["id"]))
                                      .ToList();
            if (entryIds == null || entryIds.Count <= 0)
            {
                return;
            }
            var multiValueQueryService = this.Container.GetService<IMultiValueQueryService>();
            var where = $"fmainorgid=@fmainorgid and fsourceformid='ydj_order' and {downForm.CancelStatusFldKey}='0'";
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company)
            };
            var downEntities = multiValueQueryService.Select(this.Context, where, sqlParams, downForm, "fsourceentryid", entryIds);
            var downEntries = downEntities?.SelectMany(x => x["fentity"] as DynamicObjectCollection)
                                           .Where(x => "ydj_order".EqualsIgnoreCase(Convert.ToString(x["fsourceformid"])) &&
                                                        entryIds.Contains(Convert.ToString(x["fsourceentryid"])))
                                           .GroupBy(x => Convert.ToString(x["fsourceentryid"]))
                                           .Select(x => new
                                           {
                                               entryId = x.Key,
                                               qty = x.Sum(y => Convert.ToDecimal(y["fqty"]))
                                           })
                                           .ToList();

            foreach (var dataEntity in dataEntitys)
            {
                //是否存在“出现货”且物流跟踪号不为空 的商品明细行
                var entries = dataEntity["fentry"] as DynamicObjectCollection;
                var orderId = Convert.ToString(dataEntity["id"]);
                var outspotEntries = entries.Select(o =>
                 {
                     var entryId = Convert.ToString(o["id"]);
                     var downEntry = downEntries?.FirstOrDefault(x => x.entryId.EqualsIgnoreCase(entryId));
                     var qty = downEntry == null ? 0m : downEntry.qty;
                     var canPut = Convert.ToBoolean(o["fisoutspot"])
                                  && !(Convert.ToBoolean(o["fisoutspot"]) && Convert.ToString(o["fdeliverymode"]).EqualsIgnoreCase("1")) //非(现货且自提),才能下推
                                  && !Convert.ToString(o["fmtono"]).IsNullOrEmptyOrWhiteSpace();
                     return new Dictionary<string, object>
                     {
                        { "orderId",orderId},
                        { "entryId",entryId},
                        { "qty", canPut? (Convert.ToDecimal(o["fqty"]) - qty) : 0m}
                     };
                 }).Where(x => Convert.ToDecimal(x["qty"]) > 0).ToList();

                if (outspotEntries == null || outspotEntries.Count <= 0)
                {
                    continue;
                }

                var option = OperateOption.Create();
                option.SetVariableValue("outspotEntries", outspotEntries);

                var result = convertService.Push(this.Context, new BillConvertContext()
                {
                    RuleId = "ydj_order2stk_inventorytransfer.auto",
                    SourceFormId = this.HtmlForm.Id,
                    TargetFormId = downForm.Id,
                    SelectedRows = outspotEntries.Select(x => new SelectedRow
                    {
                        PkValue = Convert.ToString(x["orderId"]),
                        EntityKey = "fentry",
                        EntryPkValue = Convert.ToString(x["entryId"])
                    }).ToConvertSelectedRows(),
                    Option = option
                });
                var convertResult = result.SrvData as ConvertResult;
                if (convertResult.TargetDataObjects != null && convertResult.TargetDataObjects.Count() > 0)
                {
                    downBills.AddRange(convertResult.TargetDataObjects);
                }
            }

            if (downBills.Count > 0)
            {
                var invokeSave = this.Gateway.InvokeBillOperation(this.Context,
                    downForm.Id,
                    downBills,
                    "save",
                    new Dictionary<string, object>());
                invokeSave?.ThrowIfHasError(true, $"自动生成{downForm.Caption}失败！");

                if (invokeSave.IsSuccess)
                {
                    var invokeSubmit = this.Gateway.InvokeBillOperation(this.Context,
                        downForm.Id,
                        downBills,
                        "submit",
                        new Dictionary<string, object>());
                    invokeSubmit?.ThrowIfHasError(true, $"自动提交{downForm.Caption}失败！");

                    if (invokeSubmit.IsSuccess)
                    {
                        var invokeAudit = this.Gateway.InvokeBillOperation(this.Context,
                            downForm.Id,
                            downBills,
                            "audit",
                            new Dictionary<string, object>());
                        invokeAudit?.ThrowIfHasError(true, $"自动审核{downForm.Caption}失败！");
                    }
                }
            }
        }

        /// <summary>
        /// 分析当前字段与对应下游字段是否参与允许变更的条件
        /// </summary>
        /// <param name="e"></param>
        private void parseConvertInfo(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null)
            {
                return;
            }
            var ruleId = Convert.ToString(eventData.GetValue("ruleId", string.Empty));
            var targetFieldId = Convert.ToString(eventData.GetValue("targetFieldId", string.Empty));

            if (ruleId.EqualsIgnoreCase("ydj_order2sal_deliverynotice") && targetFieldId.EqualsIgnoreCase("fplanqty"))
            {
                //不需要参与
                e.Cancel = true;
                return;
            }
        }

        /// <summary>
        /// 反写合作渠道
        /// </summary>
        /// <param name="dataEntities"></param>
        private void rewriteChannel(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            //检查是否已开启【合作渠道按比例计算佣金】参数
            var profileService = this.Container.GetService<ISystemProfile>();
            var enableBrokerageRatio = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablebrokerageratio", false);
            //如果没有开启，直接返回
            if (false == enableBrokerageRatio)
            {
                return;
            }

            //获取合作渠道
            var channelIds = dataEntities.Select(x => Convert.ToString(x["fchannel"])).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();
            if (channelIds == null || channelIds.Count <= 0)
            {
                return;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ste_channel");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var channelEntities = dm.Select(channelIds).OfType<DynamicObject>().ToList();

            if (channelEntities == null || channelEntities.Count <= 0)
            {
                return;
            }

            foreach (var channelEntity in channelEntities)
            {
                var channelId = Convert.ToString(channelEntity["id"]);
                var datas = dataEntities.Where(x => Convert.ToString(x["fchannel"]).EqualsIgnoreCase(channelId));

                if (datas == null || false == datas.Any())
                {
                    continue;
                }

                var sumAmount = datas.Select(x => Convert.ToDecimal(x["fsumamount"])).Sum();
                var sumBillAmount = Convert.ToDecimal(channelEntity["fsumbillamount"]);
                channelEntity["fsumbillamount"] = sumBillAmount + sumAmount;
            }

            dm.Save(channelEntities);
        }

        private void UpdateSelection(OnCustomServiceEventArgs e)
        {
            var _eventData = e.EventData as Tuple<string, DynamicObject, CommonBillDTOResponse, TargetSEP>;

            var srvData = _eventData.Item3?.OperationResult?.SrvData?.ToString();
            var data = new List<Dictionary<string, string>>();
            if (!srvData.IsNullOrEmptyOrWhiteSpace())
            {
                var sd = srvData.FromJson<Dictionary<string, List<Dictionary<string, string>>>>();
                if (sd.ContainsKey("billInfos"))
                {
                    data = sd["billInfos"];
                }
            }
            if (data.Count > 0)
            {
                foreach (var info in data)
                {
                    //var billInfos = info["billInfos"].FromJson<Dictionary<string, string>>();
                    if (info.ContainsKey("orderParam") && !info["orderParam"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var op = JObject.Parse(info["orderParam"].DecodeFromBase64String());
                        UpdateSelection(op, _eventData.Item4);
                    }
                }
            }
        }
        private void UpdateSelection(JObject orderParam, TargetSEP target)
        {
            string where = $"fmainorgid=@fmainorgid and ftranid = @ftranid";
            List<SqlParam> paramList = new List<SqlParam>();
            paramList.Add(new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company));
            paramList.Add(new SqlParam("ftranid", System.Data.DbType.String, orderParam["tranId"]));

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, paramList);
            var dataEntitys = dm.SelectBy(dataReader).OfType<DynamicObject>();
            if (dataEntitys != null && dataEntitys.Count() > 0)
            {
                this.Option.SetIgnoreOpLogFlag();
                var syncService = this.Container.GetService<ISynergyService>();

                foreach (var dataEntity in dataEntitys)
                {
                    //string tranId = Convert.ToString(dataEntity["ftranid"]);
                    //Dictionary<string, string> orderParam = orderParamList.FirstOrDefault(t => t["tranId"].EqualsIgnoreCase(tranId));

                    //string bizStatus = orderParam["bizStatus"];
                    string opCode = orderParam["opCode"].ToString();
                    string opName = orderParam["opName"].ToString();
                    //string pickDate = orderParam["pickDate"];
                    //DateTime opDate = DateTime.Now;
                    //string opDateStr = orderParam.GetValue("opDate", "");
                    //if (!DateTime.TryParse(opDateStr, out opDate))
                    //{
                    //    opDate = DateTime.Now;
                    //}
                    //if (bizStatus.IsNullOrEmptyOrWhiteSpace()) continue;

                    //处理商品明细
                    string jsonEntrys = "";
                    //if (orderParam.ContainsKey("entrys")) jsonEntrys = orderParam["entrys"];
                    jsonEntrys = orderParam["entrys"].ToString();
                    this.ProcProductEntrys(opCode, jsonEntrys, dataEntity, target);

                    //转换辅助属性字段值
                    this.ChainDataIdToLocalId(dataEntity, orderParam["chainDataJson"].ToString(), target);

                    //调用服务生成协同日志
                    syncService.WriteLog(this.Context, this.HtmlForm, dataEntity["id"] as string, opCode, opName, this.Context.CallerContext);
                }

                var prepareService = this.Container.GetService<IPrepareSaveDataService>();
                prepareService.PrepareDataEntity(this.Context, this.HtmlForm, dataEntitys.ToArray(), OperateOption.Create());

                //保存
                dm.Save(dataEntitys);

                //标记成功
                this.Result.IsSuccess = true;
            }
        }

        /// <summary>
        /// 处理商品明细
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="synProducts"></param>
        /// <returns></returns>
        private void ProcProductEntrys(string opCode, string jsonEntrys, DynamicObject dataEntity, TargetSEP target)
        {
            DynamicObjectCollection entitys = null;

            if (jsonEntrys.IsNullOrEmptyOrWhiteSpace()) return;

            var syncEntrys = jsonEntrys?.FromJson<List<Dictionary<string, object>>>();
            if (syncEntrys == null || syncEntrys.Count <= 0) return;

            entitys = dataEntity["fentry"] as DynamicObjectCollection;
            if (entitys != null)
            {
                for (var i = 0; i < entitys.Count; i++)
                {
                    var entity = entitys[i];
                    var product = this.Context.LoadBizDataById("ydj_product", entity["fproductid"].ToString());
                    if (Convert.ToBoolean(product["fissuit"]))
                    {
                        var fornumber = syncEntrys[i]["FPXPM"].ToString(); //返回的套件选配码
                        var orinumber = entity["fselectionnumber"].ToString();
                        if (fornumber == orinumber) continue;

                        var fromchaindataid = syncEntrys[i]["fmaterialid"].ToString();

                        //更新套件选配码为K3的选配码
                        //var sql = "UPDATE t_mt_suiteselection SET fnumber = @fornumber, fname = @fornumber WHERE fnumber = @orinumber";
                        var sql = @"/*dialect*/UPDATE t_mt_suiteselection SET fnumber = @fornumber, fname = @fornumber
FROM t_mt_suiteselection a
INNER JOIN t_bd_material b ON a.fproductid = b.fid
WHERE a.fnumber = @orinumber AND b.ffromchaindataid = @fromchaindataid AND a.fmainorgid = @mainorgid";
                        this.DBService.ExecuteDynamicObject(
                            this.Context,
                            sql,
                            new[] {
                                        new SqlParam("fornumber", DbType.String, fornumber),
                                        new SqlParam("orinumber", DbType.String, orinumber),
                                        new SqlParam("fromchaindataid", DbType.String, fromchaindataid),
                                        new SqlParam("mainorgid", DbType.String, this.Context.Company)
                            }
                        );
                        entity["fselectionnumber"] = fornumber;

                        if (!syncEntrys[i]["partsselection"].IsNullOrEmptyOrWhiteSpace())
                        {
                            var dyobjs = syncEntrys[i]["partsselection"].ToString().FromJson<List<Dictionary<string, string>>>();
                            foreach (var ps in dyobjs)
                            {
                                //suite_xpm,suite_materialid,parts_xpm, parts_materialid
                                sql = @"/*dialect*/UPDATE t_mt_partsselection SET fnumber = @parts_xpm, fname = @parts_xpm
FROM t_mt_partsselection c 
INNER JOIN t_bd_material cm ON c.fproductid = cm.fid
INNER JOIN t_mt_suiteselectionentry b ON b.fpartsselectionid = c.fid
INNER JOIN t_mt_suiteselection a ON a.fid = b.fid
INNER JOIN t_bd_material am ON a.fproductid = am.fid
WHERE a.fnumber = @suite_xpm AND am.ffromchaindataid = @suite_materialid AND cm.ffromchaindataid = @parts_materialid AND c.fmainorgid = @mainorgid";
                                this.DBService.ExecuteDynamicObject(
                                    this.Context,
                                    sql,
                                    new[] {
                                                new SqlParam("parts_xpm", DbType.String, ps["parts_xpm"]),
                                                new SqlParam("suite_xpm", DbType.String, ps["suite_xpm"]),
                                                new SqlParam("suite_materialid", DbType.String, ps["suite_materialid"]),
                                                new SqlParam("parts_materialid", DbType.String, ps["parts_materialid"]),
                                                new SqlParam("mainorgid", DbType.String, this.Context.Company)
                                    }
                                );
                            }
                        }
                    }
                    else
                    {
                        var fornumber = syncEntrys[i]["FXPM"].ToString(); //返回的子件选配码
                        var orinumber = entity["fselectionnumber"].ToString();
                        if (fornumber == orinumber) continue;
                        var fromchaindataid = syncEntrys[i]["fmaterialid"].ToString();
                        //更新子件选配码为K3的选配码
                        //var sql = "UPDATE t_mt_partsselection SET fnumber = @fornumber, fname = @fornumber WHERE fnumber = @orinumber";
                        var sql = @"/*dialect*/UPDATE t_mt_partsselection SET fnumber = @fornumber, fname = @fornumber
FROM t_mt_partsselection a
INNER JOIN t_bd_material b ON a.fproductid = b.fid
WHERE a.fnumber = @orinumber AND b.ffromchaindataid = @fromchaindataid AND a.fmainorgid = @mainorgid";
                        this.DBService.ExecuteDynamicObject(
                            this.Context,
                            sql,
                            new[] {
                                        new SqlParam("fornumber", DbType.String, fornumber),
                                        new SqlParam("orinumber", DbType.String, orinumber),
                                        new SqlParam("fromchaindataid", DbType.String, fromchaindataid),
                                        new SqlParam("mainorgid", DbType.String, this.Context.Company)
                            }
                        );
                        entity["fselectionnumber"] = fornumber;

                        var pfornumber = syncEntrys[i]["FPXPM"].ToString(); //返回的套件选配码
                        if (!pfornumber.IsNullOrEmptyOrWhiteSpace() && pfornumber != fornumber) //有套件选配码
                        {
                            //更新套件选配码
                            sql = @"/*dialect*/UPDATE t_mt_suiteselection SET fnumber = @fornumber, fname = @fornumber
FROM t_mt_suiteselection t
INNER JOIN t_mt_suiteselectionentry te ON t.fid = te.fid
INNER JOIN t_mt_partsselection a ON te.fpartsselectionid = a.fid
INNER JOIN t_bd_material b ON a.fproductid = b.fid
WHERE a.fnumber = @orinumber AND b.ffromchaindataid = @fromchaindataid AND a.fmainorgid = @mainorgid";
                            this.DBService.ExecuteDynamicObject(
                                this.Context,
                                sql,
                                new[] {
                                            new SqlParam("fornumber", DbType.String, pfornumber),
                                            new SqlParam("orinumber", DbType.String, orinumber),
                                            new SqlParam("fromchaindataid", DbType.String, fromchaindataid),
                                            new SqlParam("mainorgid", DbType.String, this.Context.Company)
                                }
                            );

                        }
                    }
                }
            }
        }

        /// <summary>
        /// 转换辅助属性字段值
        /// </summary>
        /// <param name="dataEntity"></param>
        private void ChainDataIdToLocalId(DynamicObject dataEntity, string chainDataJson, TargetSEP target)
        {
            var field = this.HtmlForm.GetField("fattrinfo");
            if (field == null || !(field is HtmlAuxPropertyField)) return;
            var auxPropField = field as HtmlAuxPropertyField;

            if (chainDataJson.IsNullOrEmptyOrWhiteSpace()) return;

            var jaChainData = JArray.Parse(chainDataJson);
            if (jaChainData == null || jaChainData.Count <= 0) return;

            var unPackService = this.Container.GetService<IUnPackUpdateLocalIdService>();
            var purchaseorderHtmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_purchaseorder");
            var chainMapList = unPackService.BindData(this.Context, purchaseorderHtmlForm, jaChainData); //框架封装的是采购订单的逻辑
            chainMapList.All(x =>
            {
                if (x["fpublishcid"].IsNullOrEmptyOrWhiteSpace())
                {
                    x["fpublishcid"] = target.CompanyId;
                    x["fpublishcid_pid"] = target.ProductId;
                }
                return true;
            });
            var chainEntitys = chainMapList[0]?.GetJsonValue<JArray>("fentity");
            if (chainEntitys == null || chainEntitys.Count <= 0) return;

            var auxPropValueSetForm = this.MetaModelService.LoadFormModel(this.Context, "bd_auxpropvalueset");
            var dcSerializer = this.Container.GetService<IDynamicSerializer>();

            var entitys = dataEntity["fentry"] as DynamicObjectCollection;
            foreach (var entity in entitys)
            {
                foreach (var chainEntity in chainEntitys)
                {
                    if (Convert.ToString(entity["ftranid"]).EqualsIgnoreCase(Convert.ToString(chainEntity?["fentity_ftranid"])))
                    {
                        var attrInfos = new JArray();
                        attrInfos.Add(chainEntity?.GetJsonValue<JObject>("fattrinfo"));

                        List<DynamicObject> targetDataObjects = new List<DynamicObject>();
                        dcSerializer.Sync(auxPropValueSetForm.GetDynamicObjectType(this.Context), targetDataObjects, attrInfos);
                        if (targetDataObjects.Count > 0)
                        {
                            //清掉多余的字段值
                            targetDataObjects[0]["ftranid"] = "";
                            var _entitys = targetDataObjects[0]["fentity"] as DynamicObjectCollection;
                            foreach (var _entity in _entitys)
                            {
                                _entity["ftranid"] = "";
                            }
                            //设置辅助属性组合值
                            auxPropField?.RefDynamicProperty?.SetValue(entity, targetDataObjects[0]);
                        }
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 反写销售机会的商机阶段为已成单
        /// </summary>
        /// <param name="dataEntities"></param>
        private void FinishCustomerRecord(DynamicObject[] dataEntities)
        {
            var orderNos = dataEntities?.Select(s => Convert.ToString(s["fbillno"]));

            if (orderNos == null || !orderNos.Any()) return;

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@fmainorgid", DbType.String, this.Context.Company));

            // 未成单
            string sqlWhere = $@" fmainorgid=@fmainorgid and forderno in ({string.Join(",", orderNos.Select(s => $"'{s}'"))}) and fphase<> 'customerrecord_phase_05' ";
            var reader = this.Context.GetPkIdDataReader(htmlForm, sqlWhere, sqlParams);

            var customerRecords = dm.SelectBy(reader).OfType<DynamicObject>().ToList();
            if (customerRecords.Count == 0) return;

            ICustomerRecordService customerRecordService = this.Container.GetService<ICustomerRecordService>();
            foreach (var customerRecord in customerRecords)
            {
                customerRecordService.Finish(this.Context, customerRecord);
            }

            // 添加跟进记录
            AddCustomerRecordFollowerRecord(customerRecords);
        }

        /// <summary>
        /// 添加跟进记录
        /// </summary>
        /// <param name="customerRecordObjs"></param>
        private void AddCustomerRecordFollowerRecord(IEnumerable<DynamicObject> customerRecordObjs)
        {
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_followerrecord");
            var formDt = htmlForm.GetDynamicObjectType(this.Context);

            List<DynamicObject> followerRecordObjs = new List<DynamicObject>();

            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            string deptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
            string staffId = baseFormProvider.GetMyStaff(this.Context)?.Id;

            foreach (var item in customerRecordObjs)
            {
                var followerRecordObj = new DynamicObject(formDt);

                followerRecordObj["fcustomerid"] = item["fcustomerid"];
                followerRecordObj["fcontacts"] = item["fcustomername"];
                followerRecordObj["fphone"] = item["fphone"];
                followerRecordObj["ffollowtime"] = DateTime.Now;
                followerRecordObj["ffollowerid"] = this.Context.UserId;
                followerRecordObj["fdeptid"] = deptId;
                followerRecordObj["fstaffid"] = staffId;
                followerRecordObj["ftype"] = "6";       // 默认是其他
                followerRecordObj["fdescription"] = $"商机已自动成单关闭。";
                followerRecordObj["fobjecttype"] = "objecttype20";  // 成单关闭
                followerRecordObj["fobjectid"] = item["id"];
                followerRecordObj["fobjectno"] = item["fbillno"];
                followerRecordObj["fsourcetype"] = followerRecordObj["frelatedbilltype"] = "ydj_customerrecord";
                followerRecordObj["fsourcenumber"] = followerRecordObj["frelatedbillno"] = item["fbillno"];
                followerRecordObj["ftranid"] = item["ftranid"];

                followerRecordObjs.Add(followerRecordObj);
            }

            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_followerrecord", followerRecordObjs, "save", new Dictionary<string, object>());
            result.ThrowIfHasError(true, "添加跟进记录失败！");
        }



        /// <summary>
        /// 处理客户
        /// 1. 更新客户性质为成单
        /// </summary>
        /// <param name="dataEntities"></param>
        private void HandleCustomer(DynamicObject[] dataEntities)
        {
            var customerIds = dataEntities?.Select(s => Convert.ToString(s["fcustomerid"]));
            if (customerIds == null || !customerIds.Any()) return;

            var customers = this.Context.LoadBizDataById("ydj_customer", customerIds);
            if (customers == null && !customers.Any()) return;

            List<DynamicObject> saveEntitys = new List<DynamicObject>();
            foreach (var item in customers)
            {
                string cusnature = Convert.ToString(item["fcusnature"]);

                if (cusnature.EqualsIgnoreCase("cusnature_02") == false || item["ffirstordertime"] == null)
                {
                    if (cusnature.EqualsIgnoreCase("cusnature_02") == false)
                    {
                        // 更新客户性质为：成单
                        item["fcusnature"] = "cusnature_02";
                    }

                    if (item["ffirstordertime"] == null)
                    {
                        // 首次成单时间为空时，更新首次成单时间
                        item["ffirstordertime"] = BeiJingTime.Now;
                    }

                    saveEntitys.Add(item);
                }
            }
            if (saveEntitys.Any() == false && customers.Count > 0)
            {
                //调用刷新接口，重新刷新客户成交总额
                this.Gateway.InvokeBillOperation(this.Context, "ydj_customer", customers, "SumMountrefresh", null);
                return;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_customer");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            dm.Save(saveEntitys);
            //调用刷新接口，重新刷新客户成交总额
            this.Gateway.InvokeBillOperation(this.Context, "ydj_customer", customers, "SumMountrefresh", null);

            foreach (var dataEntity in saveEntitys)
            {
                //将协同客户到K3Cloud
                DirectSynergyHelper.SyncCustomerToK3Cloud(this.Context, htmlForm, dataEntity);
            }
        }

        /// <summary>
        /// 变更二级分销采购和合同
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void ChangeResellOrder(DynamicObject[] dataEntitys)
        {
            if (dataEntitys == null || !dataEntitys.Any()) return;
            List<DynamicObject> changeOrds = dataEntitys.Where(x => Convert.ToString(x["fchangestatus"]) == "2")?.ToList();
            if (changeOrds == null || !changeOrds.Any()) return;
            var resellPurIds = changeOrds.Where(x => Convert.ToBoolean(x["fisresellorder"]) && !x["fsourceid"].IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x["fsourceid"]))?.Distinct().ToList();
            if (resellPurIds != null && resellPurIds.Any())
            {
                // 批量加载采购订单商品信息
                var productObjs = this.LoadProducts(changeOrds.ToArray());
                //二级分销采购订单集合
                var purOrders = this.Context.LoadBizDataById("ydj_purchaseorder", resellPurIds);
                // 加载当前二级经销商对应的客户ID
                var currentAgentCus = this.LoadCurrentAgentCustomerId(purOrders.Select(x => Convert.ToString(x["fmainorgid"])).Distinct().ToList());
                // 当前二级经销商信息
                var currentAgents = this.Context.LoadBizDataById("bas_agent", purOrders.Select(x => Convert.ToString(x["fmainorgid"])).ToList());
                // 批量加载一级经销商定义的商品二级分销价格
                var productPrices = this.LoadProductResellerPrice(changeOrds.ToArray(), purOrders, currentAgentCus);

                //批量加载商品总部采购价目
                var hqPurPrices = this.LoadProductStandPrice(changeOrds.ToArray(), purOrders, currentAgentCus);

                var orderIds = purOrders?.SelectMany(o => (o["fentity"] as DynamicObjectCollection).Select(x => Convert.ToString(x["fsourceinterid"]))).Distinct().ToList();
                var orders = new List<DynamicObject>();
                var existsOrderNo = new List<string>();
                if (orderIds != null && orderIds.Any())
                {
                    orders = this.Context.LoadBizDataById("ydj_order", orderIds);
                    if (orders != null && orders.Any())
                    {
                        existsOrderNo.AddRange(orders.Select(x => Convert.ToString(x["fbillno"])));
                    }
                }

                List<DynamicObject> changeOrders = new List<DynamicObject>();
                List<DynamicObject> changePurOrders = new List<DynamicObject>();
                InitService();
                //需要重新取总部零售价的分销商合同信息
                List<DynamicObject> preGetPriceOrders = new List<DynamicObject>();
                foreach (var oneorder in changeOrds.ToArray())
                {
                    var purOrder = purOrders.FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(oneorder["fsourceid"]));
                    if (purOrder.IsNullOrEmpty()) continue;
                    var resellOrder = orders?.FirstOrDefault(x => Convert.ToString(x["fbillno"]) == Convert.ToString(purOrder["forderno"]) && Convert.ToString(x["fmainorgid"]) == Convert.ToString(purOrder["fmainorgid"]));

                    var currentAgent = currentAgents?.FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(purOrder["fmainorgid"]));

                    //二级采购单
                    var purOrdCStatus = Convert.ToString(purOrder["fchangestatus"]);
                    if (purOrdCStatus == "0" || purOrdCStatus == "2")
                    {
                        this.Context.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), oneorder, true);
                        SetPurOrdChangeIngo(oneorder, purOrder, resellOrder, productObjs, productPrices, hqPurPrices, currentAgent);
                        changePurOrders.Add(purOrder);
                    }
                    //二级合同
                    var resellOrdCStatus = Convert.ToString(resellOrder?["fchangestatus"]);
                    if (resellOrder != null && (resellOrdCStatus == "0" || resellOrdCStatus == "2"))
                    {
                        if ((resellOrder["fentry"] as DynamicObjectCollection).Any(x => Convert.ToDecimal(x["fqty"]) > 0 && Convert.ToDecimal(x["fprice"]) == 0))
                        {
                            preGetPriceOrders.Add(resellOrder);
                        }
                        changeOrders.Add(resellOrder);
                    }
                }

                Dictionary<string, UserContext> agentContexs = new Dictionary<string, UserContext>();
                foreach (var item in currentAgents)
                {
                    var key = Convert.ToString(item["id"]);
                    if (!agentContexs.ContainsKey(key))
                    {
                        //此处需执行脚本修改触发器Order_Chg_Error_Data后即可行
                        agentContexs.Add(key, this.Context.CreateAgentDBContext(key));
                    }
                }

                if (preGetPriceOrders.Any())
                {
                    var profileService = this.Container.GetService<ISystemProfile>();

                    //要取总部零售价的分销商合同
                    var hqPrices = LoadProductHqPrice(preGetPriceOrders);
                    #region 再循环计算一遍价格相关数据
                    foreach (var item in preGetPriceOrders)
                    {
                        // 经销商上下文
                        var agentId = Convert.ToString(item["fmainorgid"]);
                        var agentCtx = agentContexs.GetValue(agentId);

                        // 销售合同赠品参与折扣计算
                        var isgiftdiscount = false;
                        if (agentCtx != null)
                        {
                            isgiftdiscount = profileService.GetSystemParameter(agentCtx, "bas_storesysparam", "fisgiftdiscount", false);
                        }

                        var purOrder = purOrders.Where(x => Convert.ToString(x["fsourcenumber"]) == Convert.ToString(item["fbillno"]));
                        var purOrderIds = purOrder.Select(x => Convert.ToString(x["id"]));
                        var oneOrderEntrys = changeOrds.Where(x => purOrderIds.Contains(Convert.ToString(x["fsourceid"]))).SelectMany(x => x["fentry"] as DynamicObjectCollection);

                        var faceAmountSum2 = 0M;
                        var faceAmountSum = 0M;
                        var distAmountSum = 0M;
                        var dealAmountSum = 0M;
                        foreach (var ordEntry in item["fentry"] as DynamicObjectCollection)
                        {
                            var purOrdEntryId = purOrder.SelectMany(x => x["fentity"] as DynamicObjectCollection).FirstOrDefault(x => Convert.ToString(x["fsourceentryid_e"]) == Convert.ToString(ordEntry["id"]))["id"];
                            var productId = Convert.ToString(ordEntry["fproductid"]);
                            var bizQty = Convert.ToDecimal(ordEntry["fbizqty"]);
                            //----
                            var oneOrderEntry = oneOrderEntrys?.FirstOrDefault(o => !o["fsourceentryid_e"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(o["fsourceentryid_e"]) == Convert.ToString(purOrdEntryId));
                            var price = Convert.ToDecimal(ordEntry["fprice"]);
                            if (price == 0)
                            {
                                var hqPrice = hqPrices?.FirstOrDefault(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(ordEntry["id"])));
                                price = Convert.ToDecimal(hqPrice?["salPrice"] ?? 0);
                            }
                            var distRate = Convert.ToDecimal(ordEntry["fdistrate"]);
                            var distRateRaw = ordEntry["fdistrateraw"];
                            var amount = price * bizQty;
                            var dealPrice = price * distRate / 10;
                            var dealAmount = dealPrice * bizQty;
                            var distAmount = amount - dealAmount;

                            distAmountSum += distAmount; // 折扣额汇总
                            dealAmountSum += dealAmount; // 成交金额汇总

                            // 货品原值汇总
                            var isGiveaway = Convert.ToBoolean(ordEntry["fisgiveaway"]);
                            if (isgiftdiscount)
                            {
                                //汇总非赠品商品的货品原值
                                if (!isGiveaway)
                                {
                                    faceAmountSum2 += amount;
                                }
                                faceAmountSum += amount;
                            }
                            else
                            {
                                // 赠品不参与计算货品原值
                                if (!isGiveaway)
                                {
                                    faceAmountSum += amount;
                                }
                            }

                            ordEntry["fprice"] = price;
                            ordEntry["famount"] = amount;
                            ordEntry["fdealprice"] = dealPrice;
                            ordEntry["fdealamount"] = dealAmount;
                            ordEntry["fdistamount"] = distAmount;

                            #region 二级合同财务信息
                            // 财务信息
                            item["ffaceamount"] = faceAmountSum;
                            item["fdistamount"] = distAmountSum;
                            item["fdealamount"] = dealAmountSum;

                            // 货款总折扣率
                            var distSumRate = 0M;
                            if (isgiftdiscount)
                            {
                                //货款总折扣率 = 成交金额 / 非赠品商品的货品原值
                                distSumRate = faceAmountSum2 != 0 ? dealAmountSum / faceAmountSum2 : 1;
                            }
                            else
                            {
                                // 货款总折扣率 = 成交金额 / 货品原值
                                distSumRate = faceAmountSum != 0 ? dealAmountSum / faceAmountSum : 1;
                            }
                            item["fdistsumrate"] = distSumRate;

                            // 货款总折扣额 = 折扣额
                            var distSumAmount = distAmountSum;
                            item["fdistsumamount"] = distSumAmount;

                            // 未收款 = 成交金额 + 费用收入 - 确认收款 - 申请退货金额
                            var unReceived = dealAmountSum + 0 - Convert.ToDecimal(item["freceivable"]) - Convert.ToDecimal(item["frefundamount"]);
                            item["funreceived"] = unReceived;

                            // 订单总额 = 成交金额 + 费用收入
                            var sumAmount = dealAmountSum + 0;
                            item["fsumamount"] = sumAmount;

                            //二级分销：把二级销售合同【成交单价】【成交金额】携带至一级销售合同【终端零售价】【终端金额】,【终端金额】根据数量计算
                            var onebizQty = Convert.ToDecimal(oneOrderEntry["fbizqty"]);
                            var fterprice = ordEntry?["fdealprice"] ?? 0;
                            oneOrderEntry["fterprice"] = fterprice;
                            oneOrderEntry["fteramount"] = Convert.ToDecimal(fterprice) * onebizQty;
                            #endregion
                        }
                    }
                    #endregion
                }

                var invokeOneSave = this.Gateway.InvokeBillOperation(this.Context,
                   "ydj_order",
                   changeOrds,
                   "save",
                   new Dictionary<string, object>());
                invokeOneSave?.ThrowIfHasError(true, $"自动保存合同信息失败！");

                if (invokeOneSave.IsSuccess)
                {
                    foreach (var item in agentContexs)
                    {
                        var agentCtx = item.Value;
                        var agentOrders = changeOrders.Where(x => Convert.ToString(x["fmainorgid"]) == item.Key);
                        //变更后去覆盖一级合同数据到二级合同
                        if (agentOrders != null && agentOrders.Any())
                        {
                            var invokeChange = this.Gateway.InvokeBillOperation(agentCtx,
                            "ydj_order",
                            agentOrders,
                            "change",
                            new Dictionary<string, object>());
                            invokeChange?.ThrowIfHasError(true, $"关联变更二级分销合同失败！");
                            if (invokeChange.IsSuccess)
                            {
                                var invokeSave = this.Gateway.InvokeBillOperation(agentCtx,
                                "ydj_order",
                                agentOrders,
                                "save",
                                new Dictionary<string, object>());
                                invokeSave?.ThrowIfHasError(true, $"关联保存二级分销合同失败！");
                                if (invokeSave.IsSuccess)
                                {
                                    var invokeSubmit = this.Gateway.InvokeBillOperation(agentCtx,
                                    "ydj_order",
                                    agentOrders,
                                    "submitchange",
                                    new Dictionary<string, object>() { { "changeReason", "一级合同变更生效自动变更二级合同！" } });
                                    invokeSubmit?.ThrowIfHasError(true, $"关联提交变更二级分销合同失败！");
                                    if (invokeSubmit.IsSuccess)
                                    {
                                        var invokeAudit = this.Gateway.InvokeBillOperation(agentCtx,
                                        "ydj_order",
                                        agentOrders,
                                        "audit",
                                        new Dictionary<string, object>() { { "execOpinion", "一级合同变更生效自动变更审核二级合同！" } });
                                        invokeAudit?.ThrowIfHasError(true, $"关联审核二级分销合同失败！");
                                    }
                                }
                            }
                        }
                        var agentPurOrders = changePurOrders.Where(x => Convert.ToString(x["fmainorgid"]) == item.Key);
                        //变更后去覆盖一级合同数据到二级采购
                        if (agentPurOrders != null && agentPurOrders.Any())
                        {
                            var invokeChange = this.Gateway.InvokeBillOperation(agentCtx,
                            "ydj_purchaseorder",
                            agentPurOrders,
                            "change",
                            new Dictionary<string, object>());
                            invokeChange?.ThrowIfHasError(true, $"关联变更二级分销采购失败！");
                            if (invokeChange.IsSuccess)
                            {
                                var invokeSave = this.Gateway.InvokeBillOperation(agentCtx,
                                "ydj_purchaseorder",
                                agentPurOrders,
                                "save",
                                new Dictionary<string, object>());
                                invokeSave?.ThrowIfHasError(true, $"关联保存二级分销采购失败！");
                                if (invokeSave.IsSuccess)
                                {
                                    var invokeSubmit = this.Gateway.InvokeBillOperation(agentCtx,
                                    "ydj_purchaseorder",
                                    agentPurOrders,
                                    "submitchange",
                                    new Dictionary<string, object>() { { "changeReason", "一级合同变更生效自动变更二级采购！" } });
                                    invokeSubmit?.ThrowIfHasError(true, $"关联提交变更二级分销采购失败！");
                                    if (invokeSubmit.IsSuccess)
                                    {
                                        var invokeAudit = this.Gateway.InvokeBillOperation(agentCtx,
                                        "ydj_purchaseorder",
                                        agentPurOrders,
                                        "audit",
                                        new Dictionary<string, object>() { { "execOpinion", "一级合同变更生效自动变更审核二级采购！" } });
                                        invokeAudit?.ThrowIfHasError(true, $"关联审核二级分销合同失败！");
                                    }
                                }
                            }
                        }

                    }
                }
            }
        }

        /// <summary>
        /// 将变更后数据覆盖到下游二级采购
        /// </summary>
        /// <param name="dataEntity">一级合同</param>
        /// <param name="changePurOrder">二级分销采购</param>
        /// <param name="resellOrder">二级分销合同</param>
        /// <param name="productObjs"></param>
        /// <param name="productPrices">一级经销商定义的商品二级分销价格</param>
        /// <param name="hqPurPrices">商品总部采购价目</param>
        /// <param name="currentAgent">当前二级分销商信息</param>
        private void SetPurOrdChangeIngo(DynamicObject dataEntity, DynamicObject changePurOrder, DynamicObject resellOrder,
            DynamicObjectCollection productObjs,
            List<JObject> productPrices, List<JToken> hqPurPrices,
            DynamicObject currentAgent)
        {
            var profileService = this.Container.GetService<ISystemProfile>();

            // 销售合同赠品参与折扣计算
            var isgiftdiscount = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fisgiftdiscount", false);

            var oneOrdEntrys = dataEntity?["fentry"] as DynamicObjectCollection;
            var purOrdEntrys = changePurOrder["fentity"] as DynamicObjectCollection;
            var faceAmountSum2 = 0M;
            var faceAmountSum = 0M;
            var distAmountSum = 0M;
            var dealAmountSum = 0M;
            // 将销售合同中不存在但采购订单中存在的明细行删除
            if (oneOrdEntrys.Any() && purOrdEntrys.Any())
            {
                var beRemoves = new List<DynamicObject>();
                foreach (var purEntry in purOrdEntrys)
                {
                    var purEntryId = Convert.ToString(purEntry["id"]);
                    if (!oneOrdEntrys.Any(o => Convert.ToString(o["fsourceentryid_e"]).EqualsIgnoreCase(purEntryId)))
                    {
                        beRemoves.Add(purEntry);
                    }
                }
                foreach (var item in beRemoves)
                {
                    purOrdEntrys.Remove(item);
                }
            }
            foreach (var item in oneOrdEntrys)
            {
                var ordEntryId = Convert.ToString(item["id"]);
                var ordSourceEntryId = Convert.ToString(item["fsourceentryid_e"]);
                if (string.IsNullOrWhiteSpace(ordSourceEntryId))
                    continue;
                // 不存在则新增【且重新定价合同价格为分销价等】，已存在则覆盖
                var purOrdEntry = purOrdEntrys?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(ordSourceEntryId));
                // 商品信息
                var productId = Convert.ToString(item["fproductid"]);
                var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                decimal price = Convert.ToDecimal(item["fprice"]);
                if (purOrdEntry == null)
                {
                    purOrdEntry = new DynamicObject(purOrdEntrys.DynamicCollectionItemPropertyType);
                    purOrdEntrys.Add(purOrdEntry);
                    purOrdEntry["fmaterialid"] = productId;
                    purOrdEntry["fclosestatus_e"] = 0;
                    // 商品统一零售价
                    var productPrice = productPrices?.FirstOrDefault(o =>
                        Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(ordEntryId)));

                    // 总部采购价目
                    var hqPurPrice = hqPurPrices?.FirstOrDefault(o =>
                        Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(ordEntryId)));

                    //生成的一级分销销售合同【标准商品】的对应的零售价等于总部采购价目*二级分销档案的【分销价格系数】，其他的暂不处理，按之前逻辑走
                    if (!Convert.ToBoolean(item["funstdtype"]) && item["fattrinfo"].IsNullOrEmptyOrWhiteSpace() && productPrice.IsNullOrEmpty())
                    {
                        price = Convert.ToDecimal(hqPurPrice["purPrice"]) * matchResellRatio(Convert.ToString((item?["fproductid_ref"] as DynamicObject)["fseriesid"]), currentAgent);
                    }
                    else
                    {
                        price = Convert.ToDecimal(productPrice?["fsalprice"] ?? 0);
                    }

                    if (Convert.ToBoolean(item["funstdtype"]))
                    {
                        //////'01':'新建','02':'待审批','03':'审批通过','04':'驳回','05':'待定价','06':'终审'
                        //if (Convert.ToString(item["funstdtypestatus"]) != "01")
                        //{
                        purOrdEntry["funstdtypestatus"] = item["funstdtypestatus"];
                        purOrdEntry["funstdtypecomment"] = item["funstdtypecomment"];
                        //}
                        //purOrdEntry["fprice"] = 0;
                    }

                    item["fstockstatus"] = KCZT_SYS_01; // 库存状态默认为“可用”
                    item["fdeliverymode"] = "0"; // 提货方式默认为“物流配送”
                    //item["fclosestatus_e"] = "0";

                    defCalService.Execute(this.Context, purOrderForm, new DynamicObject[] { changePurOrder });
                    preSaveService.PrepareDataEntity(this.Context, purOrderForm, new DynamicObject[] { changePurOrder }, this.Option);
                    // 源单信息
                    item["fsourcetype_e"] = "ydj_purchaseorder";
                    item["fsourcenumber_e"] = changePurOrder["fbillno"];
                    item["fsourceentryid_e"] = purOrdEntry["id"];
                }
                //else
                //{
                //    var curramount = price * Convert.ToDecimal(item["fbizqty"]);
                //    faceAmountSum += curramount; // 货品原值汇总
                //    distAmountSum += curramount - Convert.ToDecimal(item["fdistamount"]); // 折扣额汇总
                //    dealAmountSum += Convert.ToDecimal(item["fdealamount"]); // 成交金额汇总
                //}
                var bizQty = Convert.ToDecimal(purOrdEntry["fbizqty"]);
                var distRate = 10;
                var distRateRaw = 10;
                var amount = price * bizQty;
                var dealPrice = price * distRate / 10;
                var dealAmount = dealPrice * bizQty;
                var distAmount = amount - dealAmount;

                distAmountSum += distAmount; // 折扣额汇总
                dealAmountSum += dealAmount; // 成交金额汇总

                // 货品原值汇总
                var isGiveaway = Convert.ToBoolean(item["fisgiveaway"]);
                if (isgiftdiscount)
                {
                    //汇总非赠品商品的货品原值
                    if (!isGiveaway)
                    {
                        faceAmountSum2 += amount;
                    }
                    faceAmountSum += amount;
                }
                else
                {
                    // 赠品不参与计算货品原值
                    if (!isGiveaway)
                    {
                        faceAmountSum += amount;
                    }
                }

                item["fprice"] = price;
                item["famount"] = amount;
                item["fdistrate"] = distRate;
                item["fdistrateraw"] = distRateRaw;
                item["fdealprice"] = dealPrice;
                item["fdealamount"] = dealAmount;
                item["fdistamount"] = distAmount;
                purOrdEntry["fqty"] = item["fqty"];
                purOrdEntry["fbizqty"] = item["fbizqty"];
                purOrdEntry["fresultbrandid"] = item["fresultbrandid"];
                purOrdEntry["fnote"] = item["fdescription"];

                //purOrdEntry["fmaterialid"] = productId;
                purOrdEntry["fmtrlimage"] = item["fmtrlimage"];
                purOrdEntry["fattrinfo"] = item["fattrinfo"];
                purOrdEntry["fcustomdes_e"] = item["fcustomdes_e"];
                purOrdEntry["funitid"] = item["funitid"];
                purOrdEntry["fbizunitid"] = productObj?["fsalunitid"] ?? ""; // 取商品的销售单位
                //purOrdEntry["fsupplierid"] = productObj?["fsupplierid"] ?? ""; // 取商品的供应商
                purOrdEntry["funstdtype"] = item["funstdtype"];

                // 套件组合、沙发组合、配件 相关字段
                purOrdEntry["fsuitproductid"] = item["fsuitproductid"];
                purOrdEntry["fsuitdescription"] = item["fsuitdescription"];
                purOrdEntry["fsuitcombnumber"] = item["fsuitcombnumber"];
                purOrdEntry["fpartscombnumber"] = item["fpartscombnumber"];
                purOrdEntry["fsubqty"] = item["fsubqty"];
                purOrdEntry["fforproductid"] = item["fforproductid"];
                purOrdEntry["fforsuiteselectionid"] = item["fforsuiteselectionid"];
                purOrdEntry["fdescription"] = item["description_suite"];
                purOrdEntry["fpackagedescription"] = item["packagedescription"];
                purOrdEntry["fsofacombnumber"] = item["fsofacombnumber"];
                purOrdEntry["fpartqty"] = item["fpartqty"];
                purOrdEntry["fparttype"] = item["fparttype"];
                purOrdEntry["fiscombmain"] = item["fiscombmain"];
                purOrdEntry["fisautopartflag"] = item["fisautopartflag"];
                purOrdEntry["fprodrequirement"] = item["fprodrequirement"];
                purOrdEntry["fselsuiterequire"] = item["fselsuiterequire"];

                #region 一级合同财务信息
                // 财务信息
                dataEntity["ffaceamount"] = faceAmountSum;
                dataEntity["fdistamount"] = distAmountSum;
                dataEntity["fdealamount"] = dealAmountSum;

                // 货款总折扣率
                var distSumRate = 0M;
                if (isgiftdiscount)
                {
                    //货款总折扣率 = 成交金额 / 非赠品商品的货品原值
                    distSumRate = faceAmountSum2 != 0 ? dealAmountSum / faceAmountSum2 : 1;
                }
                else
                {
                    // 货款总折扣率 = 成交金额 / 货品原值
                    distSumRate = faceAmountSum != 0 ? dealAmountSum / faceAmountSum : 1;
                }
                dataEntity["fdistsumrate"] = distSumRate;

                // 货款总折扣额 = 折扣额
                var distSumAmount = distAmountSum;
                dataEntity["fdistsumamount"] = distSumAmount;

                // 未收款 = 成交金额 + 费用收入 - 确认收款 - 申请退货金额
                var unReceived = dealAmountSum + 0 - Convert.ToDecimal(dataEntity["freceivable"]) - Convert.ToDecimal(dataEntity["frefundamount"]);
                dataEntity["funreceived"] = unReceived;

                // 订单总额 = 成交金额 + 费用收入
                var sumAmount = dealAmountSum + 0;
                dataEntity["fsumamount"] = sumAmount;
                #endregion
            }
            #region 反写二级采购财务信息和价格信息
            // 反写合同关联的二级经销商采购订单【总部合同状态】为“已终审”
            ResellerHelper.WriteBackPurchaseOrderPrice(
                this.OperationContext,
                new DynamicObject[] { dataEntity },
                changePurOrder);
            #endregion
            //二级合同
            var resellOrdCStatus = Convert.ToString(resellOrder?["fchangestatus"]);
            if (resellOrder != null && (resellOrdCStatus == "0" || resellOrdCStatus == "2"))
            {
                SetOrdChangeIngo(changePurOrder, resellOrder, dataEntity, currentAgent);
            }
        }

        /// <summary>
        /// 根据系列及二级分销商信息匹配销售系数
        /// </summary>
        /// <param name="fseriesid">系列id</param>
        /// <param name="currAgentInfo">二级分销商信息</param>
        /// <returns></returns>
        public decimal matchResellRatio(string fseriesid, DynamicObject currAgentInfo)
        {
            decimal resellRatio = 1;

            if (currAgentInfo == null)
            {
                return resellRatio;
            }
            var ratioInfo = currAgentInfo?["fratioinfo"] as DynamicObjectCollection;

            //拿系列匹配分销价格系数明细配置，匹配到则取对应系数，匹配不到取表头的系数计算
            var matchRatio = ratioInfo?.FirstOrDefault(x => !x["fseriesid"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(x["fseriesid"]) == fseriesid);
            if (matchRatio == null)
            {
                var ratio = Convert.ToDecimal(currAgentInfo["fresellratio"]);
                return ratio <= 0 ? 1 : ratio;
            }
            return Convert.ToDecimal(matchRatio["fresellratio_e"]);
        }

        /// <summary>
        /// 将变更后数据覆盖到下游二级合同
        /// </summary>
        /// <param name="changePurOrder"></param>
        /// <param name="resellOrder"></param>
        /// <param name="dataEntity"></param>
        private void SetOrdChangeIngo(DynamicObject changePurOrder, DynamicObject resellOrder, DynamicObject dataEntity, DynamicObject currentAgent)
        {
            // 经销商上下文
            var agentCtx = this.Context.CreateAgentDBContext(Convert.ToString(currentAgent["id"]));

            var profileService = this.Container.GetService<ISystemProfile>();

            // 销售合同赠品参与折扣计算
            var isgiftdiscount = profileService.GetSystemParameter(agentCtx, "bas_storesysparam", "fisgiftdiscount", false);

            var ordEntrys = resellOrder?["fentry"] as DynamicObjectCollection;
            var purOrdEntrys = changePurOrder["fentity"] as DynamicObjectCollection;
            var faceAmountSum = 0M;
            var faceAmountSum2 = 0M;
            var distAmountSum = 0M;
            var dealAmountSum = 0M;
            //分销商合同新增商品行要取的总部零售价集合
            List<JToken> hqPrices = new List<JToken>();
            // 将销售合同中存在但采购订单中不存在的明细行删除
            if (ordEntrys.Any() && purOrdEntrys.Any())
            {
                var beRemoves = new List<DynamicObject>();
                foreach (var ordEntry in ordEntrys)
                {
                    var ordEntryId = Convert.ToString(ordEntry["id"]);
                    if (!purOrdEntrys.Any(o => Convert.ToString(o["fsourceentryid_e"]).EqualsIgnoreCase(ordEntryId)))
                    {
                        beRemoves.Add(ordEntry);
                    }
                }
                foreach (var item in beRemoves)
                {
                    ordEntrys.Remove(item);
                }
            }
            foreach (var item in purOrdEntrys)
            {
                var purOrdEntryId = Convert.ToString(item["id"]);
                var sourceEntryId = Convert.ToString(item["fsourceentryid_e"]);
                // 不存在则新增【且重新定价合同价格为分销价等】，已存在则覆盖
                var ordEntry = ordEntrys.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(sourceEntryId));
                // 商品信息
                var productId = Convert.ToString(item["fmaterialid"]);
                var bizQty = Convert.ToDecimal(item["fbizqty"]);
                var oneOrderEntry = (dataEntity["fentry"] as DynamicObjectCollection)?.FirstOrDefault(o => !o["fsourceentryid_e"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(o["fsourceentryid_e"]) == purOrdEntryId);
                var price = 0.00M;
                var distRateRaw = 10;
                if (ordEntry == null)
                {
                    ordEntry = new DynamicObject(ordEntrys.DynamicCollectionItemPropertyType);
                    ordEntrys.Add(ordEntry);
                    ordEntry["fproductid"] = productId;
                    ordEntry["fmtono"] = item["fmtono"];
                    //ordEntry["fcustomerid"] = item["fcustomer"];
                    ordEntry["fexdeliverydate"] = item["fdemanddate"];
                    //ordEntry["fphone"] = item["fphone"];
                    ordEntry["fmtrlimage"] = item["fmtrlimage"];
                    ordEntry["fresultbrandid"] = item["fresultbrandid"];
                    ordEntry["fclosestatus_e"] = item["fclosestatus_e"];
                    ordEntry["fstockstatus"] = KCZT_SYS_01; // 库存状态默认为“可用”
                    if (Convert.ToBoolean(item["funstdtype"]))
                    {
                        //////'01':'新建','02':'待审批','03':'审批通过','04':'驳回','05':'待定价','06':'终审'
                        //if (Convert.ToString(item["funstdtypestatus"]) != "01")
                        //{
                        ordEntry["funstdtypestatus"] = item["funstdtypestatus"];
                        ordEntry["funstdtypecomment"] = item["funstdtypecomment"];
                        //}
                    }

                    defCalService.Execute(this.Context, this.HtmlForm, new DynamicObject[] { resellOrder });
                    preSaveService.PrepareDataEntity(this.Context, this.HtmlForm, new DynamicObject[] { resellOrder }, this.Option);
                    // 源单信息
                    item["fsourceformid"] = "ydj_order";
                    item["fsourcebillno"] = resellOrder["fbillno"];
                    item["fsourceentryid_e"] = ordEntry["id"];
                    item["fsourceinterid"] = resellOrder["id"];
                    item["fsoorderno"] = resellOrder["fbillno"];
                    item["fsoorderinterid"] = resellOrder["id"];
                    item["fsoorderentryid"] = ordEntry["id"];
                }
                else
                {
                    price = Convert.ToDecimal(ordEntry["fprice"]);
                    distRateRaw = Convert.ToInt32(ordEntry["fdistrateraw"]);
                }
                //else
                //{
                //    ordEntry["fdescription"] = item["fdescription"];
                //    //var amount = Convert.ToDecimal(item["fprice"]) * Convert.ToDecimal(item["fbizqty"]);
                //    //faceAmountSum += amount; // 货品原值汇总
                //    //distAmountSum += amount - Convert.ToDecimal(item["fdistamount"]); // 折扣额汇总
                //    //dealAmountSum += Convert.ToDecimal(item["fdealamount"]); // 成交金额汇总
                //}
                ordEntry["fdescription"] = item["fnote"];
                var distRate = Convert.ToDecimal(item["fdistrate"]);
                var amount = price * bizQty;
                var dealPrice = price * distRate / 10;
                var dealAmount = dealPrice * bizQty;
                var distAmount = amount - dealAmount;

                distAmountSum += distAmount; // 折扣额汇总
                dealAmountSum += dealAmount; // 成交金额汇总

                // 货品原值汇总
                var isGiveaway = Convert.ToBoolean(ordEntry["fisgiveaway"]);
                if (isgiftdiscount)
                {
                    //汇总非赠品商品的货品原值
                    if (!isGiveaway)
                    {
                        faceAmountSum2 += amount;
                    }
                    faceAmountSum += amount;
                }
                else
                {
                    // 赠品不参与计算货品原值
                    if (!isGiveaway)
                    {
                        faceAmountSum += amount;
                    }
                }

                ordEntry["fqty"] = item["fqty"];
                ordEntry["fbizqty"] = bizQty;
                ordEntry["fprice"] = price;
                ordEntry["famount"] = amount;
                ordEntry["fdistrate"] = distRate;
                ordEntry["fdistrateraw"] = distRateRaw;
                ordEntry["fdealprice"] = dealPrice;
                ordEntry["fdealamount"] = dealAmount;
                ordEntry["fdistamount"] = distAmount;
                ordEntry["fresultbrandid"] = item["fresultbrandid"];

                ordEntry["fsupplierid"] = oneOrderEntry["fsupplierid"] ?? ""; // 取商品的供应商
                ordEntry["funstdtype"] = item["funstdtype"];

                ordEntry["fownertype"] = item["fownertype"];

                ordEntry["fownerid"] = item["fownerid"];
                ordEntry["fattrinfo"] = item["fattrinfo"];
                ordEntry["fcustomdes_e"] = item["fcustomdes_e"];
                ordEntry["funitid"] = item["funitid"];
                ordEntry["fmulfile"] = item["fmulfile"];
                ordEntry["fbizunitid"] = item["fbizunitid"];
                ordEntry["fstaffid"] = item["fentrystaffid"];

                // 套件组合、沙发组合、配件 相关字段
                ordEntry["fsuitproductid"] = item["fsuitproductid"];
                ordEntry["fsuitdescription"] = item["fsuitdescription"];
                ordEntry["fsuitcombnumber"] = item["fsuitcombnumber"];
                ordEntry["fpartscombnumber"] = item["fpartscombnumber"];
                ordEntry["fsubqty"] = item["fsubqty"];
                ordEntry["fforproductid"] = item["fforproductid"];
                ordEntry["fforsuiteselectionid"] = item["fforsuiteselectionid"];
                ordEntry["packagedescription"] = item["fpackagedescription"];
                ordEntry["fsofacombnumber"] = item["fsofacombnumber"];
                ordEntry["fpartqty"] = item["fpartqty"];
                ordEntry["fparttype"] = item["fparttype"];
                ordEntry["fiscombmain"] = item["fiscombmain"];
                ordEntry["fisautopartflag"] = item["fisautopartflag"];
                ordEntry["fprodrequirement"] = item["fprodrequirement"];
                ordEntry["fselsuiterequire"] = item["fselsuiterequire"];

                #region 二级合同财务信息
                // 财务信息
                resellOrder["ffaceamount"] = faceAmountSum;
                resellOrder["fdistamount"] = distAmountSum;
                resellOrder["fdealamount"] = dealAmountSum;

                // 货款总折扣率
                var distSumRate = 0M;
                if (isgiftdiscount)
                {
                    //货款总折扣率 = 成交金额 / 非赠品商品的货品原值
                    distSumRate = faceAmountSum2 != 0 ? dealAmountSum / faceAmountSum2 : 1;
                }
                else
                {
                    // 货款总折扣率 = 成交金额 / 货品原值
                    distSumRate = faceAmountSum != 0 ? dealAmountSum / faceAmountSum : 1;
                }
                resellOrder["fdistsumrate"] = distSumRate;

                // 货款总折扣额 = 折扣额
                var distSumAmount = distAmountSum;
                resellOrder["fdistsumamount"] = distSumAmount;

                // 未收款 = 成交金额 + 费用收入 - 确认收款 - 申请退货金额
                var unReceived = dealAmountSum + 0 - Convert.ToDecimal(resellOrder["freceivable"]) - Convert.ToDecimal(resellOrder["frefundamount"]);
                resellOrder["funreceived"] = unReceived;

                // 订单总额 = 成交金额 + 费用收入
                var sumAmount = dealAmountSum + 0;
                resellOrder["fsumamount"] = sumAmount;

                //二级分销：把二级销售合同【成交单价】【成交金额】携带至一级销售合同【终端零售价】【终端金额】,【终端金额】根据数量计算
                var onebizQty = Convert.ToDecimal(oneOrderEntry["fbizqty"]);
                var fterprice = ordEntry?["fdealprice"] ?? 0;
                oneOrderEntry["fterprice"] = fterprice;
                oneOrderEntry["fteramount"] = Convert.ToDecimal(fterprice) * onebizQty;
                #endregion
            }
        }

        /// <summary>
        /// 批量加载采购订单商品信息
        /// </summary>
        /// <param name="purOrderDatas">采购订单数据包</param>
        /// <returns>商品信息</returns>
        private DynamicObjectCollection LoadProducts(DynamicObject[] purOrderDatas)
        {
            // 所有的商品ID
            var productIds = purOrderDatas.SelectMany(o =>
            {
                var entrys = o["fentry"] as DynamicObjectCollection;
                var _productIds = entrys
                    .Select(entry => Convert.ToString(entry["fproductid"]))
                    .Where(productId => !productId.IsNullOrEmptyOrWhiteSpace());
                return _productIds;
            })
            .Distinct().ToList();

            if (!productIds.Any()) return null;

            // 批量加载商品信息
            var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fsupplierid,fsalunitid");

            return productObjs;
        }
        /// <summary>
        /// 批量加载一级经销商定义的商品二级分销价格
        /// </summary>
        /// <param name="purOrderDatas">采购订单数据包</param>
        /// <returns>商品二级分销价格</returns>
        private List<JObject> LoadProductResellerPrice(DynamicObject[] orderDatas, List<DynamicObject> purOrders, List<DynamicObject> currentAgentCus)
        {
            if (orderDatas == null || !orderDatas.Any()) return new List<JObject>();
            // 取价参数
            var productInfos = new JArray();

            foreach (var orderData in orderDatas)
            {
                var orderEntrys = orderData["fentry"] as DynamicObjectCollection;
                if (orderEntrys.Any(x => x["fsourceentryid_e"].IsNullOrEmptyOrWhiteSpace()))
                {
                    var orgid = purOrders.FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(orderData["fsourceid"]));
                    var currAgent = currentAgentCus.FirstOrDefault(x => Convert.ToString(x["forgid"]) == Convert.ToString(orgid["fmainorgid"]));
                    foreach (var orderEntry in orderEntrys)
                    {
                        productInfos.Add(new JObject
                        {
                            ["clientId"] = orderEntry["id"] as string,
                            ["productId"] = orderEntry["fproductid"] as string,
                            ["bizDate"] = DateTime.Now,
                            ["customerId"] = Convert.ToString(currAgent["fid"]),
                            ["attrInfo"] = new JObject
                            {
                                ["id"] = orderEntry["fattrinfo"] as string,
                                ["entities"] = new JArray()
                            }
                        });
                    }
                }
            }

            if (productInfos.Any())
            {
                var priceService = this.Container.GetService<IPriceService>();
                var priceList = priceService.GetResellerPrice(this.Context, productInfos);

                return priceList;
            }
            else
            {
                return new List<JObject>();
            }
        }
        /// <summary>
        /// 批量加载商品总部采购价格
        /// </summary>
        /// <param name="purOrderDatas"></param>
        /// <returns></returns>
        private List<JToken> LoadProductStandPrice(DynamicObject[] orderDatas, List<DynamicObject> purOrders, List<DynamicObject> currentAgentCus)
        {
            if (orderDatas == null || !orderDatas.Any()) return new List<JToken>();
            // 取价参数
            var productInfos = new JArray();

            foreach (var orderData in orderDatas)
            {
                var orderEntrys = orderData["fentry"] as DynamicObjectCollection;
                if (orderEntrys.Any(x => x["fsourceentryid_e"].IsNullOrEmptyOrWhiteSpace()))
                {
                    //这里暂时只获取标准品的，后续要开放其他可对应处理
                    foreach (var orderEntry in orderEntrys.Where(x => !Convert.ToBoolean(x["funstdtype"]) && x["fattrinfo"].IsNullOrEmptyOrWhiteSpace()))
                    {
                        var orgid = purOrders.FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(orderData["fsourceid"]));
                        var currAgent = currentAgentCus.FirstOrDefault(x => Convert.ToString(x["forgid"]) == Convert.ToString(orgid["fmainorgid"]));
                        productInfos.Add(new JObject
                        {
                            ["clientId"] = orderEntry["id"] as string,
                            ["productId"] = orderEntry["fproductid"] as string,
                            ["bizDate"] = DateTime.Now,
                            //["supplierId"] = purOrderData["fsupplierid"] as string,
                            ["customerId"] = Convert.ToString(currAgent["fid"]),
                            ["attrInfo"] = new JObject
                            {
                                ["id"] = orderEntry["fattrinfo"] as string,
                                ["entities"] = new JArray()
                            },
                            ["isHqPrice"] = true,
                            ["isReHqPurPrice"] = true,
                            ["supplierNotFilter"] = true//直接取最新采购价，忽略供应商取最新总部价目
                        });
                    }
                }
            }

            //var postData = new Dictionary<string, object>
            //    {
            //        { "priceFlag",4},
            //        { "modeType",1},
            //        { "idType","localDataId"},
            //        { "hopCount",0},
            //        { "productInfos",productInfos}
            //    };
            List<JToken> srvData = new List<JToken>();
            if (productInfos.Any())
            {
                ////获取总部供应商，后取总部价格
                //var hqSupplier = this.Context.ExecuteDynamicObject($"select top 1 fid from T_YDJ_SUPPLIER where fmainorgid='{Context.TopCompanyId}'",null)?.FirstOrDefault()["fid"];
                //foreach (var item in productInfos)
                //{
                //    item["supplierId"] = Convert.ToString(hqSupplier);
                //}

                IPriceService priceService = this.Container.GetService<IPriceService>();
                srvData = priceService.GetPrice(this.Context, 4, productInfos);
                //var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_purchaseprice", purOrderDatas, "getprices", postData);
                //result.ThrowIfHasError(true, "获取价目失败!");
            }
            return srvData;
        }

        /// <summary>
        /// 批量加载商品总部零售价
        /// </summary>
        /// <param name="purOrderDatas"></param>
        /// <returns></returns>
        private List<JToken> LoadProductHqPrice(List<DynamicObject> orderDatas)
        {
            if (orderDatas == null || !orderDatas.Any()) return new List<JToken>();
            // 取价参数
            var productInfos = new JArray();

            foreach (var orderData in orderDatas)
            {
                var orderEntrys = orderData["fentry"] as DynamicObjectCollection;
                foreach (var orderEntry in orderEntrys.Where(x => Convert.ToDecimal(x["fprice"]) == 0))
                {
                    productInfos.Add(new JObject
                    {
                        ["clientId"] = orderEntry["id"] as string,
                        ["productId"] = orderEntry["fproductid"] as string,
                        ["bizDate"] = DateTime.Now,
                        ["fbizunitid"] = orderEntry["fbizunitid"] as string,
                        ["customerId"] = Convert.ToString(orderData["fmainorgid"]),
                        ["attrInfo"] = new JObject
                        {
                            ["id"] = orderEntry["fattrinfo"] as string,
                            ["entities"] = new JArray()
                        },
                        ["isHqPrice"] = true
                    });
                }
            }
            List<JToken> srvData = new List<JToken>();
            if (productInfos.Any())
            {
                IPriceService priceService = this.Container.GetService<IPriceService>();
                srvData = priceService.GetPrice(this.Context, 1, productInfos);
                //var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_purchaseprice", purOrderDatas, "getprices", postData);
                //result.ThrowIfHasError(true, "获取价目失败!");
            }
            return srvData;
        }

        /// <summary>
        /// 加载当前当前二级经销商对应的客户ID
        /// </summary>
        private List<DynamicObject> LoadCurrentAgentCustomerId(List<string> orgids)
        {
            var sqlText = $@"
            select top 1 forgid,fid,fname,fphone,fprovince,fcity,fregion,faddress 
            from t_ydj_customer with(nolock) 
            where forgid in('{string.Join("','", orgids)}')";

            return this.DBService.ExecuteDynamicObject(this.Context, sqlText)?.ToList();
        }
        private void InitService()
        {
            if (defCalService == null)
            {
                // 填充字段默认值
                defCalService = this.Container.GetService<IDefaultValueCalculator>();
            }
            if (preSaveService == null)
            {
                // 保存前预处理
                preSaveService = this.Container.GetService<IPrepareSaveDataService>();
            }
            if (purOrderForm == null)
            {
                purOrderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_purchaseorder");
            }
        }
        //public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        //{
        //    base.PrepareBusinessServices(e);

        //    var opt = this.OperationContext.Option;
        //    opt.SetVariableValue("SyncMode", MuSi.Enums.Enu_MuSiSyncMode.MQ);
        //}
        /// <summary>
        /// 审核时根据 辅助属性给【初始辅助属性】赋值
        /// </summary>
        /// <param name="dataEntities"></param> 

        /// <summary>
        /// 反写采购订单销售价
        /// </summary>
        /// <param name="dataEntities"></param>
        private void rewritePurSalPrice(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }
            var swjDataEntities = dataEntities.Where(a => Convert.ToString((a["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("v6定制柜合同"));

            if (swjDataEntities == null || swjDataEntities.Count() <= 0)
            {
                return;
            }
            var ids = swjDataEntities.Select(a => Convert.ToString(a["id"])).ToList();

            var strSql = $@"select a.fid from T_YDJ_PURCHASEORDER as a inner join T_YDJ_POORDERENTRY as b on a.fid=b.fid where fsourceinterid in ('{string.Join("','", ids)}') and a.fcancelstatus=0 ";
            List<string> fids = new List<string>();
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    fids.Add(dr["fid"].ToString());
                }
            }
            if (fids.Count > 0)
            {
                var purOrders = this.Context.LoadBizDataById("ydj_purchaseorder", fids);
                foreach (var dataEntity in swjDataEntities)
                {
                    var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                    foreach (var entryItem in entrys)
                    {
                        foreach (var purOrderItem in purOrders)
                        {
                            var purEntrys = purOrderItem["fentity"] as DynamicObjectCollection;
                            var purEntryItems = purEntrys.Where(a => Convert.ToString(a["fsoorderentryid"]).Equals(Convert.ToString(entryItem["Id"]))).ToList();
                            if (purEntryItems != null && purEntryItems.Count > 0)
                            {
                                purEntryItems.ForEach(a => a["fsalprice"] = entryItem["fprice"]);
                            }
                        }
                    }
                }
                var metaModelService = this.Container.GetService<IMetaModelService>();
                var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_purchaseorder");
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                dm.Save(purOrders);
            }
        }


        /// <summary>
        /// 赋值一件代发逻辑
        /// </summary>
        /// <param name="order"></param>
        /// <param name="fentrylist"></param>
        /// <exception cref="BusinessException"></exception>
        private void SetPiecesTag(DynamicObject order, DynamicObjectCollection fentrylist)
        {
            if (fentrylist == null || fentrylist.Count <= 0) return;
            bool hasPartialtype1 = fentrylist.Any(a => Convert.ToString(a["fdeliverytype"]) == "delivery_type_02");
            bool hasPartialtype2 = fentrylist.Any(a => Convert.ToString(a["fdeliverytype"]) == "delivery_type_01");
            if (hasPartialtype1 && hasPartialtype2)
            {
                throw new BusinessException("直营销售订单商品行需按交货方式分开录单，门店仓出现货与总部直发不可混录。");
            }
            // 判断所有商品明细行【出现货】都未勾选
            //bool allNotOutSpot = fentrylist.All(x => !Convert.ToBoolean(x["fisoutspot"]));

            //if (allNotOutSpot && fmanagemodel == 1 && ftoppiecesendtag == 1)
            //{
            //    foreach (var entry in fentrylist)
            //    {
            //        entry["fdeliverytype"] = "delivery_type_02"; // 门店直发
            //    }
            //}
            //else if (!allNotOutSpot && fmanagemodel == 1 && ftoppiecesendtag == 1)
            //{
            //    // 自动勾选一件代发
            //    order["fpiecesendtag"] = true;
            //    // 所有明细行交货方式赋值为总部直发
            //    foreach (var entry in fentrylist)
            //    {
            //        entry["fdeliverytype"] = "delivery_type_01"; // 总部直发
            //    }
            //}


            if (fmanagemodel == 1 && ftoppiecesendtag == 1)
            {
                var billTypeName = Convert.ToString((order["fbilltype_ref"] as DynamicObject)?["fname"]);
                // 判断所有商品明细行【出现货】都未勾选
                bool allNotOutSpot = fentrylist.All(x => !Convert.ToBoolean(x["fisoutspot"]));
                bool allOutSpot = fentrylist.All(x => Convert.ToBoolean(x["fisoutspot"]));
                if (billTypeName.Equals("门店上样")) return;

                bool frenewalflag = Convert.ToBoolean(order["frenewalflag"]);
                if (frenewalflag)
                {
                    if (frenewalflag && allNotOutSpot)
                    {
                        // 自动勾选一件代发
                        order["fpiecesendtag"] = true;
                        // 所有明细行交货方式赋值为总部直发
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = "delivery_type_01"; // 总部直发
                        }
                    }
                    else if (frenewalflag && allOutSpot)
                    {
                        // 自动勾选一件代发
                        //order["fpiecesendtag"] = false;
                        // 78211 慕思直营项目-主任务 / 慕思直营-焕新订单开单流程改造-开发 --焕新订单的都走一键代发
                        order["fpiecesendtag"] = true;
                        // 所有明细行交货方式赋值为门店直发
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = "delivery_type_02"; // 门店直发
                        }
                    }
                    else
                    {
                        // 
                        order["fpiecesendtag"] = false;
                        // 所有明细行交货方式赋值为
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = ""; // 
                        }
                    }
                }
                else if (!billTypeName.Equals("门店上样"))
                {
                    if (allNotOutSpot)
                    {
                        // 自动勾选一件代发
                        order["fpiecesendtag"] = true;
                        // 所有明细行交货方式赋值为总部直发
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = "delivery_type_01"; // 总部直发
                        }
                    }
                    else if (allOutSpot)
                    {
                        // 自动勾选一件代发
                        order["fpiecesendtag"] = false;
                        // 所有明细行交货方式赋值为总部直发
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = "delivery_type_02"; // 总部直发
                        }
                    }
                    else
                    {
                        // 
                        order["fpiecesendtag"] = false;
                        // 所有明细行交货方式赋值为
                        foreach (var entry in fentrylist)
                        {
                            entry["fdeliverytype"] = ""; // 
                        }
                    }
                }
                if (allOutSpot == false && allNotOutSpot == false)
                {
                    throw new BusinessException("直营销售订单商品行需按交货方式分开录单，门店仓出现货与总部直发不可混录。");
                }

                var _entry = order["fentry"] as DynamicObjectCollection;
                if (_entry != null && Convert.ToBoolean(order["fpiecesendtag"]))
                {
                    foreach (var item in _entry)
                    {
                        var bizqty = Convert.ToDecimal(item["fbizqty"]);
                        var fseq = Convert.ToDecimal(item["fseq"]);
                        var fdeliverytype = Convert.ToString(item["fdeliverytype"]);
                        if (fdeliverytype.Equals("delivery_type_01"))
                        {
                            var matNumber = Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fnumber"]);
                            var packqty = Convert.ToDecimal((item["fproductid_ref"] as DynamicObject)?["fpackqty"]);
                            if (packqty > 1)
                            {
                                if (bizqty % packqty > 0)
                                {
                                    throw new BusinessException($@"销售合同{order["fbillno"]},第[{fseq}]行商品[{matNumber}]属于箱类商品，交货方式为“总部直发”，但是销售数量不满足整箱倍数，不允许一件代发操作，请核查！");
                                }
                            }
                        }
                    }
                }
            }
            else if (fmanagemodel == 1)
            {
                // 直营组织下，当【创建单据所属组织.经营类型=直营】，并且【焕新订单标记=勾选】，并且【焕新订单类型.所属组织名称≠当前创建单据组织（组织不一致）】，则在点击<审核>时，自动赋值“一件代发”逻辑，
                bool frenewalflag = Convert.ToBoolean(order["frenewalflag"]);
                if (frenewalflag)
                {
                    var reNewType = Convert.ToString(order["frenewtype"]);
                    if (!reNewType.IsNullOrEmptyOrWhiteSpace())
                    {
                        var isContainsReNewType = order.DynamicObjectType.Properties.ContainsKey("frenewtype_ref");
                        DynamicObject getReNewTypeDy = null;
                        if (isContainsReNewType)
                        {
                            getReNewTypeDy = order["frenewtype_ref"] as DynamicObject;
                            if (getReNewTypeDy == null)
                            {
                                getReNewTypeDy = this.Context.LoadBizBillHeadDataById("ydj_renewtype",reNewType,"fbizorgid,fisincome");
                            }
                        }
                        if (getReNewTypeDy != null)
                        {
                            // 直营组织下，当【创建单据所属组织.经营类型=直营】，并且【焕新订单标记=勾选】，并且【焕新订单类型.所属组织名称≠当前创建单据组织（组织不一致）】，则在点击<审核>时，自动赋值“一件代发”逻辑，
                            var bizOrgId = Convert.ToString(getReNewTypeDy["fbizorgid"]);
                            if (!bizOrgId.Equals(this.Context.Company))
                            {
                                order["fpiecesendtag"] = true;

                                var productEntrys = order["fentry"] as DynamicObjectCollection;
                                
                                // 判断所有商品明细行【出现货】都未勾选
                                bool allNotOutSpot = productEntrys.All(x => !Convert.ToBoolean(x["fisoutspot"]));
                                bool allOutSpot = productEntrys.All(x => Convert.ToBoolean(x["fisoutspot"]));
                                if (allNotOutSpot)
                                {
                                    // 所有明细行交货方式赋值为总部直发
                                    foreach (var entry in fentrylist)
                                    {
                                        entry["fdeliverytype"] = "delivery_type_01"; // 总部直发
                                    }
                                }

                                if (allOutSpot)
                                {
                                    // 所有明细行交货方式赋值为门店直发
                                    foreach (var entry in fentrylist)
                                    {
                                        entry["fdeliverytype"] = "delivery_type_02"; // 门店直发
                                    }
                                }
                                
                            }
                        }
                    }
                    
                    var _entry = order["fentry"] as DynamicObjectCollection;
                    if (_entry != null && Convert.ToBoolean(order["fpiecesendtag"]))
                    {
                        foreach (var item in _entry)
                        {
                            var bizqty = Convert.ToDecimal(item["fbizqty"]);
                            var fseq = Convert.ToDecimal(item["fseq"]);
                            var fdeliverytype = Convert.ToString(item["fdeliverytype"]);
                            if (fdeliverytype.Equals("delivery_type_01"))
                            {
                                var matNumber = Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fnumber"]);
                                var packqty = Convert.ToDecimal((item["fproductid_ref"] as DynamicObject)?["fpackqty"]);
                                if (packqty > 1)
                                {
                                    if (bizqty % packqty > 0)
                                    {
                                        throw new BusinessException($@"销售合同{order["fbillno"]},第[{fseq}]行商品[{matNumber}]属于箱类商品，交货方式为“总部直发”，但是销售数量不满足整箱倍数，不允许一件代发操作，请核查！");
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }
    }
}