using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：编辑
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("modify")]
    public class Modify : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "aftercreateuidata":
                    this.ProcAfterCreateUiData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理UI数据打包逻辑
        /// </summary>
        /// <param name="e"></param>
        private void ProcAfterCreateUiData(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as JObject;
            if (eventData == null) return;

            //销售合同实退不体现未收
            var spService = this.Container.GetService<ISystemProfile>();
            var dontReflect = spService.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fdontreflect");
            eventData["fdontreflect"] = dontReflect;

            var status = eventData["fstatus"].GetJsonValue("id", "");
            //已审核、已提交的历史数据不更新部门关联门店
            if (status != "D" && status != "E") {
                //修改根据部门加载其最新关联门店
                var fdeptid = Convert.ToString(eventData["fdeptid"].GetJsonValue<string>("id"));
                var fstoreid = Convert.ToString(this.Context.LoadBizBillHeadDataById("ydj_dept", fdeptid, "fstore")?["fstore"]);
                if (!fstoreid.IsNullOrEmptyOrWhiteSpace())
                {
                    var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bas_store");
                    var dm = this.GetDataManager();
                    dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                    var supplier = dm.Select(fstoreid) as DynamicObject;
                    if (supplier != null)
                    {
                        JObject joStore = new JObject();
                        joStore["id"] = supplier["id"] as string;
                        joStore["fnumber"] = supplier["fnumber"] as string;
                        joStore["fname"] = supplier["fname"] as string;
                        eventData["fstore"] = joStore;
                    }
                }
            }

            var fchangestatus = eventData["fchangestatus"].GetJsonValue("id", "");
            if (fchangestatus.EqualsIgnoreCase("1") || fchangestatus.EqualsIgnoreCase("3"))
            {
                var id = eventData.GetJsonValue("id", "");
                var order = this.Context.LoadBizDataById(this.HtmlForm.Id, id);
                var orderEntrys = (DynamicObjectCollection)order["fentry"];

                var transferData = this.Container.GetService<IOrderService>()
                    .ExistsInventoryTransferBill(this.Context, orderEntrys).Select(s => new
                    {
                        fbillno = Convert.ToString(s["fbillno"]),
                        forderid = Convert.ToString(s["forderid"]),
                        forderenid = Convert.ToString(s["forderenid"])
                    }).ToList();

                eventData["inventoryTransfers"] = JArray.FromObject(transferData);
            }
        }
    }
}