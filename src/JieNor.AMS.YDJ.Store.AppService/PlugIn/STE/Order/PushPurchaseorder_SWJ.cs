using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using Newtonsoft.Json;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore;
using JieNor.Framework.Enums;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("pushpurorderswj")]
    public class PushPurchaseorder_SWJ : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 下推的采购订单
        /// </summary>
        private List<DynamicObject> PushPurchaseOrders = new List<DynamicObject>();

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype", "fproductid" });
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            //if (selectRowIds == null || selectRowIds.Count == 0) return;
            e.Rules.Add(new Pur.PurchaseOrder.Validation_Save());

            string errorMsg = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                    var entry = newData["fentry"] as DynamicObjectCollection;
                    foreach (var item in entry)
                    {
                        if (selectRowIds.Any(a => a.Id == Convert.ToString(item["id"])))
                        {
                            if (Convert.ToBoolean(item["faftserviceisfree"]))
                            {
                                errorMsg = $"第{Convert.ToString(item["fseq"])}行商品【{Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fname"])}】售后免费，不允许转采购，有疑问请联系总部定制柜售后人员，谢谢！";
                                return false;
                            }
                    }
                    else
                    {
                        if (Convert.ToBoolean(item["faftserviceisfree"]))
                        {
                            errorMsg = $"第{Convert.ToString(item["fseq"])}行商品【{Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fname"])}】售后免费，不允许转采购，有疑问请联系总部定制柜售后人员，谢谢！";
                            return false;
                        }
                    }
                    
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            var IsRenewalPur = this.GetQueryOrSimpleParam<bool>("IsRenewalPur", false);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!IsRenewalPur && Convert.ToBoolean(newData["frenewalflag"]))
                {
                    return false;
                }
                return true;
            }).WithMessage("销售合同【{0}】的【焕新订单标记】为是，不允许操作。", (billObj, propObj) => billObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToBoolean(newData["fpiecesendtag"]))
                {
                    return false;
                }

                return true;
            }).WithMessage("当前销售合同【{0}】属于一件代发合同，不允许正常采购操作，请走一件代发流程，谢谢！", (billObj, propObj) => billObj["fbillno"]));

        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;


            //销售合同允许采购的最低金额比例
            var profileService = this.Context.Container.GetService<ISystemProfile>();
            var systemParameter = profileService.GetSystemParameter(this.Context, "bas_storesysparam");
            //var fproportionbuyeramount = Convert.ToInt32(systemParameter["fproportionbuyeramount"]);
            var billTypeService = this.Container.GetService<IBillTypeService>();
            //单据类型
            //var billData = GetBilltype();
            var MSRenewalNotify = this.GetQueryOrSimpleParam<bool>("MSRenewalNotify", false);
            var IsRenewalPur = this.GetQueryOrSimpleParam<bool>("IsRenewalPur", false);
            foreach (var dataEntity in e.DataEntitys)
            {
                //焕新订单合同接收通知操作自动生成收支记录并提交审核，不需要校验金额！
                if (!MSRenewalNotify && !IsRenewalPur)
                {
                    var fproportionbuyeramount = 0;
                    var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, this.HtmlForm, Convert.ToString(dataEntity["fbilltype"]));
                    if (paramSetObj != null)
                    {
                        int.TryParse(Convert.ToString(paramSetObj["fproportionbuyeramount"]), out fproportionbuyeramount);
                    }
                    //校验销售合同允许出库
                    var freceivable = dataEntity["freceivable"];
                    var fsumamount = dataEntity["fsumamount"];

                    //判断是否允许采购
                    //#37762改为订单总额-申请退货金额
                    var newfsumamount = (Convert.ToDouble(fsumamount) - Convert.ToDouble(dataEntity["frefundamount"])) * fproportionbuyeramount / 100;
                    //过滤 单据类型 = "销售转单"
                    //var orderids = billData.Where(x => Convert.ToString(x["fname"]) == "销售转单").Select(x => Convert.ToString(x["id"])).ToList();
                    if (Convert.ToDouble(freceivable) < newfsumamount)
                        throw new BusinessException("当前订单确认已收金额不足" + newfsumamount + "元，暂不允许下采购！");
                }

				//验证合同下推采购
				new OrderCommon(this.Context).CheckPurOrder(profileService, dataEntity);
            }
        }


        /// <summary>
        /// 过滤商品
        /// </summary>
        private void FilterProduct(DynamicObjectCollection fentry, List<string> purzeor, Dictionary<string, string> errorDic, bool foutspotnopur, List<string> noCanPurProductIds, List<Row> rowIds, string orderBillNo, bool isfsuperpinpurchase)
        {
            foreach (var item in fentry)
            {
                var key = Convert.ToString(item["id"]);
                var closestatus = Convert.ToString(item["fclosestatus_e"]);
                var fomsprogress = 0;//定制订单进度
                int.TryParse(Convert.ToString(item["fomsprogress"]), out fomsprogress);
                //过滤掉非勾选明细行
                if (rowIds.Count > 0 && !rowIds.Select(x => x.Id.ToString()).ToList().Contains(key))
                {
                    purzeor.Add(key);
                    continue;
                }


                //if (fentry.Count(x => x["ftransferorderstatus"].ToString() != string.Empty && x["fshipperagentid"].ToString() == Context.Company) > 0
                //    && item["fshipperagentid"].ToString() != this.Context.Company)
                //{
                //    errorDic[Guid.NewGuid().ToString("N")] = "第 " + Convert.ToString(item["fseq"]) + " 行 商品 :" + JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + " 发货经销商非等于当前登录人,不允许下推！！";
                //    purzeor.Add(key);
                //}
                //isfsuperpinpurchase 允许超销售数量采购时 不需要校验采购数量
                if (Convert.ToInt32(item["fqty"]) <= Convert.ToInt32(item["fpurqty"]) && !isfsuperpinpurchase)
                {
                    errorDic[Guid.NewGuid().ToString("N")] = "第 " + Convert.ToString(item["fseq"]) + " 行 商品 :" + JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + " 采购数量大于等于销售数量,不允许下推！";
                    purzeor.Add(key);
                }

                if (!(closestatus == "0" ||
                    closestatus == "2" ||
                    closestatus == " "))//只有正常\部分关闭\空才能下推，其它过滤掉
                {
                    errorDic[Guid.NewGuid().ToString("N")] = "第 " + Convert.ToString(item["fseq"]) + " 行" + JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + "商品" + Convert.ToString(item["fseq"]) + GetCloseStatusStr(closestatus) + "，不能下推采购！";
                    purzeor.Add(key);
                }
                if (foutspotnopur && Convert.ToBoolean(item["fisoutspot"]))//出现货商品无需采购
                {
                    errorDic[Guid.NewGuid().ToString("N")] = "第 " + Convert.ToString(item["fseq"]) + " 行" + JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + "商品出现货，不允许采购！";
                    purzeor.Add(key);
                }
                if (noCanPurProductIds.Contains(item["fproductid"]))//商品授权清单中定义的不可采购商品
                {
                    //errorDic[key] = "商品【{0} {1}】为历史商品，总部未授权，不可采购！ ".Fmt((item["fproductid_ref"] as DynamicObject)?["fnumber"], (item["fproductid_ref"] as DynamicObject)?["fname"]);
                    errorDic[Guid.NewGuid().ToString("N")] = "第 " + Convert.ToString(item["fseq"]) + " 行" + JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + " 在商品授权清单中定义的不可采购商品，不允许采购！";
                    purzeor.Add(key);
                }
                //oms 需要跳过[定制订单进度]=“单据作废”的商品行
                if (fomsprogress == -1)
                {
                    errorDic[Guid.NewGuid().ToString("N")] = "第 " + Convert.ToString(item["fseq"]) + " 行" + JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + "已作废的商品不允许转采购！";
                    purzeor.Add(key);
                    continue;
                }
                //oms 对不起，第1行商品定制订单进度不为“流程完成”，禁止转采购！
                if (fomsprogress != 50)
                {
                    errorDic[Guid.NewGuid().ToString("N")] = "对不起，第 " + Convert.ToString(item["fseq"]) + " 行商品" + JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + "定制订单进度不为“流程完成”，禁止转采购！";
                    purzeor.Add(key);
                }
            }
        }

        /// <summary>
        /// 返回对应状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private string GetCloseStatusStr(string status)
        {
            string result = "";
            switch (status)
            {
                case "0":
                    result = "正常";
                    break;
                case "1":
                    result = "整单关闭";
                    break;
                case "2":
                    result = "部分关闭";
                    break;
                case "3":
                    result = "自动关闭";
                    break;
                case "4":
                    result = "手动关闭";
                    break;
                default:
                    result = "关闭";
                    break;
            }
            return result;
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //选中商品行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);

            this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, false);

            var profileService = this.Container.GetService<ISystemProfile>();
            var foutspotnopur = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "foutspotnopur", false); //出现货商品无需采购
            var isfsuperpinpurchase = profileService.GetSystemParameter(Context, "pur_systemparam", "fsuperpinpurchase", false);//允许超销售数量采购
            //var fispdk = profileService.GetSystemParameter(this.Context, "pur_systemparam", "fispdk", false); //套件与沙发商品是否成套采购

            //单据类型分组 目前分类 大客户，期初合同，其它 分为三类
            var billtypeGroup = GetBillTypeGroup(e.DataEntitys);
            //过滤提示
            Dictionary<string, string> errorDic = new Dictionary<string, string>();
            //记录商品行ID相关过滤
            var topCloseOrg = new List<string>();
            //记录全部商品
            List<string> allpid = new List<string>();
            //商品明细集合
            List<DynamicObjectCollection> allProductList = new List<DynamicObjectCollection>();
            //商品授权清单中定义的不可采购商品
            var noCanPurProductIds = GetNotCanPurProduct();
            var fentrys = e.DataEntitys.SelectMany(t => t["fentry"] as DynamicObjectCollection);
            foreach (var item in e.DataEntitys)
            {
                var entries = item["fentry"] as DynamicObjectCollection;
                //过滤商品相关业务逻辑
                FilterProduct(entries, topCloseOrg, errorDic, foutspotnopur, noCanPurProductIds, selectRowIds, Convert.ToString(item["fbillno"]), isfsuperpinpurchase);
                //一级库存携带的商品不参与停产校验
                allpid.AddRange(entries.Select(x => x["fproductid"].ToString()).ToList());
                allProductList.Add(entries);
            }
            //待合单合同
            IEnumerable<DynamicObject> packDataEntitys = null;
            //一对一不合并的合同
            IEnumerable<DynamicObject> noPackDataEntitys = null;
            if (this.Context.IsSecondOrg)
            {
                packDataEntitys = e.DataEntitys.Where(x => !Convert.ToBoolean(x["fneedtransferorder"]));
                noPackDataEntitys = e.DataEntitys.Where(x => Convert.ToBoolean(x["fneedtransferorder"]));
            }
            else
            {
                packDataEntitys = e.DataEntitys/*.Where(x => !Convert.ToBoolean(x["fneedtransferorder"]))*/;
            }

            //将总部和非总部商品进行区分
            //var whetherdic = DicMainProdect(allpid);
            //全部未停购的商品
            var validProd = ProductNotStopBuying(allpid, errorDic, allProductList);

            //记录下推的明细行
            int count = 0;
            if (packDataEntitys != null && packDataEntitys.Any())
            {
                //var packWhetherDic = whetherdic.Where(f => packDataEntitys.SelectMany(x => x["fentry"] as DynamicObjectCollection).Select(x => Convert.ToString(x["fproductid"])).Contains(f.Key));
                //城市分组
                var groupCity = GetGroupCity(packDataEntitys.Select(x => x["fdeptid"].ToString()).ToList());

                foreach (var valorder in packDataEntitys)
                {
                    var order = valorder;
                    var entries = order["fentry"] as DynamicObjectCollection;
                    //dlist.Add(entries);
                    foreach (var item in entries)
                    {
                        if (topCloseOrg.Contains(Convert.ToString(item["id"])))
                            continue;
                        if (Convert.ToString(item["fomsprogress"]) != "-1" && Convert.ToString(item["fomsprogress"]) != "50")
                            continue;
                        List<DynamicObjectCollection> dlist = new List<DynamicObjectCollection>();
                        List<DynamicObjectCollection> dynamicObjects = new List<DynamicObjectCollection>();
                        //dynamicObjects.Add
                        DynamicObjectCollection dynamicObjects1 = new DynamicObjectCollection(entries.DynamicCollectionItemPropertyType);
                        dynamicObjects1.Add(item);
                        dlist.Add(dynamicObjects1);
                        PushPurorder(order, dlist);
                    }
                }
            }
            if (noPackDataEntitys != null && noPackDataEntitys.Any())
            {
                PushPurOrderOneToOne(noPackDataEntitys.ToArray(), validProd, topCloseOrg);
            }

            foreach (var item in errorDic)
            {
                this.Result.ComplexMessage.ErrorMessages.Add(item.Value);
            }

            // 返回采购订单ids
            this.Result.OptionData["PushPurchaseOrderIds"] =
                this.PushPurchaseOrders.Select(s => Convert.ToString(s["id"])).ToList();

            this.AddRefreshPageAction();
        }

        /// <summary>
        /// 按单据类型 大客户，期初合同，其它 分为三类
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        private Dictionary<string, List<string>> GetBillTypeGroup(DynamicObject[] obj)
        {
            Dictionary<string, List<string>> dic = new Dictionary<string, List<string>>();
            IOrderService orderService = this.Context.Container.GetService<IOrderService>();
            var billtypeid = orderService.GetBillTypeData(this.Context, this.HtmlForm, "v6定制柜合同");
            AddDicdeliver("v6定制柜合同", billtypeid, dic);
            return dic;
        }

        /// <summary>
        /// 获取不可采购商品id
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public List<string> GetNotCanPurProduct()
        {
            var result = new List<string>();
            if (this.Context.IsTopOrg)
            {
                return result;
            }

            var agents = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(this.Context);
            if (agents == null || agents.Count == 0)
            {
                return result;
            }

            var rulePara = new DataQueryRuleParaInfo()
            {
                SrcFldId = this.HtmlForm.Id,
            };

            var prdAuths = new ProductDataIsolateHelper().GetProductAuthInfo(Context, rulePara, agents);
            var prdAuthLst = prdAuths.SelectMany(f => f.fproductauthlist).ToList();
            if (prdAuthLst == null || prdAuthLst.Count == 0)
            {
                return result;
            }

            result = prdAuthLst.Where(f => f != null && f.fnopurchase)?.ToList()?.Select(f => f.fproductid)?.ToList();

            return result;
        }



        /// <summary>
        /// 下推采购
        /// </summary>
        /// <param name="order">单据头</param>
        /// <param name="entries">明细行</param>
        /// <param name="prodlistid">送达方分组商品</param>
        /// <param name="ismainlist">主商品与非主商品</param>
        /// <param name="validProd">有效商品</param>
        /// <param name="validProd">有效商品</param>
        /// <param name="noCanPurProductIds">商品授权清单中定义的不可采购商品</param>
        /// <param name="BillTypeGroup">按单据类型分组</param>
        /// <param name="closeOrg">要过滤的明细行</param>
        /// <param name="mainAgents">著经销商组织</param>
        private int PushPurorder(DynamicObject order,
                                List<DynamicObjectCollection> entries)
        {
            BillConvertContext billCvtCtx = null;
            var ruleId = "ydj_order_swj2ydj_purchaseorder";
            var sourceFormId = "ydj_order";
            var targetFormId = "ydj_purchaseorder";
            var selectedRows = new List<SelectedRow>();

            //foreach (var order in orders)
            //{
            #region 多个合同商品明细合并

            foreach (var ent in entries)
            {
                var selRows = new List<SelectedRow>();
                var ss = ent.Select(x =>
                  {
                      var sel = new SelectedRow
                      {
                          PkValue = order["id"].ToString(),
                          BillNo = order["fbillno"].ToString(),
                          EntityKey = "fentry",
                          EntryPkValue = Convert.ToString(x["id"])
                      };
                      sel.FieldValues = new Dictionary<string, object>();
                      sel.FieldValues["matid"] = x["fproductid"];
                      sel.FieldValues["matno"] = (x["fproductid_ref"] as DynamicObject)?["fnumber"];
                      sel.FieldValues["matname"] = (x["fproductid_ref"] as DynamicObject)?["fname"];
                      return sel;
                  }).ToList();
                //selRows.Contains(ss);
                //var ens = ent.Where(x => billTypeGroup.Contains(x["id"].ToString())
                //                && prodlistid.Contains(x["id"].ToString())
                //                && ismainlist.Contains(x["fproductid"].ToString())
                //                && validProd.Contains(x["fproductid"].ToString())
                //                && !closeOrg.Contains(x["id"].ToString())
                //             ).ToList();
                //selRows.Add(ent);
                selRows.AddRange(ent.Select(x =>
                {
                    var sel = new SelectedRow
                    {
                        PkValue = order["id"].ToString(),
                        BillNo = order["fbillno"].ToString(),
                        EntityKey = "fentry",
                        EntryPkValue = Convert.ToString(x["id"])
                    };
                    sel.FieldValues = new Dictionary<string, object>();
                    sel.FieldValues["matid"] = x["fproductid"];
                    sel.FieldValues["matno"] = (x["fproductid_ref"] as DynamicObject)?["fnumber"];
                    sel.FieldValues["matname"] = (x["fproductid_ref"] as DynamicObject)?["fname"];
                    return sel;
                }).ToList());




                selectedRows.AddRange(selRows);


                billCvtCtx = new BillConvertContext()
                {
                    RuleId = ruleId,
                    SourceFormId = sourceFormId,
                    TargetFormId = targetFormId,
                    SelectedRows = selectedRows.ToConvertSelectedRows(),
                    Option = this.Option
                };

                var count = billCvtCtx.SelectedRows.Count();
                if (count == 0)
                {
                    return count;
                }

                if (Convert.ToBoolean(order["fisapplypur"]))
                {
                    billCvtCtx.RuleId = "ydj_order2pur_reqorder";
                    billCvtCtx.TargetFormId = "pur_reqorder";
                }
                else
                {
                    //检查是否已经下推过采购订单
                    //this.CheckIsPushPurOrder(dataEntity);
                }

                var convertService = this.Container.GetService<IConvertService>();
                var result = convertService.Push(this.Context, billCvtCtx);
                var convertResult = result.SrvData as ConvertResult;

                if (billCvtCtx.TargetFormId.EqualsIgnoreCase("ydj_purchaseorder"))
                {
                    this.DealRenewal(convertResult.TargetDataObjects, new DynamicObject[] { order });
                }

                //生成编码
                var billNoService = this.Context.Container.GetService<IBillNoService>();
                billNoService.SetBillNo(this.Context, billCvtCtx.TargetFormId, convertResult.TargetDataObjects);
                //foreach (var item in convertResult.TargetDataObjects)
                //{
                //    item["fbillno"] = "DZ" + item["fbillno"];
                //}
                var invokeResult = this.Gateway.InvokeBillOperation(this.Context,
                    billCvtCtx.TargetFormId,
                    convertResult.TargetDataObjects,
                    "draft",
                    new Dictionary<string, object>());

                if (invokeResult.IsSuccess)
                {
                    var targetForm = this.MetaModelService.LoadFormModel(this.Context, billCvtCtx.TargetFormId);
                    this.Result.IsSuccess = true;
                    this.Result.ComplexMessage.SuccessMessages.Add($"{targetForm.Caption}生成成功！");
                    foreach (var targetData in convertResult.TargetDataObjects)
                    {
                        this.Result.ComplexMessage.SuccessMessages.Add($"{targetForm.Caption}编号【{targetData["fbillno"]}】");
                    }

                    if (convertResult.HtmlForm.Id.EqualsIgnoreCase("ydj_purchaseorder"))
                    {
                        this.PushPurchaseOrders.AddRange(convertResult.TargetDataObjects);
                    }
                }
                #endregion
            }
            //}
            return 1;
        }

        /// <summary>
        /// 合同一堆已下推采购
        /// </summary>
        /// <param name="order">单据头</param>
        /// <param name="validProd">有效商品</param>
        /// <param name="closeOrg">要过滤的明细行</param>
        private int PushPurOrderOneToOne(DynamicObject[] orders,
                                List<string> validProd,
                                List<string> closeOrg)
        {
            BillConvertContext billCvtCtx = null;
            var ruleId = "ydj_order2ydj_purchaseorder";
            var sourceFormId = "ydj_order";
            var targetFormId = "ydj_purchaseorder";
            var selectedRows = new List<SelectedRow>();

            foreach (var order in orders)
            {
                var fentry = order["fentry"] as DynamicObjectCollection;
                var ens = fentry.Where(x => validProd.Contains(x["fproductid"].ToString())
                                       && !closeOrg.Contains(x["id"].ToString())
                                     );
                var selRows = new List<SelectedRow>();
                selRows.AddRange(ens.Select(x =>
                {
                    var sel = new SelectedRow
                    {
                        PkValue = order["id"].ToString(),
                        BillNo = order["fbillno"].ToString(),
                        EntityKey = "fentry",
                        EntryPkValue = Convert.ToString(x["id"])
                    };
                    sel.FieldValues = new Dictionary<string, object>();
                    sel.FieldValues["matid"] = x["fproductid"];
                    sel.FieldValues["matno"] = (x["fproductid_ref"] as DynamicObject)?["fnumber"];
                    sel.FieldValues["matname"] = (x["fproductid_ref"] as DynamicObject)?["fname"];
                    return sel;
                }).ToList());

                selectedRows.AddRange(selRows);
            }

            billCvtCtx = new BillConvertContext()
            {
                RuleId = ruleId,
                SourceFormId = sourceFormId,
                TargetFormId = targetFormId,
                SelectedRows = selectedRows.ToConvertSelectedRows(),
                Option = this.Option
            };

            var count = billCvtCtx.SelectedRows.Count();
            if (count == 0)
            {
                return count;
            }

            if (Convert.ToBoolean(orders[0]["fisapplypur"]))
            {
                billCvtCtx.RuleId = "ydj_order2pur_reqorder";
                billCvtCtx.TargetFormId = "pur_reqorder";
            }
            else
            {
                //检查是否已经下推过采购订单
                //this.CheckIsPushPurOrder(dataEntity);
            }

            var convertService = this.Container.GetService<IConvertService>();
            var result = convertService.Push(this.Context, billCvtCtx);
            var convertResult = result.SrvData as ConvertResult;

            if (billCvtCtx.TargetFormId.EqualsIgnoreCase("ydj_purchaseorder"))
            {
                this.DealRenewal(convertResult.TargetDataObjects, orders);
            }

            var invokeResult = this.Gateway.InvokeBillOperation(this.Context,
                billCvtCtx.TargetFormId,
                convertResult.TargetDataObjects,
                "draft",
                new Dictionary<string, object>());

            if (invokeResult.IsSuccess)
            {
                var targetForm = this.MetaModelService.LoadFormModel(this.Context, billCvtCtx.TargetFormId);
                this.Result.IsSuccess = true;
                this.Result.ComplexMessage.SuccessMessages.Add($"{targetForm.Caption}转单采购订单生成成功！");
                foreach (var targetData in convertResult.TargetDataObjects)
                {
                    this.Result.ComplexMessage.SuccessMessages.Add($"{targetForm.Caption}转单采购订单编号【{targetData["fbillno"]}】");
                }

                if (convertResult.HtmlForm.Id.EqualsIgnoreCase("ydj_purchaseorder"))
                {
                    this.PushPurchaseOrders.AddRange(convertResult.TargetDataObjects);
                }
            }
            return count;
        }

        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            //if (e.EventName == "afterCreateUIData")
            //{
            //    foreach (var item in e.DataEntities)
            //    {
            //        item["fbillno"] = "DZ" + Convert.ToString(item["fbillno"]);
            //    }
            //}

        }

        /// <summary>
        /// 城市进行分组
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, List<string>> GetGroupCity(List<string> deptids)
        {
            Dictionary<string, List<string>> dic = new Dictionary<string, List<string>>();
            //var orgin = Context.IsTopOrg ? Context.Company : Context.TopCompanyId;
            string strSql = @"select t2.fmycity,t1.fid from t_bd_department t1
                            join t_bas_store t2 on t1.fstore=t2.fid
                            where t1.fid in ('{0}')  and t1.fforbidstatus='0'".Fmt(string.Join("','", deptids)/*, orgin*/); /*and t2.fmainorgid = '{1}'*/
            var dm = Context.Container.GetService<IDBService>();
            var res = dm.ExecuteDynamicObject(Context, strSql);
            var list = res.Where(x => !x["fmycity"].ToString().IsNullOrEmptyOrWhiteSpace()).ToList();

            var isexists = false;
            foreach (var v1 in deptids)
            {
                foreach (var v2 in list)
                {
                    if (v1 == v2["fid"].ToString())
                    {
                        isexists = true;
                        if (v2["fmycity"].ToString().IsNullOrEmptyOrWhiteSpace())
                            AddDicdeliver("无城市", v1, dic);
                        else
                            AddDicdeliver(v2["fmycity"].ToString(), v1, dic);
                        break;
                    }
                }
                if (!isexists)
                    AddDicdeliver("无城市", v1, dic);
                isexists = false;
            }
            return dic;
        }

        /// <summary>
        /// 将送达方对应的业绩品牌商品进行分组
        /// </summary>
        /// <param name="key"></param>
        /// <param name="val"></param>
        /// <param name=""></param>
        private void AddDicdeliver(string key, string val, Dictionary<string, List<string>> dic)
        {
            if (dic.ContainsKey(key))
            {
                List<string> list = dic[key];
                list.Add(val);
                dic[key] = list;
            }
            else
            {
                List<string> list = new List<string>();
                list.Add(val);
                dic[key] = list;
            }
        }

        /// <summary>
        /// 总部商品和非总部商品分类
        /// </summary>
        /// <param name="pids">商品ID集合</param>
        /// <returns></returns>
        private Dictionary<string, bool> DicMainProdect(List<string> pids)
        {
            Dictionary<string, bool> prodlist = new Dictionary<string, bool>();
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));
            var orgin = Context.IsTopOrg ? Context.Company : Context.TopCompanyId;
            var where = "fid in ('{0}') and fmainorgid='{1}'".Fmt(string.Join("','", pids), orgin);
            var reader = this.Context.GetPkIdDataReader(purForm, where, new List<SqlParam>() { });
            var purOrder = dm.SelectBy(reader).OfType<DynamicObject>();
            //存主商品
            var mainprodids = new List<string>();
            if (purOrder != null)
                mainprodids = purOrder.Select(x => x["id"].ToString()).ToList();

            //将总部商品加入dic
            mainprodids.ForEach(x => prodlist[x] = true);
            //将非总部的商品加入DIC
            var falseMainProd = pids.Where(x => !mainprodids.Contains(x)).ToList();
            falseMainProd.ForEach(x => prodlist[x] = false);
            return prodlist;
        }

        /// <summary>
        /// 返回没有停购的商品
        /// </summary>
        /// <returns></returns>
        private List<string> ProductNotStopBuying(List<string> prodids, Dictionary<string, string> error, List<DynamicObjectCollection> allProductList)
        {
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));
            var where = "fid in ('{0}')".Fmt(string.Join("','", prodids));
            var reader = this.Context.GetPkIdDataReader(purForm, where, new List<SqlParam> { });
            var purOrder = dm.SelectBy(reader).OfType<DynamicObject>();

            var productMap = purOrder.ToDictionary(
                x => x["id"].ToString(),
                x => new {
                    IsActive = !Convert.ToBoolean(x["fendpurchase"]),
                    Name = x["fname"].ToString()
                });
            var notStoppedSet = new HashSet<string>(
                    productMap.Where(kv => kv.Value.IsActive)
                  .Select(kv => kv.Key));
            var productCheckMap = new Dictionary<string, (bool HasFirstInventory, List<string> NonFirstSeqs)>();
            foreach (var id in prodids.Distinct())
            {
                productCheckMap[id] = (false, new List<string>());
            }


            foreach (var collection in allProductList)
            {
                foreach (var item in collection)
                {
                    var pid = Convert.ToString(item["fproductid"]);
                    if (!productCheckMap.TryGetValue(pid, out var status))
                        continue;

                    if (Convert.ToBoolean(item["fisfromfirstinventory"]))
                    {
                        productCheckMap[pid] = (true, status.NonFirstSeqs);
                    }
                    else
                    {
                        // 收集所有非首次库存的行号
                        var seq = Convert.ToString(item["fseq"]);
                        status.NonFirstSeqs.Add(seq);
                        productCheckMap[pid] = status;
                    }
                }
            }

            var resultSet = new HashSet<string>();
            foreach (var id in prodids.Distinct())
            {
                if (notStoppedSet.Contains(id))
                {
                    resultSet.Add(id);
                    continue;
                }

                var (hasFirstInventory, nonFirstSeqs) = productCheckMap[id];
                if (hasFirstInventory)
                {
                    resultSet.Add(id);
                }
                if (nonFirstSeqs.Count > 0 && productMap.TryGetValue(id, out var product))
                {
                    foreach (var seq in nonFirstSeqs)
                    {
                        error[Guid.NewGuid().ToString("N")] =
                            $"第 {seq} 行 {product.Name} 商品已停购，不允许采购！";
                    }
                }
            }
            return resultSet.ToList();
        }

        /// <summary>
        /// 处理下推客户联系人相关数据
        /// </summary>
        /// <param name="targetDataObjects"></param>
        /// <param name="orders"></param>
        private void DealRenewal(IEnumerable<DynamicObject> targetDataObjects, DynamicObject[] orders)
        {
            var billTypeService = this.Container.GetService<IBillTypeService>();
            foreach (var item in targetDataObjects)
            {
                var frenewalflag = Convert.ToBoolean(item["frenewalflag"]);
                if (frenewalflag)
                {
                    var entrys = (item["fentity"] as DynamicObjectCollection)?.FirstOrDefault();
                    var order = orders.Where(x => Convert.ToString(x["fbillno"]) == Convert.ToString(entrys?["fsourcebillno"])).FirstOrDefault();
                    if (order != null)
                    {
                        var billType = billTypeService.GetBillTypeById(this.Context, Convert.ToString(order["fbilltype"]));
                        if (Convert.ToString(billType["fname"]) == "销售转单" || Convert.ToBoolean(order["fisresellorder"]))
                        {
                            item["fcustomerid"] = Convert.ToString(order["fterminalcustomer"]);
                            item["fconsignee"] = Convert.ToString(order["fcontacts_c"]);
                            item["fphone"] = Convert.ToString(order["fcoophone"]);
                            item["fprovince"] = Convert.ToString(order["fprovince_c"]);
                            item["fcity"] = Convert.ToString(order["fcity_c"]);
                            item["fregion"] = Convert.ToString(order["fregion_c"]);
                            item["faddress"] = Convert.ToString(order["fcooaddress"]);
                        }
                        else
                        {
                            item["fcustomerid"] = Convert.ToString(order["fcustomerid"]);
                            item["fconsignee"] = Convert.ToString((order["fcustomercontactid_ref"] as DynamicObject)["fcontacter"]);
                            item["fphone"] = Convert.ToString(order["fphone"]);
                            item["fprovince"] = Convert.ToString(order["fprovince"]);
                            item["fcity"] = Convert.ToString(order["fcity"]);
                            item["fregion"] = Convert.ToString(order["fregion"]);
                            item["faddress"] = Convert.ToString(order["faddress"]);
                        }

                        //一键采购触发合同生成采购订单时，销售合同的【销售员】需携带至下游采购订单的【采购员】。
                        item["fpostaffid"] = Convert.ToString(order["fstaffid"]);
                    }
                }
            }
        }
    }
}
