using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 采购订单：校验不可以单独下配件商品
    /// </summary>
    [InjectService]
    [FormId("ydj_order|ydj_purchaseorder")]
    [OperationNo("CheckTgcPart")]
    public class CheckTgcPart : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string fproductid = this.GetQueryOrSimpleParam("fproductid", "");
            string sql = $@"SELECT 1 FROM t_sel_fittingsmap 
                            INNER JOIN t_sel_fittingsmapentry ON t_sel_fittingsmapentry.fid = t_sel_fittingsmap.fid and fdisable != 1
                            INNER JOIN SER_YDJ_CATEGORY ON SER_YDJ_CATEGORY.fid =t_sel_fittingsmap.fcategoryid
                            WHERE SER_YDJ_CATEGORY.fname ='铁架床' AND fmatchbyproduct = '1' AND fmaterialid = '{fproductid}'";
            //如果查到已经被配置到选配配件映射中的配件商品，则不能单独下单
            this.Result.IsSuccess = this.DBService.ExecuteDynamicObject(this.Context, sql).Count == 0;
        }
    }




}
