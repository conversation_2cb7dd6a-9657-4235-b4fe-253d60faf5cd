using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：检查产品类别是否为xx类别
    /// </summary>
    [InjectService]
    [FormId("ydj_order|ydj_purchaseorder")]
    [OperationNo("CheckCategory")]
    public class CheckCategory : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string fcategoryid = this.GetQueryOrSimpleParam("fcategoryid", "");
            string[] categorylist= fcategoryid.Split(',');
            var catagory = categorylist.JoinEx("','",false);
            if (CheckCategoryByName(catagory, "沙发类"))
            {
                this.Result.IsSuccess = true;
            }
            else {
                this.Result.IsSuccess = false;
            }
        }

        private bool CheckCategoryByName(string catagory, string TypeName)
        {
            string sql = $@" select c1.fid fid1,c1.fname fname1,
                            c2.fparentid fparentid2,c2.fid fid2,c2.fname fname2,
                            c3.fparentid fparentid3,c3.fid fid3,c3.fname fname3 
                            from ser_ydj_category c1 
                            left join ser_ydj_category c2 on c2.fid=c1.fparentid
                            left join ser_ydj_category c3 on c3.fid=c2.fparentid 
                            where c1.fid in ('{catagory}')  AND ( ISNULL(c1.fname,'') <>'{TypeName}' AND  ISNULL(c2.fname,'')<>'{TypeName}' AND ISNULL(c3.fname,'')<>'{TypeName}')";
            return this.DBService.ExecuteDynamicObject(this.Context, sql).Count == 0;
        }
    }




}
