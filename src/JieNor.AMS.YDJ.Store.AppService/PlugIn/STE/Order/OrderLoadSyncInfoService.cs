using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    public class OrderLoadSyncInfoService : IOrderLoadSyncInfoService
    {
        public Tuple<bool, Dictionary<string, object>> LoadSyncInfo(UserContext userContext, Tuple<string, DynamicObject> eventData, OperateOption option)
        {
            if (eventData == null || eventData.Item2 == null) return new Tuple<bool, Dictionary<string, object>>(false, null);

            if (option != null)
            {
                //获取原始操作码
                var originalOpNo = option.GetVariableValue<string>("originalOpNo", string.Empty);
                //如果是协同变更接口的，不需要再协同到对方，因为变更是从协同方发起的
                if ("SyncChange".EqualsIgnoreCase(originalOpNo))
                {
                    return new Tuple<bool, Dictionary<string, object>>(true, new Dictionary<string, object>
                    {
                        { "syncData",new Dictionary<TargetSEP, DynamicObject>() }
                    });
                }
            }

            var dataEntity = eventData.Item2;

            //返回不需要打包的实体主键Id，比如：商品明细主键Id
            var productEntrys = dataEntity["fentry"] as DynamicObjectCollection;
            Dictionary<string, List<string>> notPackEntityPkId = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);
            notPackEntityPkId["fentry"] = productEntrys
                .Where(o => !Convert.ToString(o["foperationmode"]).EqualsIgnoreCase("1"))
                .Select(o => Convert.ToString(o["id"]))
                .ToList();

            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var productForm = metaModelService.LoadFormModel(userContext, "ydj_product");
            var dm = userContext.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, productForm.GetDynamicObjectType(userContext));

            //商品明细根据商品对应的发布企业分组
            var groupEntry = productEntrys
                .Where(o => !o["fproductid"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(o["foperationmode"]).EqualsIgnoreCase("1"))
                .GroupBy(k =>
                {
                    var publishcid = "";
                    var publishpid = "";
                    var product = dm.Select(k["fproductid"]) as DynamicObject;
                    if (product != null)
                    {
                        var publishField = productForm?.GetField(productForm?.PublishCIdFldKey) as Framework.MetaCore.FormMeta.HtmlCompanyField;
                        publishcid = publishField?.DynamicProperty?.GetValue<string>(product);
                        publishpid = publishField?.ProductIdDynamicProperty?.GetValue<string>(product);
                    }
                    return $"{publishcid}#{publishpid}";
                });

            var supplierService = userContext.Container.GetService<Core.Interface.ISupplierService>();


            Dictionary<TargetSEP, DynamicObject> syncData = new Dictionary<TargetSEP, DynamicObject>();
            var selectionData = new List<Dictionary<string, string>>();
            foreach (var group in groupEntry)
            {
                var groupKeys = group.Key.Split('#');
                var target = supplierService.GetSyncTargetSEP(userContext, groupKeys[0], groupKeys[1]);
                if (target == null) continue;

                var newEntity = dataEntity.Clone() as DynamicObject;
                var newEntityEntry = newEntity["fentry"] as DynamicObjectCollection;
                newEntityEntry.Clear();

                foreach (var entry in group)
                {
                    var localProductId = Convert.ToString(entry["fproductid"]);
                    var product = userContext.LoadBizDataById("ydj_product", localProductId);
                    if (product == null) continue;
                    if (product["ffromchaindataid"].IsNullOrEmptyOrWhiteSpace()) continue;

                    Dictionary<string, string> sinfo = new Dictionary<string, string>()
                    {
                        { "ftranid", Convert.ToString(entry["ftranid"]) },
                        { "fmaterialid", Convert.ToString(product["ffromchaindataid"]) },
                        { "fselectionnumber", Convert.ToString(entry["fselectionnumber"]) },
                        { "fissuit", Convert.ToString(product["fissuit"]) },
                        { "fforsuiteselectionid", Convert.ToString(entry["fforsuiteselectionid"]) }
                    };
                    var forselectionnumber = "";
                    if (!entry["fforsuiteselectionid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var suite = userContext.LoadBizDataById("mt_suiteselection", Convert.ToString(entry["fforsuiteselectionid"]));
                        forselectionnumber = suite?.GetValue("fnumber", "") ?? "";
                    }
                    sinfo["forselectionnumber"] = forselectionnumber;
                    selectionData.Add(sinfo);

                    newEntityEntry.Add(entry);
                }
                syncData.Add(target, newEntity);
            }

            var simpleData = new Dictionary<string, string>();
            //设置选配信息
            SetSelectionSimpleData(userContext, simpleData, productEntrys);

            simpleData.Add("selectionData", selectionData.ToJson());

            //设置K3的组织ID
            this.SetK3Org(userContext, dataEntity, simpleData);

            if (syncData.Count > 0)
            {
                return new Tuple<bool, Dictionary<string, object>>(true, new Dictionary<string, object>
                {
                    { "syncData", syncData },
                    { "simpleData", simpleData },
                    { "notPackEntityPkId", notPackEntityPkId }
                });
            }

            return new Tuple<bool, Dictionary<string, object>>(false, null);
        }

        /// <summary>
        /// 设置K3的组织ID
        /// </summary>
        private void SetK3Org(UserContext userCtx, DynamicObject dataEntity, Dictionary<string, string> simpleData)
        {
            var deptIds = new List<string>();

            //单据头销售部门
            var deptId = Convert.ToString(dataEntity["fdeptid"]);
            deptIds.Add(deptId);

            //商品明细的销售部门
            //var entryDeptKvs = new Dictionary<string, string>();
            //var entrys = dataEntity["fentry"] as DynamicObjectCollection;
            //foreach (var entry in entrys)
            //{
            //    var _tranId = Convert.ToString(entry["ftranid"]);
            //    var _deptId = Convert.ToString(entry["fdeptid"]);
            //    if (_tranId.IsNullOrEmptyOrWhiteSpace()) continue;
            //    entryDeptKvs[_tranId] = _deptId;
            //}
            //deptIds.AddRange(entryDeptKvs.Values);

            //联合开单业务员的销售部门
            var dutyEntrys = dataEntity["fdutyentry"] as DynamicObjectCollection;
            var staffIds = dutyEntrys.Select(o => Convert.ToString(o["fdutyid"])).ToList();
            var dutyDeptKvs = this.GetDeptByStaffs(userCtx, staffIds);
            deptIds.AddRange(dutyDeptKvs.Values);

            //加载部门对于的K3组织ID
            var deptOrgMapKv = DirectSynergyHelper.GetK3OrgIdsByDeptIds(userCtx, deptIds);

            var orgData = new Dictionary<string, string>();
            deptOrgMapKv.TryGetValue(deptId, out orgData);
            var orgId = "";
            orgData?.TryGetValue("orgId", out orgId);
            simpleData.Add("orgId", orgId);

            //var entryOrgIdKv = new Dictionary<string, string>();
            //foreach (var item in entryDeptKvs)
            //{
            //    var _tranId = item.Key;
            //    var _deptId = item.Value;
            //    var _orgData = new Dictionary<string, string>();
            //    deptOrgMapKv.TryGetValue(_deptId, out _orgData);
            //    var _orgId = "";
            //    _orgData?.TryGetValue("orgId", out _orgId);
            //    entryOrgIdKv[_tranId] = _orgId;
            //}
            //simpleData.Add("entryOrgIdKv", entryOrgIdKv.ToJson());

            var dutyOrgIdKv = new Dictionary<string, string>();
            foreach (var item in dutyDeptKvs)
            {
                var staffNumber = item.Key;
                var staffDeptId = item.Value;
                var _orgData = new Dictionary<string, string>();
                deptOrgMapKv.TryGetValue(staffDeptId, out _orgData);
                var _orgId = "";
                _orgData?.TryGetValue("orgId", out _orgId);
                dutyOrgIdKv[staffNumber] = _orgId;
            }
            simpleData.Add("dutyOrgIdKv", dutyOrgIdKv.ToJson());
        }

        /// <summary>
        /// 获取指定员工的销售部门
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="staffIds"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetDeptByStaffs(UserContext userCtx, List<string> staffIds)
        {
            var dutyDeptKvs = new Dictionary<string, string>();
            if (staffIds == null || !staffIds.Any()) return dutyDeptKvs;

            var sqlText = $@"
            select s.fnumber,se.fdeptid from t_bd_staff s 
            inner join t_bd_staffentry se on se.fid=s.fid and se.fismain='1' 
            where s.fmainorgid=@fmainorgid";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company)
            };

            if (dutyDeptKvs.Count == 1)
            {
                sqlText += $" and s.fid='{staffIds[0]}'";
            }
            else
            {
                sqlText += $" and s.fid in('{string.Join("','", staffIds)}')";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    var staffNumber = reader.GetValueToString("fnumber");
                    var staffDeptId = reader.GetValueToString("fdeptid");
                    dutyDeptKvs[staffNumber] = staffDeptId;
                }
            }

            return dutyDeptKvs;
        }

        /// <summary>
        /// 获取选配信息数据包
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        protected void SetSelectionSimpleData(UserContext userContext, Dictionary<string, string> simpleData, DynamicObjectCollection productEntrys)
        {
            List<string> suiteSelectionNumbers = new List<string>();
            List<string> suiteSelectionIds = new List<string>();
            List<string> partsSelectionNumbers = new List<string>();
            var dbService = userContext.Container.GetService<IDBService>();

            foreach (var pe in productEntrys)
            {
                if (pe["fselectionnumber"].IsNullOrEmptyOrWhiteSpace()) continue;
                var snumber = pe["fselectionnumber"].ToString();
                var product = userContext.LoadBizDataById("ydj_product", pe["fproductid"].ToString());
                var issuite = !product["fissuit"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(product["fissuit"]);
                if (issuite)
                {
                    suiteSelectionNumbers.Add(snumber);
                }
                else
                {
                    //产品是子件选配，有所属套件，则为套件选配中的子件，把套件也添加到协同数据中
                    if (!pe["fforsuiteselectionid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        suiteSelectionIds.Add(pe["fforsuiteselectionid"].ToString());
                    }
                    partsSelectionNumbers.Add(snumber);
                }
            }

            var suiteSelectionFilter = "";
            if (suiteSelectionNumbers.Count > 0)
            {
                suiteSelectionFilter = " a.fnumber in ('" + suiteSelectionNumbers.JoinEx("','", false) + "')";
            }
            if (suiteSelectionIds.Count > 0)
            {
                if (suiteSelectionFilter != "")
                {
                    suiteSelectionFilter += " OR ";
                }
                suiteSelectionFilter += " a.fid in ('" + suiteSelectionIds.JoinEx("','", false) + "')";
            }
            if (suiteSelectionFilter != "")
            {
                var sqlSuiteSelection = @"SELECT a.fid, a.fnumber, a.fproductid, d.fnumber AS fproductnumber, d.ffromchaindataid AS fchainDataId, a.fploidy
FROM T_MT_SUITESELECTION a
INNER JOIN T_BD_MATERIAL d ON a.fproductid = d.fid
WHERE a.fmainorgid = @fmainorgid AND (" + suiteSelectionFilter + ")";

                List<Dictionary<string, object>> arrSuiteSelection = new List<Dictionary<string, object>>();
                using (var reader = dbService.ExecuteReader(userContext, sqlSuiteSelection, new SqlParam("fmainorgid", System.Data.DbType.String, userContext.Company)))
                {
                    while (reader.Read())
                    {
                        Dictionary<string, object> jo = new Dictionary<string, object>();
                        jo.Add("fid", reader.GetValue<string>("fid"));
                        jo.Add("fnumber", reader.GetValue<string>("fnumber"));
                        jo.Add("fproductid", reader.GetValue<string>("fproductid"));
                        jo.Add("fproductnumber", reader.GetValue<string>("fproductnumber"));
                        jo.Add("fchainDataId", reader.GetValue<string>("fchainDataId"));
                        jo.Add("fploidy", reader.GetValue<string>("fploidy"));
                        arrSuiteSelection.Add(jo);
                    }
                }
                //var suiteselections = dbService.ExecuteDynamicObject(userContext, sqlSuiteSelection);

                //获取套件下的子件
                var sqlPartsSelection = @"SELECT b.fid, b.fpartproductid, c.fnumber AS fpartproductnumber, c.ffromchaindataid AS fproductchaindataid, b.fqty, b.fpartsselectionid, e.fnumber AS fpartsselectionnumber
FROM T_MT_SUITESELECTIONENTRY b
INNER JOIN T_BD_MATERIAL c ON b.fpartproductid = c.fid
INNER JOIN T_MT_PARTSSELECTION e ON b.fpartsselectionid = e.fid
WHERE b.fid IN ('{0}')".Fmt(arrSuiteSelection.Select(x => x["fid"].ToString()).JoinEx("','", false));
                //var partsselections = dbService.ExecuteDynamicObject(userContext, sqlPartsSelection);
                List<Dictionary<string, object>> arrPartsSelection = new List<Dictionary<string, object>>();
                using (var reader = dbService.ExecuteReader(userContext, sqlPartsSelection))
                {
                    while (reader.Read())
                    {
                        Dictionary<string, object> jo = new Dictionary<string, object>();
                        jo.Add("fid", reader.GetValue<string>("fid"));
                        jo.Add("fpartproductid", reader.GetValue<string>("fpartproductid"));
                        jo.Add("fpartproductnumber", reader.GetValue<string>("fpartproductnumber"));
                        jo.Add("fproductchaindataid", reader.GetValue<string>("fproductchaindataid"));
                        jo.Add("fqty", reader.GetValue<string>("fqty"));
                        jo.Add("fpartsselectionid", reader.GetValue<string>("fpartsselectionid"));
                        jo.Add("fpartsselectionnumber", reader.GetValue<string>("fpartsselectionnumber"));
                        arrPartsSelection.Add(jo);
                    }
                }
                //套件下的子件也需要协同
                partsSelectionNumbers.AddRange(arrPartsSelection.Select(x => x["fpartsselectionnumber"].ToString()));

                foreach (var s in arrSuiteSelection)
                {
                    s["fentity"] = arrPartsSelection.Where(x => x["fid"].ToString() == s["fid"].ToString());
                }

                simpleData.Add("suiteselection", arrSuiteSelection.ToJson());
            }

            if (partsSelectionNumbers.Count > 0)
            {
                var sqlPartsSelection = @"SELECT a.fid, a.fnumber, a.fproductid, b.fnumber AS fproductnumber, b.ffromchaindataid AS fproductchaindataid
FROM T_MT_PARTSSELECTION a
INNER JOIN T_BD_MATERIAL b ON a.fproductid = b.fid
WHERE a.fmainorgid = @fmainorgid AND a.fnumber IN ('{0}')".Fmt(partsSelectionNumbers.JoinEx("','", false));
                List<Dictionary<string, object>> arrPartsSelection = new List<Dictionary<string, object>>();
                using (var reader = dbService.ExecuteReader(userContext, sqlPartsSelection, new SqlParam("fmainorgid", System.Data.DbType.String, userContext.Company)))
                {
                    while (reader.Read())
                    {
                        Dictionary<string, object> jo = new Dictionary<string, object>();
                        jo.Add("fid", reader.GetValue<string>("fid"));
                        jo.Add("fnumber", reader.GetValue<string>("fnumber"));
                        jo.Add("fproductid", reader.GetValue<string>("fproductid"));
                        jo.Add("fproductnumber", reader.GetValue<string>("fproductnumber"));
                        jo.Add("fproductchaindataid", reader.GetValue<string>("fproductchaindataid"));
                        arrPartsSelection.Add(jo);
                    }
                }

                //获取维度值，商品ID转换为云链ID
                var sqlDimesions = @"SELECT a.fid, a.fmatchingdimensions AS fdimensionid, b.fnumber AS dimensionnumber, b.ffromchaindataid AS fdimensionchaindataid,
    a.fbizfieldsrc, a.fbizfieldtype, (CASE WHEN fbizfieldsrc != 'basedata' THEN a.fbizfieldvalue ELSE (SELECT ffromchaindataid FROM t_bd_material WHERE fid = a.fbizfieldvalue) END) AS fbizfieldvalue
FROM T_MT_PARTSSELECTIONENTRY a
INNER JOIN T_MT_SELECTIONDIMENSION b ON a.fmatchingdimensions = b.fid
WHERE b.fmainorgid = @fmainorgid AND a.fid IN ('{0}')".Fmt(arrPartsSelection.Select(x => x["fid"].ToString()).JoinEx("','", false));
                List<Dictionary<string, object>> arrDimensions = new List<Dictionary<string, object>>();
                using (var reader = dbService.ExecuteReader(userContext, sqlDimesions, new SqlParam("fmainorgid", System.Data.DbType.String, userContext.Company)))
                {
                    while (reader.Read())
                    {
                        Dictionary<string, object> jo = new Dictionary<string, object>();
                        jo.Add("fid", reader.GetValue<string>("fid"));
                        jo.Add("fdimensionid", reader.GetValue<string>("fdimensionid"));
                        jo.Add("dimensionnumber", reader.GetValue<string>("dimensionnumber"));
                        jo.Add("fbizfieldsrc", reader.GetValue<string>("fbizfieldsrc"));
                        jo.Add("fbizfieldtype", reader.GetValue<string>("fbizfieldtype"));
                        jo.Add("fbizfieldvalue", reader.GetValue<string>("fbizfieldvalue"));
                        jo.Add("fdimensionchaindataid", reader.GetValue<string>("fdimensionchaindataid"));
                        arrDimensions.Add(jo);
                    }
                }

                foreach (var s in arrPartsSelection)
                {
                    s["fentity"] = arrDimensions.Where(x => x["fid"].ToString() == s["fid"].ToString()).ToList();
                }

                simpleData.Add("partsselection", arrPartsSelection.ToJson());
            }
        }

        private DynamicObject GetProduct(List<DynamicObject> products, string productId)
        {
            return products.FirstOrDefault(x => x["fid"].ToString() == productId);
        }
    }
}
