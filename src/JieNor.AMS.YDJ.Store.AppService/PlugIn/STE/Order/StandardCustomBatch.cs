using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.MP.API.DTO;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 商品批量转标准品
    /// </summary>
    [InjectService]
    [FormId("stk_inventoryverify|stk_initstockbill")]
    [OperationNo("standardbustombatch")]
    public class StandardCustomBatch : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var formId = this.HtmlForm.Id;
            var AllSelectRows = this.GetQueryOrSimpleParam("allselectrows", "");
            List<Dictionary<string, string>> ResultRows = new List<Dictionary<string, string>>();
            //初始库存单
            if (!AllSelectRows.IsNullOrEmptyOrWhiteSpace()) 
            {
                var AllSelectRowsObj = JArray.Parse(AllSelectRows);
                foreach (var Arr in AllSelectRowsObj) {
                    //列表和编辑页面辅助属性值取值不同
                    var attrinfoid = Convert.ToString(Arr?["data"]["fattrinfo"]);
                    if (Arr["data"]["fattrinfo"].HasValues) {
                        attrinfoid = Convert.ToString(Arr["data"]["fattrinfo"]?["id"]);
                    }
                    var productid =Convert.ToString(Arr?["data"]["fmaterialid"]);
                    if (Arr["data"]["fmaterialid"].HasValues)
                    {
                        productid = Convert.ToString(Arr["data"]["fmaterialid"]?["id"]);
                    }
                    var attrinfoobj = this.Context.LoadBizDataById("bd_auxpropvalueset", attrinfoid);
                    var setEntrys = attrinfoobj?["fentity"] as DynamicObjectCollection;
                    //辅助属性组合值键值对
                    List<PropEntity> auxPropKv = new List<PropEntity>();
                    if (setEntrys != null && setEntrys.Any())
                    {
                        foreach (var item in setEntrys)
                        {
                            var sel_propobj = this.Context.LoadBizDataById("sel_prop", Convert.ToString(Convert.ToString(item["fauxpropid"])));
                            auxPropKv.Add(new PropEntity()
                            {
                                PropId = Convert.ToString(item["fauxpropid"]),
                                PropName = Convert.ToString(sel_propobj["fname"]),
                                PropNumber = Convert.ToString(sel_propobj["fnumber"]),
                                PropValueDataType = (PropValueDataTypeEnum)Convert.ToInt32(sel_propobj["fdatatype"]),
                                ValueId = Convert.ToString(item["fvalueid"]),
                                ValueName = Convert.ToString(item["fvaluename"]),
                                ValueNumber = Convert.ToString(item["fvaluenumber"])
                            });
                        }
                    }
                    //有标准品则不用去匹配配件了，直接返回标准品商品ID
                    var StandardId = GetProductId(productid, auxPropKv);
                    if (StandardId != "")
                    {
                        //直接根据唯一标识获取数据
                        var dm = this.GetDataManager();
                        var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "stk_initstockbill");
                        var formDt = htmlForm.GetDynamicObjectType(this.Context);
                        dm.InitDbContext(this.Context, formDt);
                        //根据id获取
                        var initstockbillObj = dm.Select(Convert.ToString(Arr["pkValue"])) as DynamicObject;
                        if (initstockbillObj != null)
                        {
                            initstockbillObj["fmaterialid"] = StandardId;
                            initstockbillObj["fattrinfo"] = "";
                            initstockbillObj["fdostandard"] = true;
                            dm.Save(initstockbillObj);
                            ResultRows.Add(new Dictionary<string, string> {
                            { "StandardId" , StandardId },
                            {  "rowId" , Convert.ToString(Arr["id"]) } });
                        }

                    }
                }
            }
            //获取盘点单明细所有行
            var Allrows = this.GetQueryOrSimpleParam("allrows", "");
            //盘点单
            if (!Allrows.IsNullOrEmptyOrWhiteSpace()) {
                var AllrowsObj = JArray.Parse(Allrows);
                foreach (var ArrObj in AllrowsObj)
                {
                    var Objrow = JObject.Parse(Convert.ToString(ArrObj["fattrinfo"]));
                    string productId = Convert.ToString(ArrObj?["fmaterialid"]?["id"]);
                    if (productId.IsNullOrEmptyOrWhiteSpace()) continue;
                    if (Objrow["fentity"]?.Count() > 0)
                    {
                        List<PropEntity> propLists = new List<PropEntity>();
                        for (var i = 0; i < Objrow["fentity"].Count(); i++)
                        {
                            var selpropobj = this.Context.LoadBizDataById("sel_prop", Convert.ToString(Objrow["fentity"][i]["fauxpropid"]?["id"]));
                            var fdatatype = Convert.ToString(selpropobj["fdatatype"]);
                            PropValueDataTypeEnum PropValueDataType = PropValueDataTypeEnum.Char; ;
                            switch (fdatatype)
                            {
                                case "1":
                                    PropValueDataType = PropValueDataTypeEnum.Char;
                                    break;
                                case "2":
                                    PropValueDataType = PropValueDataTypeEnum.Numeric;
                                    break;
                                default:
                                    break;
                            }
                            propLists.Add(new PropEntity
                            {
                                PropId = Convert.ToString(Objrow["fentity"][i]["fauxpropid"]?["id"]),
                                PropName = Convert.ToString(Objrow["fentity"][i]["fauxpropid"]?["fname"]),
                                PropNumber = Convert.ToString(Objrow["fentity"][i]["fauxpropid"]?["fnumber"]),
                                PropValueDataType = PropValueDataType,
                                ValueId = Convert.ToString(Objrow["fentity"][i]["fvalueid"]),
                                ValueName = Convert.ToString(Objrow["fentity"][i]["fvaluename"]),
                                ValueNumber = Convert.ToString(Objrow["fentity"][i]["fvaluenumber"])
                            }); ;
                        }
                        //有标准品则不用去匹配配件了，直接返回标准品商品ID
                        var StandardId = GetProductId(productId, propLists);
                        if (StandardId != "")
                        {
                            ResultRows.Add(new Dictionary<string, string> {
                            { "StandardId" , StandardId },
                            {  "rowId" , Convert.ToString(ArrObj["id"]) } });
                        }
                    }
                }
            }
            this.Result.SrvData = ResultRows;
            this.Result.IsSuccess = true;
        }
        //根据商品辅助属性在标准品映射找到符合条件的标准品id
        private string GetProductId(string productId, List<PropEntity> Objrow) {
            var propSelService = this.Container.GetService<IPropSelectionService>();
            var standardProductId = propSelService.LoadStandardProductId(this.Context, productId, Objrow);
            return standardProductId;
        }
    }
}
