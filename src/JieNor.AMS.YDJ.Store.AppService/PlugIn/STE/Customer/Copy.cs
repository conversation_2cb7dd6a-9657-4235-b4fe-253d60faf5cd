using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Customer
{
    /// <summary>
    /// 客户：复制
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("Copy")]
    public class Copy : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLowerInvariant())
            {
                case "aftercreateuidata":
                    var eventData = e.EventData as JObject;
                    if (eventData != null)
                    {
                        eventData["fphone"] = string.Empty;
                        eventData["fentry"] = new JArray();
                    }
                    break;
                default:
                    break;
            }
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                //清空会员ID
                if (dataEntity.DynamicObjectType.Properties.ContainsKey("fmemberno"))
                {
                    dataEntity["fmemberno"] = "";
                }
               
            }

            this.Result.IsSuccess = true;
        }
    }
}