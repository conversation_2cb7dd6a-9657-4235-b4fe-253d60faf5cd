using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using System.Text.RegularExpressions;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.CostAccounting
{
    [InjectService]
    [FormId("ydj_costaccounting")]
    [OperationNo("ydj_order2ydj_costaccounting")]
    public class OrderToCostaccountingConvertService : AbstractConvertServicePlugIn
    {
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Any() == false)
            {
                return;
            }

            foreach(var targetDataEntity in e.TargetDataEntities)
            {
                var faccountamount = Convert.ToDecimal(targetDataEntity["faccountamount"]);
                var fdealsumamount = Convert.ToDecimal(targetDataEntity["fdealsumamount"]);
                var faccountscale = faccountamount == 0 ? 0 : Math.Round(fdealsumamount / faccountamount, 2, MidpointRounding.AwayFromZero);
                targetDataEntity["faccountscale"] = faccountscale;
            }
        }
    }
}
