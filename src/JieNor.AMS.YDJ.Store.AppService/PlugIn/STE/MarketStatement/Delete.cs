using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.MarketStatement
{
    /// <summary>
    /// 卖场对账单：删除
    /// </summary>
    [InjectService]
    [FormId("ydj_marketstatement")]
    [OperationNo("Delete")]
    public class Delete: AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(new Validation_Check(true));
        }
        
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var incomeIds = e.DataEntitys.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                                         .Select(x => Convert.ToString(x["fincomeid"]))
                                         .Where(x => false == string.IsNullOrWhiteSpace(x))
                                         .Distinct()
                                         .ToList();

            if (incomeIds == null || incomeIds.Count <= 0)
            {
                return;
            }

            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var incomeDm = this.Container.GetService<IDataManager>();
            incomeDm.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));

            var incomeEntities = incomeDm.Select(incomeIds).OfType<DynamicObject>().ToArray();
            if (incomeEntities == null || incomeEntities.Length <= 0)
            {
                return;
            }

            foreach (var incomeEntity in incomeEntities)
            {
                var statementStatus = Convert.ToString(incomeEntity["fstatementstatus"]);
                var tranId = Convert.ToString(incomeEntity["ftranid"]);
                if (statementStatus == "3")
                {
                    throw new BusinessException($"删除失败！流水号为[{tranId}]的收支记录是卖场已对账状态，不允许删除!");
                }
                if (statementStatus == "2")
                {
                    incomeEntity["fstatementstatus"] = "1";
                }
            }

            var prepareSaveService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveService.PrepareDataEntity(this.Context, incomeForm, incomeEntities, OperateOption.Create());
            incomeDm.Save(incomeEntities);
        }
    }
}
