using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.YDJService
{
    /// <summary>
    /// 收银单--删除
    /// </summary>
    [InjectService]
    [FormId("ydj_cash")]
    [OperationNo("Delete")]
    public class CashDelete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return TaskStatus(newData); 
            }).WithMessage("删除此数据，客户的储蓄金额将少于0，不允许删除！"));
        }

        private bool TaskStatus(DynamicObject data)
        {
            bool result = true;

            string fusage = (data["fusage"] ?? "").ToString();//支付用途（usage_type_01：定金    usage_type_02：贷款）
            if (fusage == "usage_type_01")//支付用途为（usage_type_01：定金）
            {
                var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_customer");//加载模型
                var dm = this.Container.GetService<IDataManager>();//当前IOC服务容器
                dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
                DynamicObject dyObj = dm.Select(data["fcustomerid"]) as DynamicObject;//加载数据   客户的         update
                if (dyObj == null)
                {
                    result = false;
                }

                string customerId = (data["fcustomerid"] ?? "").ToString();//客户ID
                decimal cash = Convert.ToDecimal(data["fdeposit"] ?? 0);//本次消费金额
                //decimal oldcash = Convert.ToDecimal(data["foldcash"] ?? 0);//历史定金
                //string oldtype = (data["foldtype"] ?? "").ToString();//历史收支类型（1：收    2：退）
                string fszlx = (data["fszlx"] ?? "").ToString();//收支类型（1：收    2：退）
                decimal shoppingCash = CashCalculation(fszlx, cash);

                //通过客户ID查询储值金额
                string sql = @"select fbalance from t_ydj_customer where fid = @customerId";
                List<SqlParam> ss = new List<SqlParam> {
                    new SqlParam ("@customerId", System.Data.DbType.String, customerId)
                };
                decimal customerCash = 0;
                DynamicObjectCollection dd = this.DBService.ExecuteDynamicObject(this.Context, sql, ss);
                if (dd == null || dd.Count() == 0) { return result; }
                customerCash = Convert.ToDecimal(dd[0]["fbalance"] ?? 0);
                if (customerCash + shoppingCash < 0)
                {
                    result = false;
                }
                else
                {
                    dyObj["fbalance"] = Convert.ToDecimal(dyObj["fbalance"] ?? 0) + shoppingCash;
                    dm.Save(dyObj);
                }
            }
            return result;
        }

        /// <summary>
        /// 差值计算
        /// </summary>
        /// <param name="oldType"></param>
        /// <param name="oldCash"></param>
        /// <param name="type"></param>
        /// <param name="cash"></param>
        /// <returns></returns>
        //private decimal CashCalculation(string oldType, decimal oldCash, string type, decimal cash)
        //{
        //    decimal shoppingCash = 0;
        //    if (oldType == "1")//收
        //    {
        //        oldCash = 0 - oldCash;
        //    }
        //    if (type == "2")//退
        //    {
        //        cash = 0 - cash;
        //    }
        //    shoppingCash = cash + oldCash;
        //    return shoppingCash;
        //}
        private decimal CashCalculation(string type, decimal cash)
        {
            decimal shoppingCash = 0;
            if (type == "1")//收
            {
                cash = 0 - cash;
            }
            if (type == "2")//退
            {
                cash = 0 + cash;
            }
            shoppingCash = cash;
            return shoppingCash;
        }
    }
}
