using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.DataTransferObject.Report;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    /// <summary>
    /// 【部门】数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("ydj_dept")]
    public class DeptDataRule : IDataQueryRule
    {


        /// <summary>
        /// 用户所属组织是总部，则可以看到所有的部门信息;
        /// 其他情况，按组织架构树来过滤
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();
            //var orgInfos = DataAuthHelp.GetCurrentUserOrgInfos(ctx);
            //if (orgInfos == null || orgInfos.Count == 0)
            //{
            //    return filter;
            //}

            ////总部或分公司用户，可以看所有数据，不加额外的过滤
            //if (orgInfos.Any(f => "1".EqualsIgnoreCase(f.OrgType) || "2".EqualsIgnoreCase(f.OrgType)))
            //{
            //    return filter;
            //}

            ////TODO 经销商或门店的用户，看自己的部门信息
            //var orgIds = orgInfos.Select(f => f.OrgId).Distinct().ToList();
            //filter.Add(new FilterRowObject()
            //{
            //    Id = "fmainorgid",
            //    Operator = "in",
            //    Value = string.Join(",", orgIds),
            //});

            return filter;
        }



    }
}