using JieNor.AMS.YDJ.Core.DataEntity.Barcode;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.BarcodeMgr;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    public class BaseScanTaskConvertPlugIn : AbstractConvertServicePlugIn
    {
        private bool _isScanTaskCall;

        protected bool IsScanTaskCall
        {
            get { return _isScanTaskCall; }
        }

        protected override void OnInitialized(InitializeServiceEventArgs e)
        {
            base.OnInitialized(e);

            var IsScanTaskCallToken = string.Empty;
            this.Option.TryGetVariableValue("IsScanTaskCall", out IsScanTaskCallToken);
            _isScanTaskCall = string.Equals("true", IsScanTaskCallToken);
            //_isScanTaskCall = false;
        }

        public override void AfterGetSourceBillData(AfterGetSourceBillDataEventArgs e)
        {
            base.AfterGetSourceBillData(e);
            if (IsScanTaskCall == false)
            {
                return;
            }
            var barcodeMgrService = this.UserContext.Container.GetService<IBarcodeMgrService>();
            var matchBarcodeOption = new MatchBarcodeOption
            {
                ActiveEntityKey = "fentity",
                AmountFieldKey = "famount",
                DemandQtyFieldKey = "fqty",
                PriceFieldKey = "fprice",
                QtyFieldKey = "fqty",
                StockQtyFieldKey = "fstockqty",
                BackDateCheckStockId = this.SourceHtmlForm.Id.EqualsIgnoreCase("stk_inventorytransferreq"),
            };
            changeMatchBarcodeOption(matchBarcodeOption);
            e.SourceDataEntities = barcodeMgrService.SplitInoutStockRowByBarcode(this.UserContext, this.SourceHtmlForm, e.SourceDataEntities, matchBarcodeOption);
        }

        public override void OnGroupBillEntryData(OnGroupBillEntryDataEventArgs e)
        {
            base.OnGroupBillEntryData(e);
        }

        protected virtual void changeMatchBarcodeOption(MatchBarcodeOption matchBarcodeOption)
        {

        }
    }
}
