using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 发货排单平台单据转换基类
    /// </summary>
    public class BaseSchedulePlatformConvertPlugIn : AbstractConvertServicePlugIn
    {
        protected List<Dictionary<string, object>> entityDatas;
        protected List<Dictionary<string, object>> serviceDatas;
        protected string schedulePlanNumber;

        /// <summary>
        /// 价格字段键值
        /// </summary>
        protected virtual string priceKey
        {
            get
            {
                return "fprice";
            }
        }

        /// <summary>
        /// 仓库字段映射发货排单平台的发货或收货仓库的键值
        /// </summary>
        protected virtual string storeHouseIdMapKey
        {
            get
            {
                return "fstorehouseidfrom";
            }
        }

        /// <summary>
        /// 发货或收货日期映射键值
        /// </summary>
        protected virtual string dateMapKey
        {
            get
            {
                return "fstockdate";
            }
        }

        /// <summary>
        /// 发货或收货部门映射键值
        /// </summary>
        protected virtual string stockDeptIdMapKey
        {
            get
            {
                return "fstockdeptid";
            }
        }

        /// <summary>
        /// 发货或收货人映射键值
        /// </summary>
        protected virtual string stockStaffIdMapKey
        {
            get
            {
                return "fstockstaffid";
            }
        }

        /// <summary>
        /// 明细id的键值
        /// </summary>
        protected virtual string entityIdKey
        {
            get
            {
                return "fentity_id";
            }
        }

        protected override void OnInitialized(InitializeServiceEventArgs e)
        {
            base.OnInitialized(e);

            schedulePlanNumber = string.Empty;
            this.Option.TryGetVariableValue("schedulePlanNumber", out schedulePlanNumber);
            if (string.IsNullOrWhiteSpace(schedulePlanNumber))
            {
                return;
            }

            this.Option.TryGetVariableValue("entityDatas", out entityDatas);
            this.Option.TryGetVariableValue("serviceDatas", out serviceDatas);
        }

        /// <summary>
        /// 字段值映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeMapFieldValue(BeforeMapFieldValueEventArgs e)
        {
            if (this.entityDatas == null
                || this.entityDatas.Any() == false) return;
            if (e.SourceDataEntities.Any() == false) return;

            var targetFldKey = e.FieldMapObject?.Id;
            var targetField = this.TargetHtmlForm?.GetField(targetFldKey);
            if (targetField == null) return;

            bool isCancel = false;
            object targetValue = null;

            var srcEntryId = e.SourceDataEntities.First().GetString(entityIdKey);
            var existReturnObj = this.entityDatas.FirstOrDefault(o => o.GetString("fsourceentryid").EqualsIgnoreCase(srcEntryId));
            if (existReturnObj == null) return;

            object fscheduleqty = 0m;
            var targetFieldId = targetField.Id.ToLower();
            switch (targetFieldId)
            {
                case "fqty":
                case "fsecqty":
                    if (existReturnObj.TryGetValue("fscheduleqty", out fscheduleqty))
                    {
                        isCancel = true;
                        targetValue = fscheduleqty;
                    }
                    break;
                case "famount":
                    var dPrice = Convert.ToDecimal(e.SourceDataEntities.First().GetValue(priceKey, 0m));
                    if (existReturnObj.TryGetValue("fscheduleqty", out fscheduleqty))
                    {
                        isCancel = true;
                        targetValue = dPrice * Convert.ToDecimal(fscheduleqty);
                    }
                    break;
                case "fvolume":
                    var fvolume = Convert.ToDecimal(e.SourceDataEntities.First().GetValue("fproductid.fvolume", 0m));
                    if (existReturnObj.TryGetValue("fscheduleqty", out fscheduleqty))
                    {
                        isCancel = true;
                        targetValue = fvolume * Convert.ToDecimal(fscheduleqty);
                    }
                    break;
                case "fgross":
                    var fgrossload = Convert.ToDecimal(e.SourceDataEntities.First().GetValue("fproductid.fgrossload", 0m));
                    if (existReturnObj.TryGetValue("fscheduleqty", out fscheduleqty))
                    {
                        isCancel = true;
                        targetValue = fgrossload * Convert.ToDecimal(fscheduleqty);
                    }
                    break;
                case "fstorehouseid":
                    object fstorehouseid = string.Empty;
                    if (existReturnObj.TryGetValue(storeHouseIdMapKey, out fstorehouseid))
                    {
                        isCancel = true;
                        targetValue = fstorehouseid;
                    }
                    break;
                case "fdate":
                    if (existReturnObj.TryGetValue(dateMapKey, out fscheduleqty))
                    {
                        isCancel = true;
                        targetValue = fscheduleqty;
                    }
                    break;
                case "fstockdeptid":
                    if (existReturnObj.TryGetValue(stockDeptIdMapKey, out fscheduleqty))
                    {
                        isCancel = true;
                        targetValue = fscheduleqty;
                    }
                    break;
                case "fstockstaffid":
                    if (existReturnObj.TryGetValue(stockStaffIdMapKey, out fscheduleqty))
                    {
                        isCancel = true;
                        targetValue = fscheduleqty;
                    }
                    break;
                default:
                    targetValue = BeforeMapFieldValue(existReturnObj, targetFieldId, out isCancel);
                    break;
            }

            if (isCancel)
            {
                e.Cancel = true;
                targetField.DynamicProperty.SetValue(e.TargetEntryDataEntity, targetValue);
            }
        }

        protected virtual object BeforeMapFieldValue(Dictionary<string,object> existReturnObj, string targetFieldId, out bool isCancel)
        {
            isCancel = false;
            return null;
        }

        /// <summary>
        /// 计算汇总数据
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities?.Any() != true)
            {
                return;
            }

            if (entityDatas == null || entityDatas.Count <= 0)
            {
                return;
            }


            foreach (var dataEntity in e.TargetDataEntities)
            {
                var ftotalpackageqty = 0m;
                var ftotalcubeqty = 0m;
                var ftotalgrossload = 0m;

                dataEntity["fschedulebillno"] = schedulePlanNumber;

                var fentities = dataEntity["fentity"] as DynamicObjectCollection;
                if (fentities == null || fentities.Count <= 0)
                {
                    continue;
                }

                foreach (var fentity in fentities)
                {
                    ftotalcubeqty += Convert.ToDecimal(fentity["fvolume"]);
                    ftotalpackageqty += Convert.ToDecimal(fentity["fpackqty"]);
                    ftotalgrossload += Convert.ToDecimal(fentity["fgross"]);
                }

                dataEntity["ftotalpackageqty"] = ftotalpackageqty;
                dataEntity["ftotalcubeqty"] = ftotalcubeqty;
                dataEntity["ftotalgrossload"] = ftotalgrossload;
            }
        }
    }
}
