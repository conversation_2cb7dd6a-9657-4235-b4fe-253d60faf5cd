using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 采购订单下推包装清单
    /// </summary>
    [InjectService]
    [FormId("bcm_packorder")]
    [OperationNo("ydj_purchaseorder2bcm_packorder")]
    public class PurchaseOrder2PackOrderConvertPlugIn : AbstractConvertServicePlugIn
    {

        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            var dataEntities = e.TargetDataEntities.ToArray();

            var datas = dataEntities.SelectMany(x =>
            {
                var fentry = x["fentity"] as DynamicObjectCollection;
                return fentry.Where(z => !Convert.ToString(z["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()).Select(y =>
                {
                    return new
                    {
                        fmaterialid = (string)(y["fmaterialid"]),//商品id
                        fsourceentryid = (string)y["fsourceentryid"]//源单行内码(采购订单明细行)
                    };
                });
            });

            SetProductInfo(datas.Select(x => x.fmaterialid).Distinct().ToList(),
                datas.Select(x => x.fsourceentryid).Distinct().ToList(), 
                dataEntities);
        }

        /// <summary>
        /// 根据商品打包类型设置打包类型,包件数和待扫描包数
        /// </summary>
        /// <param name="productIds"></param>
        /// <param name="dataEntities"></param>
        private void SetProductInfo(List<string> productIds,List<string> sourceentryids, DynamicObject[] dataEntities)
        {
            var productForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_product");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, productForm.GetDynamicObjectType(this.UserContext));
            var products = dm.Select(productIds).OfType<DynamicObject>().ToList();
            if (products == null || products.Count <= 0)
            {
                return;
            }

            //查询上游《采购订单》单据体-商品明细 对应商品 的行号
            var sql1 = @"select fentryid,FSeq from t_ydj_poorderentry where fentryid in ({0})".Fmt(sourceentryids.JoinEx(",", true));
            var dbService = this.UserContext.Container.GetService<IDBService>();
            var sourceentries = dbService.ExecuteDynamicObject(this.UserContext, sql1);

            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var fentry in fentries)
                {
                    var productId = (string)(fentry["fmaterialid"]);
                    var product = products.FirstOrDefault(x => (string)(x["id"]) == productId);
                    if (product != null)
                    {
                        fentry["fpacktype"] = product["fpackagtype"];
                        var fbizremainqty = fentry["fbizremainqty"] ?? 0;

                        switch (product["fpackagtype"])
                        {
                            case "1":
                                fentry["fpackcount"] = "1";
                                fentry["fbizqty"] = fbizremainqty;
                                break;
                            case "2":
                                fentry["fpackcount"] = product["fbag"];
                                fentry["fbizqty"] = Convert.ToInt32(fbizremainqty) * Convert.ToInt32(product["fbag"]);
                                break;
                            case "3":
                                fentry["fpackcount"] = product["fpiece"];
                                fentry["fbizqty"] = Convert.ToInt32(fbizremainqty) / Convert.ToInt32(product["fpiece"]);
                                break;
                            default:
                                break;
                        }
                    }

                    if (sourceentries != null && sourceentries.Count > 0)
                    {
                        var fsourceentryid = (string)fentry["fsourceentryid"];
                        var sourceentry = sourceentries.FirstOrDefault(x => (string)x["fentryid"] == fsourceentryid);
                        if (sourceentry != null)
                        {
                            fentry["fsourceseq"] = sourceentry["FSeq"];
                        }
                    }
                }
            }
        }
    }
}
