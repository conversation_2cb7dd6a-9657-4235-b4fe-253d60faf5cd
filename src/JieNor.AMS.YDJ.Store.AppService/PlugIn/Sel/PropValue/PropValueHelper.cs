using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel
{
    /// <summary>
    /// 属性值帮助类
    /// </summary>
    public class PropValueHelper
    {
        /// <summary>
        /// 排除重名的属性值
        /// </summary>
        /// <param name="listData">属性值数据列表</param>
        public static void ExcludeRepeatPropValue(List<Dictionary<string, object>> listData)
        {
            if (listData == null || !listData.Any()) return;

            //是否存在【属性值名称】字段值
            var hasName = listData?.FirstOrDefault()?.ContainsKey("fname") ?? false;
            if (!hasName) return;

            //是否存在【非标】字段值
            var hasNoSuit = listData?.FirstOrDefault()?.ContainsKey("fnosuit") ?? false;

            //如果存在属性值名称相同的，则优先返回【标准属性值】，如果没有标准属性值，则默认返回第一个
            var removeList = new List<Dictionary<string, object>>();

            var groups = listData.GroupBy(o => Convert.ToString(o["fname"]).Trim());
            foreach (var group in groups)
            {
                var valueList = group.ToList();
                if (valueList.Count < 2) continue;

                //最终只留下一个属性值

                //1、如有标准值则先取标准值
                Dictionary<string, object> stayProp = null;
                if (hasNoSuit)
                {
                    stayProp = valueList.FirstOrDefault(o => Convert.ToString(o["fnosuit"]) != "1");
                }

                //2、如没有标准值则任意取一个属性值
                if (stayProp == null)
                {
                    stayProp = valueList.FirstOrDefault();
                }

                //其余的全部移除掉
                foreach (var item in valueList)
                {
                    if (!Convert.ToString(item["fbillhead_id"]).EqualsIgnoreCase(Convert.ToString(stayProp["fbillhead_id"])))
                    {
                        removeList.Add(item);
                    }
                }
            }

            foreach (var item in removeList)
            {
                listData.Remove(item);
            }
        }
    }
}
