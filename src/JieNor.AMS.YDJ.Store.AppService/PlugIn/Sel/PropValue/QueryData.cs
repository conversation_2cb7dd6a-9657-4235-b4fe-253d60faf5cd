using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.PropValue
{
    /// <summary>
    /// 属性值：列表数据查询
    /// </summary>
    [InjectService]
    [FormId("sel_propvalue")]
    [OperationNo("QueryData")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 选配表单标识
        /// </summary>
        private string[] selFormIds = new string[] { "sel_propselectionsingle", "sel_selectionform" };

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    this.PrepareQueryBuilderParameter(e);
                    break;
                    //移除排除重名的属性值
                    //20230609  32061
                    //case "afterListData":
                    //    this.AfterListData(e);
                    //    break;
            }
        }

        /// <summary>
        /// //列表准备查询过滤参数事件：可以在此处自定义额外的过滤条件
        /// </summary>
        /// <param name="e"></param>
        private void PrepareQueryBuilderParameter(OnCustomServiceEventArgs e)
        {
            var queryPara = e.EventData as SqlBuilderParameter;
            if (queryPara != null)
            {
                //如果属性选配已经明确了要查询【由非标录入时生成】为 true 的属性值时，则这里以属性选配时指定的条件为准，不再加下面的过滤条件
                if (queryPara.FilterString.Contains("fnosuitcreate='1'")) return;

                //只查询【由非标录入时生成】为 false 的属性值
                queryPara.FilterString = queryPara.FilterString.JoinFilterString("fnosuitcreate='0'");

                //如果是选配页面发起的属性值基础资料查询，则固定按属性值编码升序排序
                var parentPageFormId = this.ParentPageFormId ?? "";
                if (selFormIds.Contains(parentPageFormId, StringComparer.OrdinalIgnoreCase))
                {
                    queryPara.OrderByString = "fnumber asc";
                }
            }
        }

        /// <summary>
        /// 列表数据查询后触发的事件：可以干预列表数据
        /// </summary>
        /// <param name="e"></param>
        private void AfterListData(OnCustomServiceEventArgs e)
        {
            var parentPageFormId = this.ParentPageFormId ?? "";

            //如果是选配页面发起的属性值基础资料查询，则对列表数据做处理
            if (!selFormIds.Contains(parentPageFormId, StringComparer.OrdinalIgnoreCase)) return;

            var listData = e.EventData as List<Dictionary<string, object>>;

            //排除重名的属性值
            //20230608  32061
            PropValueHelper.ExcludeRepeatPropValue(listData);
        }
    }
}