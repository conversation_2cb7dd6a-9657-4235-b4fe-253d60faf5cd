using JieNor.AMS.YDJ.Store.AppService.Service;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
	/// 运维问题：运维已验证
	/// </summary>
	[InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("operationsverified")]
    public class OperationsVerified : AbstractOperationServicePlugIn
    {


        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fquestionprogress"]).EqualsIgnoreCase("q_progress_05") || Convert.ToString(newData["fquestiontype"]).EqualsIgnoreCase("q_type_02"))
                {
                    return true;
                }
                return false;
            }).WithMessage("对不起，只有问题进度=开发已解决 或 问题类型=操作问题，才允许操作!"));

        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            foreach (var item in e.DataEntitys)
            {
                var currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                item["fquestionprogress"] = "q_progress_09";
                item["ftestfinishtime"] = currentTime;
            }

           
            var res = this.Gateway.InvokeBillOperation(this.Context, "ydj_maintenance", e.DataEntitys, "save", new Dictionary<string, object>() { });

            res.ThrowIfHasError();

            //var dm = this.GetDataManager();
            //dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(Context));
            //var prepareSerivce = this.Container.GetService<IPrepareSaveDataService>();
            //prepareSerivce.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());
            //dm.Save(e.DataEntitys);

            //刷新页面
            this.AddRefreshPageAction();
        }
    }
}


