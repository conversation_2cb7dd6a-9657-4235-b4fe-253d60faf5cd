using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
    /// 运维问题：方案完成
    /// </summary>
    [InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("schemefinish")]
    public class SchemeFinish : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var questionAnalysis = Convert.ToString(newData["fquestionanalysis"]);
                if ("q_analysis_02".Equals(questionAnalysis) || "q_analysis_03".Equals(questionAnalysis))
                {
                    return true;
                }
                else
                {
                    return false;
                }

            }).WithMessage("对不起,只有选择问题定性为内部需求or外部需求时，才能操作！"));
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            List<DynamicObject> modifyobjs = new List<DynamicObject>(); //有变动的单据

            foreach (var item in e.DataEntitys)
            {
                var fschemefinishdate = Convert.ToString(item["fschemefinishdate"])?.Trim();

                if (fschemefinishdate.IsNullOrEmptyOrWhiteSpace())
                {
                    item["fschemefinishdate"] = currentTime;
                    modifyobjs.Add(item);
                }
            }

            if (modifyobjs.Count>0)
            {
                var res = this.Gateway.InvokeBillOperation(this.Context, "ydj_maintenance", e.DataEntitys, "save", new Dictionary<string, object>() { });

                res.ThrowIfHasError();
            }

            //刷新页面
            this.AddRefreshPageAction();
        }
    }
}
