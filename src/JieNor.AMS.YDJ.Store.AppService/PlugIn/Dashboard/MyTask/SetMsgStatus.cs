using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Dashboard.MyTask
{
    /// <summary>
    /// 仪表盘：标记我的消息为（已读/未读）
    /// </summary>
    [InjectService]
    [FormId("dashboard_mytask")]
    [OperationNo("setmsgstatus")]
    public class SetMsgStatus : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var setType = this.GetQueryOrSimpleParam<string>("setType");
            var readStatus = this.GetQueryOrSimpleParam<string>("readStatus");
            if (!setType.EqualsIgnoreCase("single") && !setType.EqualsIgnoreCase("all")) return;
            if (!readStatus.EqualsIgnoreCase("read") && !readStatus.EqualsIgnoreCase("unread")) return;

            var taskForm = this.MetaModelService.LoadFormModel(this.Context, "bf_task");
            var taskDm = this.GetDataManager();
            taskDm.InitDbContext(this.Context, taskForm.GetDynamicObjectType(this.Context));

            var taskObjs = new List<DynamicObject>();
            if (setType.EqualsIgnoreCase("single"))
            {
                var taksId = this.GetQueryOrSimpleParam<string>("taskId");
                if (taksId.IsNullOrEmptyOrWhiteSpace()) return;
                var taskObj = taskDm.Select(taksId) as DynamicObject;
                if (taskObj == null 
                    || !Convert.ToString(taskObj["fexcuter"]).EqualsIgnoreCase(this.Context.UserId)
                    || !Convert.ToString(taskObj["fmainorgid"]).EqualsIgnoreCase(this.Context.Company))
                {
                    return;
                }
                taskObjs.Add(taskObj);
                ProcMsgStatus(taskForm, taskObj, readStatus);
            }
            else
            {
                var sqlParam = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                    new SqlParam("@fexcuter", System.Data.DbType.String, this.Context.UserId)
                };
                var sqlWhere = "fmainorgid=@fmainorgid and fexcuter=@fexcuter";
                var dataReader = this.Context.GetPkIdDataReader(taskForm, sqlWhere, sqlParam);
                taskObjs = taskDm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                if (taskObjs == null || taskObjs.Count() <= 0) return;
                foreach (var taskObj in taskObjs)
                {
                    ProcMsgStatus(taskForm, taskObj, readStatus);
                }
            }

            var pkService = this.Container.GetService<IDataEntityPkService>();
            pkService.AutoSetPrimaryKey(this.Context, taskObjs, taskDm.DataEntityType);

            taskDm.Save(taskObjs);

            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 处理消息状态
        /// </summary>
        /// <param name="taskForm"></param>
        /// <param name="taskObj"></param>
        /// <param name="readStatus"></param>
        private void ProcMsgStatus(HtmlForm taskForm, DynamicObject taskObj, string readStatus)
        {
            if (taskObj == null) return;
            var taskMsgs = taskObj["freadmsg"] as DynamicObjectCollection;
            if (readStatus.EqualsIgnoreCase("read"))
            {
                var taskMsg = taskMsgs.FirstOrDefault(o => Convert.ToString(o["freaderid"]).EqualsIgnoreCase(this.Context.UserId));
                if (taskMsg == null)
                {
                    var entity = taskForm.GetEntryEntity("freadmsg");
                    taskMsg = entity.DynamicObjectType.CreateInstance() as DynamicObject;
                    taskMsg["freadstatus"] = "2";
                    taskMsg["freaderid"] = this.Context.UserId;
                    taskMsg["freaddate"] = DateTime.Now;
                    taskMsgs.Add(taskMsg);
                }
            }
            else
            {
                var toRemoves = new List<DynamicObject>();
                foreach (var taskMsg in taskMsgs)
                {
                    if (Convert.ToString(taskMsg["freaderid"]).EqualsIgnoreCase(this.Context.UserId))
                    {
                        toRemoves.Add(taskMsg);
                    }
                }
                foreach (var item in toRemoves)
                {
                    taskMsgs.Remove(item);
                }
            }
        }
    }
}