using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockOut
{




    /// <summary>
    /// 其它出库单保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary> 
    public class UpdateReserveBill : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if(e.DataEntitys==null)
            {
                return;
            }
            List<string> sql = new List<string>();
            foreach (var item in e.DataEntitys)
            {
                sql.Add(@"/*dialect*/ update t set fsourcestatus = x.fstatus
                            from t_stk_reservebill as t 
                            inner join t_stk_otherstockout x on x.fid='{0}' and t.fsourcepkid = x.fid and t.fsourcetype ='stk_otherstockout' ".Fmt(item["Id"]));

            }

            var dbSvc = this.Container.GetService<IDBServiceEx>();
            dbSvc.ExecuteBatch(this.Context, sql);
        }


    }



    /// <summary>
    /// 其它出库单保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("Save")]
    public class OrderSave : UpdateReserveBill
    { 
    }



    /// <summary>
    /// 其它出库单保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("Submit")]
    public class OrderSubmit : UpdateReserveBill
    {
    }


    /// <summary>
    /// 其它出库单保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("UnSubmit")]
    public class OrderUnSubmit : UpdateReserveBill
    {
    }


    /// <summary>
    /// 其它出库单保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("Audit")]
    public class OrderAudit : UpdateReserveBill
    {
    }

    /// <summary>
    /// 其它出库单保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("UnAudit")]
    public class OrderUnAudit : UpdateReserveBill
    {
    }

}