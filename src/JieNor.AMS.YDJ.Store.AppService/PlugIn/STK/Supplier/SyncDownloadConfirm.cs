using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.DataTransferObject.Integration;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.Supplier
{
    ///// <summary>
    ///// 供应商下载操作
    ///// </summary>
    //[InjectService]
    //[FormId("ydj_supplier")]
    //[OperationNo("SyncDownloadConfirm")]
    //public class SyncDownloadConfirm: SupplierSyncDownloadConfirm
    //{
    //    public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
    //    {
    //        //不要调用基类的OnCustomServiceEvent事件
    //        //base.OnCustomServiceEvent(e);
    //        switch (e.EventName.ToLower())
    //        {
    //            case "savedataentities":
    //                save(e);
    //                break;
    //            default:
    //                return;
    //        }
    //    }

    //    private void save(OnCustomServiceEventArgs e)
    //    {
    //        e.Result = "OK";
    //        var eventData = e.EventData as Dictionary<string, object>;
    //        var userContext = eventData["userContext"] as UserContext;
    //        var dataEntities = eventData["dataEntities"] as List<DynamicObject>;
    //        var cooOperationModes = eventData["cooOperationModes"] as List<Dictionary<string, string>>;

    //        var publishInfos = dataEntities.Select(x => new Dictionary<string, string>
    //        {
    //            { "fpublishcid", Convert.ToString(x["fpublishcid"]) },
    //            { "fpublishpid", Convert.ToString(x["fpublishcid_pid"]) },
    //            { "fmainorgid",Convert.ToString(x["fmainorgid"])}
    //        }).Distinct(x => string.Join("|", x["fpublishcid"], x["fpublishpid"], x["fmainorgid"])).ToList();

    //        var metaModelService = userContext.Container.GetService<IMetaModelService>();
    //        var supplierForm = metaModelService.LoadFormModel(userContext, "ydj_supplier");
    //        var suppliers = GetPublishInfo(userContext, publishInfos, supplierForm, cooOperationModes);
    //        var saveEntities = new List<DynamicObject>();

    //        foreach(var dataEntity in dataEntities)
    //        {
    //            var supplier = suppliers.FirstOrDefault(x => x["cooCompanyId"] == Convert.ToString(dataEntity["fpublishcid"]) &&
    //                                                         x["cooProductId"] == Convert.ToString(dataEntity["fpublishcid_pid"]) &&
    //                                                         x["companyId"]==Convert.ToString(dataEntity["fmainorgid"]));

    //            //如果是直营模式直接返回
    //            if (supplier != null && supplier["operationMode"] == "1")
    //            {
    //                saveEntities.Add(dataEntity);
    //            }
    //        }

    //        if (saveEntities == null || saveEntities.Count <= 0)
    //        {
    //            return;
    //        }

    //        var dm = userContext.Container.GetService<IDataManager>();
    //        dm.InitDbContext(userContext, supplierForm.GetDynamicObjectType(userContext));
    //        var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
    //        prepareSaveDataService.PrepareDataEntity(userContext, supplierForm, saveEntities.ToArray(), OperateOption.Create());
    //        dm.Save(saveEntities);
    //    }
    //}

    /// <summary>
    /// 供应商下载操作
    /// </summary>
    [InjectService]
    [FormId("ydj_supplier")]
    [OperationNo("SyncDownloadConfirm")]
    public class SyncDownloadConfirm : AbstractSyncDownloadConfirmPlugIn
    {
        protected override bool SaveDataEntities(UserContext userContext, 
                                                 HtmlForm htmlForm, 
                                                 List<DynamicObject> dataEntities, 
                                                 List<DynamicObject> rollbackEntities, 
                                                 List<Dictionary<string, object>> billDatas, 
                                                 List<Dictionary<string, string>> cooOperationModes, 
                                                 List<ChainDataIdToLocalDataIdMapsSet> chainDataIdToLocalDataIdMaps)
        {
            var publishInfos = dataEntities.Select(x => new Dictionary<string, string>
            {
                { "fpublishcid", Convert.ToString(x["fpublishcid"]) },
                { "fpublishpid", Convert.ToString(x["fpublishcid_pid"]) },
                { "fmainorgid",Convert.ToString(x["fmainorgid"])}
            }).Distinct(x => string.Join("|", x["fpublishcid"], x["fpublishpid"], x["fmainorgid"])).ToList();

            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var supplierForm = metaModelService.LoadFormModel(userContext, "ydj_supplier");
            var suppliers = SupplierSyncDownloadConfirm.GetPublishInfo(userContext, publishInfos, supplierForm, cooOperationModes);
            var saveEntities = new List<DynamicObject>();

            foreach (var dataEntity in dataEntities)
            {
                var supplier = suppliers.FirstOrDefault(x => x["cooCompanyId"] == Convert.ToString(dataEntity["fpublishcid"]) &&
                                                             x["cooProductId"] == Convert.ToString(dataEntity["fpublishcid_pid"]) &&
                                                             x["companyId"] == Convert.ToString(dataEntity["fmainorgid"]));

                //如果是直营模式直接返回
                if (supplier != null && supplier["operationMode"] == "1")
                {
                    saveEntities.Add(dataEntity);
                }
            }

            if (saveEntities == null || saveEntities.Count <= 0)
            {
                return true;
            }

            var dm = userContext.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, supplierForm.GetDynamicObjectType(userContext));
            var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(userContext, supplierForm, saveEntities.ToArray(), OperateOption.Create());

            for (int i = 0, nSize = 200; i < saveEntities.Count; i += nSize)
            {
                var splitDatas = saveEntities.GetRange(i, Math.Min(nSize, saveEntities.Count - i));
                dm.Save(splitDatas, null, OperateOption.InstanceBulkCopyAndNoCache);
            }

            return true;
        }
    }
}
