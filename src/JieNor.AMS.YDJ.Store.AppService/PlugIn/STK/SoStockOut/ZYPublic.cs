using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockOut
{

    /// <summary>
    /// 一直营销售出库不允许删除，反审核，作废
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("unaudit")]
    public class E3unaudit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            string e3Auto = this.GetQueryOrSimpleParam<string>("zyAuto", "");
            base.PrepareValidationRules(e);
            e.Rules.Add(new Validation_ZY(e3Auto, this.OperationName));
        }
    }

    /// <summary>
    /// 一件代发的采购入库和销售出库不允许提交，审核，删除，反审核
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("delete")]
    public class E3stockdelete : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            string zyAuto = this.GetQueryOrSimpleParam<string>("zyAuto", "");
            base.PrepareValidationRules(e);
            e.Rules.Add(new Validation_ZY(zyAuto, this.OperationName));
        }
    }


    /// <summary>
    /// 一件代发的采购入库和销售出库不允许提交，审核，删除，反审核
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("cancel")]
    public class E3stockCancel : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            string zyAuto = this.GetQueryOrSimpleParam<string>("zyAuto", "");
            base.PrepareValidationRules(e);
            e.Rules.Add(new Validation_ZY(zyAuto, this.OperationName));
        }
    }
    /// <summary>
    /// 
    /// </summary>
    public class Validation_ZY : AbstractBaseValidation
    {

        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }
        public Validation_ZY(string zyAuto, string opName)
        {
            this._zyAuto = zyAuto;
            this._opName = opName;
        }
        /// <summary>
        /// 保存提交
        /// </summary>
        private string _zyAuto { get; set; }
        private string _opName { get; set; }

        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }
            if (!_zyAuto.Equals("true"))
            {
                //CheckChangeInfo(userCtx, formInfo, dataEntities, result, option, operationNo);
            }


            return result;
        }



        /// <summary>
        /// 取消用这个判断
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="result"></param>
        /// <param name="option"></param>
        private void CheckChangeInfo(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys, ValidationResult result, OperateOption option, string operationNo)
        {
            if (dataEntitys == null || !dataEntitys.Any()) return;
            foreach (var item in dataEntitys)
            {
                var managemodel = Convert.ToString(item["fmanagemodel"]);
                if (managemodel.Equals("1"))
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"当前单据【{item["fbillno"]}】审核通过后已自动同步总部，不允许该操作。！",
                        DataEntity = item,
                    });

                }
            }
        }

    }
}
