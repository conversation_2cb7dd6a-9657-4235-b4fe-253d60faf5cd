using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PoStockReturn
{
    /// <summary>
    /// 采购退货单：反审核
    /// 作者：zpf
    /// 日期：2022-06-10
    /// </summary>
    [InjectService]
    [FormId("stk_postockreturn")]
    [OperationNo("UnAudit")]
    public class UnAudit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理规则校验
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            IPurchaseOrderService orderService = this.Container.GetService<IPurchaseOrderService>();
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return orderService.ChangeOrSubmitStatus(this.Context, newData);
            }).WithMessage("对不起，上游采购订单变更中，已禁止此操作！"));
        }

        public virtual void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }
            //获取源头单据采购入库单信息
            var postockinNos = e.DataEntitys.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "stk_postockin").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            var postockins = this.Context.LoadBizDataByFilter("stk_postockin", " fbillno in ('{0}') ".Fmt(string.Join("','", postockinNos)));

            //获取源头单据采购订单信息
            var fsourcenumbers = postockins.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "ydj_purchaseorder").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            var purchaseorders = this.Context.LoadBizDataByFilter("ydj_purchaseorder", " fbillno in ('{0}') ".Fmt(string.Join("','", fsourcenumbers)));

            foreach (var purchaseorder in purchaseorders)
            {
                var purchaseOrderEntrys = (purchaseorder["fentity"] as DynamicObjectCollection).ToList();
                List<DynamicObject> productlist = new List<DynamicObject>();
                foreach (var pur in purchaseOrderEntrys)
                {
                    if (!pur["fsuitcombnumber"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var product = this.Context.LoadBizDataById("ydj_product", pur["fmaterialid"].ToString(), true);
                        string str = product["fsuiteflag"].ToString();
                        if (product["fsuiteflag"].ToString().ToLower() == "true")
                        {
                            productlist.Add(product);
                        }
                    }
                }

                Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatusWhitfissuitflag(purchaseorder, productlist);
            }

            if (purchaseorders.Count > 0)
            {
                this.Context.SaveBizData("ydj_purchaseorder", purchaseorders);
            }

            // 反写销售合同【已采购入库数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackPurInQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
        }
    }
}
