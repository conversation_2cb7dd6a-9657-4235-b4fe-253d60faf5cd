using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Auth;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework.Interface.Log;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.ProductAuth
{

    /// <summary>
    /// 专供商品授权：清除专供商品
    /// </summary>
    [InjectService]
    [FormId("ydj_productzgdialog_other")]
    [OperationNo("clearzgproduct_other")]
    public class ClearZGProduct_other : AbstractOperationServicePlugIn
    {

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var formid = "ydj_productauth_other";
            string type = this.GetQueryOrSimpleParam("type", "");
            var dm = this.GetDataManager();
            var productauthHtml = this.MetaModelService.LoadFormModel(this.Context, formid);
            dm.InitDbContext(this.Context, productauthHtml.GetDynamicObjectType(this.Context));
            base.BeginOperationTransaction(e);
            foreach (var item in e.DataEntitys)
            {
                DynamicObjectCollection orgs = item["forgs"] as DynamicObjectCollection;
                DynamicObjectCollection products = item["fproducts"] as DynamicObjectCollection;
                List<string> deliverids = orgs.Where(_ => !string.IsNullOrEmpty(Convert.ToString(_["fdeliverid"]))).Select(_ => Convert.ToString(_["fdeliverid"])).ToList();
                var deliverItems = this.Context.LoadBizBillHeadDataById("bas_deliver", deliverids, "fname,fnumber");
                string deliverNums = string.Join(",", deliverItems.Select(_ => _["fnumber"]).ToList());
                List<string> orgids = orgs.Where(_ => !string.IsNullOrEmpty(Convert.ToString(_["forgid"]))).Select(_ => Convert.ToString(_["forgid"])).ToList();
                DynamicObjectCollection products_auot = item["fproducts_auot"] as DynamicObjectCollection;
                List<string> productids_auot = products_auot.Where(_ => !string.IsNullOrEmpty(Convert.ToString(_["fproductid_o"]))).Select(_ => Convert.ToString(_["fproductid_o"])).ToList();
                List<string> productids = products.Where(_ => !string.IsNullOrEmpty(Convert.ToString(_["fproductid_o"]))).Select(_ => Convert.ToString(_["fproductid_o"])).ToList();

                var prdItems = this.Context.LoadBizDataById("ydj_product", productids);
                var prdItems_auot = this.Context.LoadBizDataById("ydj_product", productids_auot);
                //获取专供商品、授权商品的 商品：销售组织列表键值
                Dictionary<string, List<string>> products_dic = prdItems.ToDictionary(k => Convert.ToString(k["id"]), v => (v["fsaleorgentry"] as DynamicObjectCollection).Select(o => Convert.ToString(o["fsaleorgid"])).ToList());
                Dictionary<string, List<string>> products_auot_dic = prdItems_auot.ToDictionary(k => Convert.ToString(k["id"]), v => (v["fsaleorgentry"] as DynamicObjectCollection).Select(o => Convert.ToString(o["fsaleorgid"])).ToList());

                string supplierIds = "";
                foreach (string orgItem in orgs.Select(_ => _["forgid"]).ToList())
                {
                    supplierIds += "'" + orgItem + "',";
                }
                if (supplierIds.Length > 0)
                {
                    supplierIds = supplierIds.Substring(0, supplierIds.Length - 1);
                }
                var orgItems = this.Context.LoadBizDataByFilter("bas_agent", " fid in ({0})".Fmt(supplierIds));

                string ids = "";
                foreach (var orgid in orgids)
                {
                    ids += "'" + orgid + "',";
                }
                if (string.IsNullOrWhiteSpace(ids)) continue;
                ids = ids.Substring(0, ids.Length - 1);

                string filterstring = "and 1=1 ";

                List<string> citys = orgs.Where(_ => !string.IsNullOrEmpty(Convert.ToString(_["fcityid"]))).Select(_ => Convert.ToString(_["fcityid"])).ToList();
                if (citys.Count > 0)
                {
                    filterstring += $" and fcityid in ('{string.Join("','", citys)}') and fdeliverid in ('{string.Join("','", deliverids)} ') ";
                }

                List<DynamicObject> productauths_old = this.Context.LoadBizDataByFilter("ydj_productauth", $" forgid in ({ ids }) and  fforbidstatus = 0");
                List<DynamicObject> productauths = this.Context.LoadBizDataByFilter(formid, $" forgid in ({ ids }) {filterstring} ");
                var str = string.Empty;

                List<string> removeZGPrdAuthEntryIds = new List<string>();
                List<string> removeZGPrdAuthExcludeEntryIds = new List<string>();
                List<string> removePrdAuthEntryIds = new List<string>();
                List<string> removePrdAuthExcludeEntryIds = new List<string>();

                foreach (var productauthItem in productauths)
                {
                    string fsoureid = Convert.ToString(productauthItem["fsoureid"]);
                    //if (fsoureid.IsNullOrEmptyOrWhiteSpace()) continue;
                    //var productauthItem_old = productauths_old.Where(o => Convert.ToString(o["id"]).EqualsIgnoreCase(fsoureid)).FirstOrDefault();
                    foreach (var productauthItem_old in productauths_old) 
                    {
                        var productAuthEntrys_old = productauthItem_old?["fproductauthexclude"] as DynamicObjectCollection;
                        var productAuthList_old = productauthItem_old?["fproductauthlist"] as DynamicObjectCollection;

                        var productAuthEntrys_ot = productauthItem?["fproductauthexclude_ot"] as DynamicObjectCollection;
                        var productAuthList_ot = productauthItem?["fproductauthlist_ot"] as DynamicObjectCollection;
                        var deliverid = Convert.ToString(productauthItem?["fdeliverid"]);
                        var fsaleorgid = Convert.ToString(productauthItem?["fsaleorgid"]);

                        var orgid = Convert.ToString(productauthItem?["forgid"]);
                        string deliverNum = deliverItems.Where(o => Convert.ToString(o["id"]).EqualsIgnoreCase(deliverid)).Select(_ => Convert.ToString(_["fnumber"]))?.FirstOrDefault();
                        string supplierNum = orgItems.Where(o => Convert.ToString(o["id"]).EqualsIgnoreCase(orgid)).Select(_ => Convert.ToString(_["fnumber"]))?.FirstOrDefault();
                        //特殊商品授权和商品授权 授予组织对应才需要往下走。按经销商记录操作日志
                        if (!(orgid.EqualsIgnoreCase(Convert.ToString(productauthItem_old?["forgid"])) && Convert.ToString(productauthItem?["fcityid"]).EqualsIgnoreCase(Convert.ToString(productauthItem_old?["fcityid"])))) continue;

                        if (type.EqualsIgnoreCase("type_01"))
                        {
                            var productLst = new List<string>();
                            foreach (var productItem in products)
                            {
                                if (!string.IsNullOrEmpty(Convert.ToString(productItem["fproductid_o"])))
                                {
                                    var productAuthEntryItems_ot = productAuthEntrys_ot?.Where(_ => Convert.ToString(_["fproductid_o"]) == Convert.ToString(productItem["fproductid_o"])).ToList();
                                    //送达方销售组织匹配商品档案销售组织，如果当前送达方关联组织 不在商品的销售组织中则跳过当前商品
                                    products_dic.TryGetValue(Convert.ToString(productItem["fproductid_o"]), out List<string> product_orgids);
                                    if (!fsaleorgid.IsNullOrEmptyOrWhiteSpace() && (product_orgids.Count == 0 || !product_orgids.Contains(fsaleorgid)))
                                    {
                                        continue;
                                    }
                                    if (productAuthEntryItems_ot != null && productAuthEntryItems_ot.Count > 0)
                                    {
                                        foreach (var productAuthEntryItem_ot in productAuthEntryItems_ot)
                                        {
                                            productAuthEntrys_ot.Remove(productAuthEntryItem_ot);
                                            removeZGPrdAuthExcludeEntryIds.Add(Convert.ToString(productAuthEntryItem_ot["id"]));
                                        }
                                    }
                                    productLst.Add(Convert.ToString(productItem["fproductid_o"]));
                                    //如果商品授权清单存在则需要 清除商品然后 同步到商品授权清单中
                                    var productAuthEntryItems_old = productAuthEntrys_old?.Where(_ => Convert.ToString(_["fproductid_o"]) == Convert.ToString(productItem["fproductid_o"])).ToList();
                                    if (productAuthEntryItems_old != null && productAuthEntryItems_old.Count > 0)
                                    {
                                        foreach (var productAuthEntryItem_old in productAuthEntryItems_old)
                                        {
                                            productAuthEntrys_old.Remove(productAuthEntryItem_old);
                                            removePrdAuthExcludeEntryIds.Add(Convert.ToString(productAuthEntryItem_old["id"]));
                                        }
                                    }
                                }
                            }
                            var productNumLst = prdItems.Where(o => productLst.Contains(Convert.ToString(o["id"]))).Select(o => Convert.ToString(o["fnumber"])).ToList();
                            if (productNumLst.Count > 0)
                            {
                                str = $"执行了：【清除专供商品】操作,商品：{string.Join(",", productNumLst) }";
                            }
                            else
                            {
                                str = $"执行了：【清除专供商品】操作,商品：无可清除商品";
                            }
                        }

                        if (type.EqualsIgnoreCase("type_02"))
                        {
                            var productLst_auto = new List<string>();
                            //授权商品
                            foreach (var productItem_auot in products_auot)
                            {
                                if (!string.IsNullOrEmpty(Convert.ToString(productItem_auot["fproductid_o"])))
                                {
                                    //送达方销售组织匹配商品档案销售组织，如果当前送达方关联组织 不在商品的销售组织中则跳过当前商品
                                    products_auot_dic.TryGetValue(Convert.ToString(productItem_auot["fproductid_o"]), out List<string> product_orgids);
                                    if (!fsaleorgid.IsNullOrEmptyOrWhiteSpace() && (product_orgids.Count == 0 || !product_orgids.Contains(fsaleorgid)))
                                    {
                                        continue;
                                    }
                                    var productAuthListItems_ot = productAuthList_ot?.Where(_ => Convert.ToString(_["fproductid"]) == Convert.ToString(productItem_auot["fproductid_o"])).ToList();
                                    if (productAuthListItems_ot != null && productAuthListItems_ot.Count > 0)
                                    {
                                        foreach (var ListItems_ot in productAuthListItems_ot)
                                        {
                                            productAuthList_ot.Remove(ListItems_ot);
                                            removeZGPrdAuthEntryIds.Add(Convert.ToString(ListItems_ot["id"]));
                                        }
                                    }
                                    productLst_auto.Add(Convert.ToString(productItem_auot["fproductid_o"]));
                                    //如果商品授权清单存在则需要 清除商品然后 同步到商品授权清单中
                                    var productAuthListItems_old = productAuthList_old?.Where(_ => Convert.ToString(_["fproductid"]) == Convert.ToString(productItem_auot["fproductid_o"])).ToList();
                                    if (productAuthListItems_old != null && productAuthListItems_old.Count > 0)
                                    {
                                        foreach (var productAuthListItem_oldItem in productAuthListItems_old)
                                        {
                                            productAuthList_old.Remove(productAuthListItem_oldItem);
                                            removePrdAuthEntryIds.Add(Convert.ToString(productAuthListItem_oldItem["id"]));
                                        }
                                    }
                                }
                            }
                            var productNumLst = prdItems_auot.Where(o => productLst_auto.Contains(Convert.ToString(o["id"]))).Select(o => Convert.ToString(o["fnumber"])).ToList();
                            if (productNumLst.Count > 0)
                            {
                                str = $"执行了：【清除非专供商品】操作,商品：{string.Join(",", productNumLst) }";
                            }
                            else
                            {
                                str = $"执行了：【清除非专供商品】操作,商品：无可清除商品";
                            }
                        }
                        this.Logger.WriteLog(this.Context, new LogEntry
                        {
                            BillIds = productauthItem["id"] as string,
                            BillNos = productauthItem["fnumber"] as string,
                            BillFormId = formid,
                            OpName = "清除商品",
                            OpCode = this.OperationNo,
                            Content = "经销商：{0}；送达方：{1}；{2}".Fmt(supplierNum, deliverNum, str),
                            DebugData = "经销商：{0}；送达方：{1}；{2}".Fmt(supplierNum, deliverNum, str),
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03,
                        });

                        if (!productauthItem_old.IsNullOrEmptyOrWhiteSpace())
                        {
                            //旧的商品授权添加记录
                            this.Logger.WriteLog(this.Context, new LogEntry
                            {
                                BillIds = productauthItem_old["id"] as string,
                                BillNos = productauthItem_old["fnumber"] as string,
                                BillFormId = "ydj_productauth",
                                OpName = "特殊商品授权清单-清除商品",
                                OpCode = this.OperationNo,
                                Content = "经销商：{0}；送达方：{1}；{2}".Fmt(supplierNum, deliverNum, str),
                                DebugData = "经销商：{0}；送达方：{1}；{2}".Fmt(supplierNum, deliverNum, str),
                                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                                Level = Enu_LogLevel.Info.ToString(),
                                LogType = Enu_LogType.RecordType_03,
                            });
                        } 
                    } 
                }
                var attachprepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                attachprepareSaveDataService.PrepareDataEntity(this.Context, productauthHtml, productauths.ToArray(), Framework.SuperOrm.OperateOption.Create());
                dm.Save(productauths);
                //同步到商品授权清单中。
                this.Context.SaveBizData("ydj_productauth", productauths_old);

                try
                {
                    // 记录删除的明细行id
                    DebugUtil.WriteLogToFile("清除专供商品：" + new
                    {
                        RemoveZGPrdAuthExcludeEntryIds = removeZGPrdAuthExcludeEntryIds,
                        RemoveZGPrdAuthEntryIds = removeZGPrdAuthEntryIds,
                        RemovePrdAuthExcludeEntryIds = removePrdAuthExcludeEntryIds,
                        RemovePrdAuthEntryIds = removePrdAuthEntryIds
                    }.ToJson(), "清除专供商品");
                }
                catch (Exception ex)
                {
                    this.Container.GetService<ILogServiceEx>().Error("清除专供商品", ex);
                }

                var clearCacheProductAuths = productauths.Concat(productauths_old);
                this.Container.GetService<IProductAuthService>().ClearPrdAuthCache(this.Context, clearCacheProductAuths, this.HtmlForm, this.OperationNo);

            }

            if (e.DataEntitys != null && e.DataEntitys.Length > 0)
            {
                //清除缓存
                ProductDataIsolateHelper.ClearCacheByBiz(this.Context, new PrdDataIsolateChannelMessage
                {
                    Message = $"{this.HtmlForm?.Caption}-{this.OperationNo}",
                    TopCompanyId = this.Context.TopCompanyId
                });
            }
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {

            if (e.DataEntitys.Count() <= 0) return;
            string type = this.GetQueryOrSimpleParam("type", "");
            HashSet<string> allorgid = new HashSet<string>();
            HashSet<string> allRemoveZGProductId = new HashSet<string>();
            HashSet<string> allRemoveAuthProductId = new HashSet<string>();
            foreach (var item in e.DataEntitys)
            {
                var orgs = item["forgs"] as DynamicObjectCollection; // 经销商送达方数据
                if (orgs != null)
                {
                    var orgids = orgs.Where(_ => _?["forgid"] != null && _?["forgid"].ToString() != "")
                                    .Select(_ => _["forgid"].ToString())
                                    .ToList(); // 经销商主键集合
                    allorgid.UnionWith(orgids);
                }
                var products = item["fproducts"] as DynamicObjectCollection;
                if (products != null && type.EqualsIgnoreCase("type_01"))
                {
                    var proids = products.Where(_ => _?["fproductid_o"] != null && _?["fproductid_o"].ToString() != "")
                                    .Select(_ => _["fproductid_o"].ToString())
                                    .ToList();
                    allRemoveZGProductId.UnionWith(proids);
                }
                var products_auto = item["fproducts_auot"] as DynamicObjectCollection;
                if (products_auto != null && type.EqualsIgnoreCase("type_02"))
                {
                    var proids = products_auto.Where(_ => _?["fproductid_o"] != null && _?["fproductid_o"].ToString() != "")
                                    .Select(_ => _["fproductid_o"].ToString())
                                    .ToList(); // 
                    allRemoveAuthProductId.UnionWith(proids);
                }
            }
            List<string> finalAllorgid = allorgid.ToList();
            if (allRemoveAuthProductId.Count <= 0 && allRemoveZGProductId.Count <= 0) { return; }
            if (finalAllorgid.Count <= 0) return;
            // 1. 获取此次修改的授权清单
            List<DynamicObject> ProductAuths = this.Context.LoadBizDataByFilter("ydj_productauth", $" forgid in ({ string.Join(",", finalAllorgid.Select(id => $"'{id}'")) }) and  fforbidstatus = 0").ToList();
            StringBuilder dataLog = new StringBuilder();
            dataLog.Append($"ydj_productauth:清除专供商品【{this.OperationNo}】->-begin:{DateTime.Now.ToString("yyyyMMddHHmmss")},一级组织:{string.Join(",", finalAllorgid)}");
            dataLog.AppendLine();
             Task task = new Task(() =>
            {
                this.Container.GetService<IProductAuthService>().ClearSecondAgentAuth(this.Context, ProductAuths, null,
                    allRemoveZGProductId.ToList(), allRemoveAuthProductId.ToList(), this.HtmlForm, this.OperationNo, "清除专供商品");
            });
            ThreadWorker.QuequeTask(task, result => {
                dataLog.AppendLine($"ydj_productauth:清除专供商品【{this.OperationNo}】->-end:{DateTime.Now.ToString("yyyyMMddHHmmss")},{result.Exception}");
                this.Logger.WriteLogToFile(dataLog.ToString(), "清除专供商品");
            });
        }
    }
}
