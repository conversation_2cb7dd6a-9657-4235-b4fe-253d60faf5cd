using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Auth;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.ProductAuth
{
    /// <summary>
    /// 商品授权：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_productauth")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {

        private List<string> allRemoveExProductId = new List<string>();
        private List<string> allAddExProductId = new List<string>();
        private List<string> removeAuthProductId = new List<string>();
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            var seqSvc = this.Container.GetService<ISequenceService>();
            foreach (var item in e.DataEntitys)
            {
                if (item.DataEntityState.FromDatabase == false)
                {
                    // 没有名字就给个默认名字
                    if (item["fname"].IsNullOrEmptyOrWhiteSpace())
                    {
                        item["fname"] = seqSvc.GetSequence<string>();
                    }
                }
            }
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        { 
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys.Count() == 1) { 
                var item = e.DataEntitys[0];
                var old_info = this.Context.LoadBizDataById(this.HtmlForm.Id, item["id"].ToString());
                if (old_info != null) {
                    var new_exProductIds = (item["fproductauthexclude"] as DynamicObjectCollection).Select(p => Convert.ToString(p["fproductid_o"])).ToList();
                    var old_exProductIds = (old_info["fproductauthexclude"] as DynamicObjectCollection).Select(p => Convert.ToString(p["fproductid_o"])).ToList();
                    var new_authProductIds = (item["fproductauthlist"] as DynamicObjectCollection).Select(p => Convert.ToString(p["fproductid"])).ToList();
                    var old_authProductIds = (old_info["fproductauthlist"] as DynamicObjectCollection).Select(p => Convert.ToString(p["fproductid"])).ToList();
                    allAddExProductId.AddRange(new_exProductIds.Except(old_exProductIds));
                    allRemoveExProductId.AddRange(old_exProductIds.Except(new_exProductIds));
                    removeAuthProductId.AddRange(old_authProductIds.Except(new_authProductIds));
                }
            }
        }
       
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            //接口更新商品授权清单时送达方、销售组织跟随当前授予组织联动变
            //AlterDeliverAndSaleOrg(e.DataEntitys);

            //如果是专供新菜单操作，保存时需要从新表同步到旧表中。（例外商品、授权商品）
            //if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_productauth_other"))
            //{
            //    foreach (var productauthItem in e.DataEntitys)
            //    {
            //        //例外商品更新
            //        var productAuthEntrys = productauthItem?["fproductauthexclude"] as DynamicObjectCollection;
            //        var productAuthEntrys_ot = productauthItem?["fproductauthexclude_ot"] as DynamicObjectCollection;
            //        productAuthEntrys.Clear();
            //        foreach (var productItem in productAuthEntrys_ot)
            //        {
            //            if (!string.IsNullOrEmpty(Convert.ToString(productItem["fproductid_o"])))
            //            {
            //                var detail = productAuthEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
            //                detail["fproductid_o"] = productItem["fproductid_o"];
            //                productAuthEntrys.Add(detail);
            //            }
            //        }
            //        //授权商品更新
            //        var productAuthList = productauthItem?["fproductauthlist"] as DynamicObjectCollection;
            //        var productAuthList_ot = productauthItem?["fproductauthlist_ot"] as DynamicObjectCollection;
            //        productAuthList.Clear();
            //        foreach (var productItem in productAuthList_ot)
            //        {
            //            if (!string.IsNullOrEmpty(Convert.ToString(productItem["fproductid"])))
            //            {
            //                var detail = productAuthList.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
            //                detail["fproductid"] = productItem["fproductid"];
            //                detail["fnopurchase"] = productItem["fnopurchase"];
            //                productAuthList.Add(detail);
            //            }
            //        }
            //    }
            //}

            // 过滤【新增】的【授权组织类型】="经销商"的《商品授权清单》
            var orgIds = e.DataEntitys
                .Where(s => !s.DataEntityState.FromDatabase)
                .Select(s => Convert.ToString(s["forgid"])).ToList();
            if (orgIds.IsNullOrEmpty()) return;

            var agentIds = this.Context.LoadBizBillHeadDataById("bas_organization", orgIds, "forgtype").Where(s => Convert.ToString(s["forgtype"]).EqualsIgnoreCase("4")).Select(s => Convert.ToString(s["id"]));

            if (agentIds.Any())
            {
                var productAuthService = this.Container.GetService<IProductAuthService>();
                var productAuths = e.DataEntitys.Where(s => agentIds.Contains(Convert.ToString(s["forgid"])));
                productAuthService.AddSpecial(this.Context, productAuths, false);
            }

        }
        /// <summary>
        /// 更新商品授权清单时送达方、销售组织跟随当前授予组织联动变
        /// </summary>
        /// <param name="datas"></param>
        private void AlterProductAuth_Other(DynamicObject[] datas)
        {
            if (datas == null || datas.Length == 0)
            {
                return;
            }
            //如果是更换营业执照场景直接走更新特殊商品授权清单逻辑 
            this.Option.TryGetVariableValue("IsChangeLicenseNew", out bool IsChangeLicenseNew);

            var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), datas, false);

            IMuSiService muSiService = this.Container.GetService<IMuSiService>();
            foreach (var data in datas)
            {
                var forgid = Convert.ToString(data["forgid"]);

                var fcityid = Convert.ToString(data["fcityid"]);
                var productAuth_other = this.Context.LoadBizBillHeadDataByACLFilter("ydj_productauth_other", $"forgid = '{forgid}' and fcityid = '{fcityid}' and fforbidstatus ='0' ", "fnumber,fid,fsaleorgid").ToList();
                //获取还未生成特殊商品授权清单的送达方维度
                var ProductAuthObjs_deliver = LoadProductAuthByDeliver(Convert.ToString(data["id"]));
                //如果是新增需要创建专供授权，如果找不到特殊商品授权清单需要生成。
                if ((!productAuth_other.Any() || ProductAuthObjs_deliver.Any()) && !IsChangeLicenseNew)
                {
                    muSiService.SaveProductAuth_other(this.Context, "ydj_productauth_other", ProductAuthObjs_deliver);
                }
                //如果商品授权清单 授予组织 + 城市 关联的所有送达方都有特殊商品授权则更新表头
                else
                {
                    var id = Convert.ToString(data["id"]);
                    var productAuth_others = this.Context.LoadBizDataByACLFilter("ydj_productauth_other", $"fsoureid = '{Convert.ToString(data["id"])}'", true).ToList();
                    if (productAuth_others.IsNullOrEmptyOrWhiteSpace()) continue;

                    List<DynamicObject> saveObjs = new List<DynamicObject>();
                    foreach (var obj in productAuth_others)
                    {
                        var deliver = obj["fdeliverid_ref"] as DynamicObject;
                        var fsaleorgid = Convert.ToString(deliver?["fsaleorgid"]);
                        if (!fsaleorgid.IsNullOrEmptyOrWhiteSpace())
                        {
                            obj["fsaleorgid"] = fsaleorgid;
                        }
                        obj["fforbidstatus"] = 0;
                        obj["fmodifydate"] = DateTime.Now;
                        obj["fname"] = Convert.ToString(data["fname"]);
                        obj["forgid"] = Convert.ToString(data["forgid"]);
                        obj["fcityid"] = Convert.ToString(data["fcityid"]);

                        saveObjs.Add(obj);
                    }
                    if (saveObjs.Any())
                    {
                        this.Context.SaveBizData("ydj_productauth_other", saveObjs);
                    }
                }
            }
        }

        private void AlterProductAuth_SecondOrg(DynamicObject[] datas)
        {
            if (datas == null || datas.Length == 0)
            {
                return;
            }
            List<string> storeIds = datas.Where(p=>p["forgtype"].ToString()=="5").Select(p => p["forgid"].ToString()).ToList();
            Task task = new Task(() =>
            {
                this.Container.GetService<IProductAuthService>().ClearSecondAgentAuth(this.Context, datas.ToList()
                    ,allAddExProductId , allRemoveExProductId, removeAuthProductId, this.HtmlForm,this.OperationNo,this.OperationName);
                if (storeIds.Count > 0) {
                    this.Container.GetService<IStoreService>().UpdateStoreNewChannel(this.Context, "updateProductAuth", storeIds);
                }
            });
            ThreadWorker.QuequeTask(task, result => { });
        
        }

        /// <summary>
        /// 加载城市+经销商 匹配送达方 的商品授权清单
        /// 05.10：可能 城市 + 经销商 匹配多个不同的送达方，初始化后新下发的送达方没有对应的特殊商品授权清单，这次需要查出这些漏网之鱼
        /// </summary>
        private DynamicObjectCollection LoadProductAuthByDeliver(string pauthid)
        {
            //总部企业标识
            var topOrgId = this.Context.IsTopOrg ? this.Context.Company : this.Context.TopCompanyId;
            var sqlText = $@"select distinct deliver.fid as deliverid,deliver.fsaleorgid,pauth.fid,'4' as forgtype  
                            from t_ydj_productauth pauth
                            inner join T_BAS_DELIVER deliver on pauth.forgid = deliver.fagentid and pauth.fcityid = deliver.fcity
                            where forgtype ='4' and fcityid !='' and deliver.fforbidstatus = 0 and pauth.fid = '{pauthid}' and pauth.fmainorgid ='{topOrgId}'
                            and not exists (select 1 from t_ydj_productauth_other as ot where ot.fdeliverid = deliver.fid and ot.forgid= pauth.forgid and ot.fcityid = pauth.fcityid and ot.fforbidstatus =0)
                            union
                            select distinct deliver.fid as deliverid,deliver.fsaleorgid,pauth.fid,'5' as forgtype
                            from t_ydj_productauth pauth
                            inner join T_BAS_STORE store on pauth.forgid = store.fid 
                            inner join T_BAS_DELIVER deliver on store.fagentid = deliver.fagentid and store.fmycity = deliver.fcity 
                            where forgtype ='5' and fcityid !='' and store.fforbidstatus = 0 and deliver.fforbidstatus = 0 and pauth.fid = '{pauthid}' and pauth.fmainorgid ='{topOrgId}'
                            and not exists (select 1 from t_ydj_productauth_other as ot where ot.fdeliverid = deliver.fid and ot.forgid= pauth.forgid and ot.fcityid = pauth.fcityid and ot.fforbidstatus =0)
";

            var dynObs = this.DBService.ExecuteDynamicObject(this.Context, sqlText);

            return dynObs;
        }
        /// <summary>
        /// 更新商品授权清单时送达方、销售组织跟随当前授予组织联动变
        /// </summary>
        /// <param name="datas"></param>
        private void AlterDeliverAndSaleOrg(DynamicObject[] datas)
        {
            if (datas == null || datas.Length == 0)
            {
                return;
            }
            var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), datas, false);

            foreach (var data in datas)
            {
                var forgid = Convert.ToString(data["forgid"]);

                var org = data["forgid_ref"] as DynamicObject;
                var forgtype = Convert.ToString(org["forgtype"]);

                var fcityid = Convert.ToString(data["fcityid"]);
                var productAuthDeliverEntrys = data?["fdeliverentry"] as DynamicObjectCollection;
                //授予组织类型为经销商时，根据经销商+城市找送达方
                if (!forgid.IsNullOrEmptyOrWhiteSpace() && forgtype.EqualsIgnoreCase("4"))
                {
                    var delivers = this.Context.LoadBizBillHeadDataByACLFilter("bas_deliver", $"fagentid = '{forgid}' and fcity = '{fcityid}'", "fnumber,fid,fsaleorgid").ToList();
                    if (delivers.Count > 0)
                    {
                        productAuthDeliverEntrys.Clear();
                        foreach (var deliver in delivers)
                        {
                            data["fdeliverid"] = Convert.ToString(deliver["fid"]);
                            data["fsaleorgid"] = Convert.ToString(deliver["fsaleorgid"]);

                            var detail = productAuthDeliverEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                            detail["fdeliverid_o"] = Convert.ToString(deliver["fid"]);
                            detail["fsaleorgid_o"] = Convert.ToString(deliver["fsaleorgid"]);
                            productAuthDeliverEntrys.Add(detail);
                        }
                    }
                }
                //授予组织类型为门店时，根据门店经销商+城市找送达方
                if (!forgid.IsNullOrEmptyOrWhiteSpace() && forgtype.EqualsIgnoreCase("5"))
                {
                    var store = this.Context.LoadBizBillHeadDataById("bas_store", forgid, "fnumber,fid,fagentid,fmycity");
                    var fmycity = Convert.ToString(store?["fmycity"]);
                    var fagentid = Convert.ToString(store?["fagentid"]);
                    if (!fmycity.IsNullOrEmptyOrWhiteSpace())
                    {
                        var delivers = this.Context.LoadBizBillHeadDataByACLFilter("bas_deliver", $"fagentid = '{fagentid}' and fcity = '{fmycity}'", "fnumber,fid,fsaleorgid").ToList();
                        if (delivers.Count > 0)
                        {
                            productAuthDeliverEntrys.Clear();
                            foreach (var deliver in delivers)
                            {
                                data["fdeliverid"] = Convert.ToString(deliver["fid"]);
                                data["fsaleorgid"] = Convert.ToString(deliver["fsaleorgid"]);

                                var detail = productAuthDeliverEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                detail["fdeliverid_o"] = Convert.ToString(deliver["fid"]);
                                detail["fsaleorgid_o"] = Convert.ToString(deliver["fsaleorgid"]);
                                productAuthDeliverEntrys.Add(detail);
                            }
                        }
                    }
                }
            }
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            //UpdateStoreIsNewChannel(e.DataEntitys);
        }


        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            this.Container.GetService<IProductAuthService>().ClearPrdAuthCache(this.Context, e.DataEntitys, this.HtmlForm, this.OperationNo);

            //如果是新的商品商品授权清单保存需要判断当前授予组织+城市 有没有对应的专供，没有则需要创建一份
            //0527:当前商品授权保存后执行。因为后续特殊商品授权生成需要依赖已有的商品授权数据，所以特殊商品授权生成节点需要在商品授权保存事务后
            AlterProductAuth_Other(e.DataEntitys);
            AlterProductAuth_SecondOrg(e.DataEntitys);
        }

        /// <summary>
        /// 更新门店的【创新新渠道】
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void UpdateStoreIsNewChannel(IEnumerable<DynamicObject> dataEntitys)
        {
            if (dataEntitys.IsNullOrEmpty())
            {
                return;
            }

            var Logger = this.Context.Container.GetService<ILogService>();
            StringBuilder dataLog = new StringBuilder();
            dataLog.Append("ydj_productauth:authsave-begin:{0}".Fmt(DateTime.Now.ToString("yyyyMMddHHmmss")));

            foreach (var item in dataEntitys)
            {
                var fbillno = Convert.ToString(item["fnumber"]);
                dataLog.Append("单号{0}\r\n".Fmt(fbillno));
                var fentry = item["fproductauthbs"] as DynamicObjectCollection;
                foreach (var detail in fentry)
                {
                    dataLog.Append("fbrandid[{0}],fserices[{1}],fdatasource[{2}]\r\n".Fmt(Convert.ToString(detail["fbrandid"]),
                        Convert.ToString(detail["fserieid"]),
                        Convert.ToString(item["fdatasourceterminal"])));
                }
            }
            //记录日志
            Logger.WriteLogToFile(dataLog.ToString(), "ProAuth日志Log");

            //#75244 【250491】 【慕思现场4.10-4.14】新渠道系列拆分Z2 慕思经典-甄选 ，Z5 慕思经典-优选
            var z2 = this.Context.LoadBizDataByNo("ydj_series", "fnumber", new[] { "Z2", "Z5" }).FirstOrDefault();

            var storePrdAuths = dataEntitys.Where(s => Convert.ToString(s["forgtype"]).EqualsIgnoreCase("5"));
            if (storePrdAuths.IsNullOrEmpty())
            {
                return;
            }

            var storeIds = storePrdAuths.Select(s => Convert.ToString(s["forgid"])).ToList();
            var stores = this.Context.LoadBizDataById("bas_store", storeIds);

            var beUpdate = new List<DynamicObject>();

            foreach (var item in dataEntitys)
            {
                var storeId = Convert.ToString(item["forgid"]);
                var store = stores.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(storeId));

                if (store == null)
                {
                    continue;
                }

                var hbs = item["fproductauthbs"] as DynamicObjectCollection;
                bool flag = false;
                foreach (var hbitem in hbs)
                {
                    if (!Convert.ToString(hbitem["fserieid"]).IsNullOrEmptyOrWhiteSpace() && Convert.ToString(hbitem["fserieid"]).EqualsIgnoreCase(Convert.ToString(z2?["id"])))
                    {
                        flag = true;
                        break;
                    }
                }

                var fisnewchannel = Convert.ToBoolean(store["fisnewchannel"]);
                if (fisnewchannel != flag)
                {
                    //存在Z2，勾选门店的创新新渠道
                    //不存在Z2，取消勾选门店的创新新渠道
                    store["fisnewchannel"] = flag;
                    beUpdate.Add(store);
                }
            }

            if (beUpdate.Any())
            {
                var dm = this.GetDataManager();
                var dt = this.MetaModelService.LoadFormModel(this.Context, "bas_store").GetDynamicObjectType(this.Context);
                dm.InitDbContext(this.Context, dt);
                dm.Save(beUpdate);
            }
        }

    }

}
