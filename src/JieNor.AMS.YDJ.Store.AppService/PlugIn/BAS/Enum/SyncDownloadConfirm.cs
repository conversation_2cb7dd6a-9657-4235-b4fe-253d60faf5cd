using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Integration;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Enum
{
    ///// <summary>
    ///// 辅助资料下载操作
    ///// </summary>
    //[InjectService]
    //[FormId("bd_enum")]
    //[OperationNo("SyncDownloadConfirm")]
    //public class SyncDownloadConfirm : AbstractOperationServicePlugIn
    //{
    //    public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
    //    {
    //        base.OnCustomServiceEvent(e);
    //        switch (e.EventName.ToLower())
    //        {
    //            case "savedataentities":
    //                save(e);
    //                break;
    //            default:
    //                return;
    //        }
    //    }

    //    private void save(OnCustomServiceEventArgs e)
    //    {
    //        e.Result = "OK";
    //        var eventData = e.EventData as Dictionary<string, object>;
    //        var userContext = eventData["userContext"] as UserContext;
    //        var dataEntities = eventData["dataEntities"] as List<DynamicObject>;

    //        if (userContext == null || dataEntities == null || dataEntities.Count <= 0)
    //        {
    //            return;
    //        }

    //        var metaModelService = userContext.Container.GetService<IMetaModelService>();
    //        var htmlForm = metaModelService.LoadFormModel(userContext, "bd_enumdata");
    //        var dm = userContext.Container.GetService<IDataManager>();
    //        dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
    //        var ids = getIds(userContext, dataEntities, htmlForm);

    //        if (ids == null || ids.Length <= 0)
    //        {
    //            return;
    //        }

    //        var dbEntities = dm.Select(ids).OfType<DynamicObject>().ToList();
    //        var entryEntityModel = htmlForm.GetEntryEntity("fentity");
    //        var saveEntities = new List<DynamicObject>();

    //        foreach (var dataEntity in dataEntities)
    //        {
    //            var saveEntity = dbEntities.FirstOrDefault(x => 
    //            {
    //                if(Convert.ToString(x["fname"]) != Convert.ToString(dataEntity["fcategory"]))
    //                {
    //                    return false;
    //                }
    //                var fmainorgid = Convert.ToString(x["fmainorgid"]);
    //                return fmainorgid == "" || fmainorgid == " " || fmainorgid == "0" || fmainorgid == Convert.ToString(dataEntity["fmainorgid"]);
    //            } );
    //            if (saveEntity == null)
    //            {
    //                saveEntity = new DynamicObject(htmlForm.GetDynamicObjectType(userContext));
    //                saveEntity["fname"] = dataEntity["fcategory"];
    //                saveEntity["fmodule"] = dataEntity["fmodule"];
    //                saveEntity["fmoduleorder"] = dataEntity["fmoduleorder"];
    //                saveEntity["fvisible"] = dataEntity["fvisible"];
    //            }
    //            saveEntities.Add(saveEntity);
    //            var entryEntities = saveEntity["fentity"] as DynamicObjectCollection;
    //            if (entryEntities != null)
    //            {
    //                var entryEntity = entryEntities.FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(dataEntity["id"]));
    //                if (entryEntity == null)
    //                {
    //                    entryEntity = new DynamicObject(entryEntityModel.DynamicObjectType);
    //                    entryEntities.Add(entryEntity);
    //                    entryEntity["id"] = dataEntity["id"];
    //                    entryEntity["fenumitem"] = dataEntity["fenumitem"];
    //                    entryEntity["fgroup"] = dataEntity["fgroup"];
    //                    entryEntity["forder"] = dataEntity["forder"];
    //                    entryEntity["fdisabled"] = dataEntity["fforbidstatus"];
    //                    entryEntity["fispreset"] = dataEntity["fissys"];
    //                    entryEntity["fenmainorgid"] = dataEntity["fmainorgid"];
    //                }

    //                entryEntity["ffromchaindataid"] = dataEntity["ffromchaindataid"];
    //                entryEntity["fpublishcid_txt"] = dataEntity["fpublishcid_txt"];
    //                entryEntity["fpublishcid"] = dataEntity["fpublishcid"];
    //                entryEntity["fpublishcid_pid"] = dataEntity["fpublishcid_pid"];
    //                entryEntity["fdataorigin"] = dataEntity["fdataorigin"];
    //                entryEntity["fdownloaddate"] = dataEntity["fdownloaddate"];
    //            }
    //        }

    //        if (saveEntities == null || saveEntities.Count <= 0)
    //        {
    //            return;
    //        }

    //        var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
    //        prepareSaveDataService.PrepareDataEntity(userContext, htmlForm, saveEntities.ToArray(), OperateOption.Create());

    //        dm.Save(saveEntities);

    //        var cacheClient = userContext.Container.GetService<IRedisCache>();

    //        foreach (var name in saveEntities.Select(x => Convert.ToString(x["fname"])))
    //        {
    //            var cacheKey = $"querycombo:enumdata.{name}";
    //            cacheClient.RemoveAllByKeyPattern(userContext, cacheKey, Enu_SearchType.Contains);
    //        }
    //    }

    //    private string[] getIds(UserContext userContext,List<DynamicObject> dataEntities,HtmlForm htmlForm)
    //    {
    //        var names = dataEntities.Select(x => Convert.ToString(x["fcategory"])).Distinct().ToList();
    //        var sqlParams = new List<SqlParam>();
    //        var sql = new StringBuilder($"select t1.fid from {htmlForm.BillHeadTableName} t1 ");
    //        var dbService = userContext.Container.GetService<IDBService>();

    //        if (names.Count == 1)
    //        {
    //            sql.Append(" where fname=@fname");
    //            sqlParams.Add(new SqlParam("@fname", System.Data.DbType.String, names[0]));
    //        }
    //        else if (names.Count <= 50)
    //        {
    //            sql.Append(" where fname in (");
    //            sql.Append(string.Join(",", names.Select((x, i) => string.Format("@fname{0}", i))));
    //            sql.Append(")");
    //            sqlParams.AddRange(names.Select((x, i) => new SqlParam(string.Format("@fname{0}", i), System.Data.DbType.String, x)));
    //        }

    //        var results = new List<string>();
    //        if (names.Count <= 50)
    //        {
    //            using (var dataReader = dbService.ExecuteReader(userContext, sql.ToString(), sqlParams))
    //            {
    //                while (dataReader.Read())
    //                {
    //                    results.Add(Convert.ToString(dataReader.GetValue(0)));
    //                }
    //            }
    //        }
    //        else
    //        {
    //            using (var trans = userContext.CreateTransaction())
    //            {
    //                var tableName = dbService.CreateTempTableWithDataList(userContext, names);
    //                sql.Append($" inner join {tableName} t2 on t1.fname=t2.fid ");
    //                using (var dataReader = dbService.ExecuteReader(userContext, sql.ToString()))
    //                {
    //                    while (dataReader.Read())
    //                    {
    //                        results.Add(Convert.ToString(dataReader.GetValue(0)));
    //                    }
    //                }
    //            }
    //        }

    //        return results.Distinct().ToArray();
    //    }
    //}

    /// <summary>
    /// 辅助资料下载操作
    /// </summary>
    [InjectService]
    [FormId("bd_enum")]
    [OperationNo("SyncDownloadConfirm")]
    public class SyncDownloadConfirm : AbstractSyncDownloadConfirmPlugIn
    {
        protected override bool SaveDataEntities(UserContext userContext, 
                                                 HtmlForm htmlForm,
                                                 List<DynamicObject> dataEntities, 
                                                 List<DynamicObject> rollbackEntities, 
                                                 List<Dictionary<string, object>> billDatas, 
                                                 List<Dictionary<string, string>> cooOperationModes, 
                                                 List<ChainDataIdToLocalDataIdMapsSet> chainDataIdToLocalDataIdMaps)
        {

            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var enumForm = metaModelService.LoadFormModel(userContext, "bd_enumdata");
            var dm = userContext.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, enumForm.GetDynamicObjectType(userContext));
            var ids = getIds(userContext, dataEntities, enumForm);

            if (ids == null || ids.Length <= 0)
            {
                return true;
            }

            var dbEntities = dm.Select(ids).OfType<DynamicObject>().ToList();
            var entryEntityModel = enumForm.GetEntryEntity("fentity");
            var saveEntities = new List<DynamicObject>();

            foreach (var dataEntity in dataEntities)
            {
                var saveEntity = dbEntities.FirstOrDefault(x =>
                {
                    if (Convert.ToString(x["fname"]) != Convert.ToString(dataEntity["fcategory"]))
                    {
                        return false;
                    }
                    var fmainorgid = Convert.ToString(x["fmainorgid"]);
                    return fmainorgid == "" || fmainorgid == " " || fmainorgid == "0" || fmainorgid == Convert.ToString(dataEntity["fmainorgid"]);
                });
                if (saveEntity == null)
                {
                    saveEntity = new DynamicObject(enumForm.GetDynamicObjectType(userContext));
                    saveEntity["fname"] = dataEntity["fcategory"];
                    saveEntity["fmodule"] = dataEntity["fmodule"];
                    saveEntity["fmoduleorder"] = dataEntity["fmoduleorder"];
                    saveEntity["fvisible"] = dataEntity["fvisible"];
                }
                saveEntities.Add(saveEntity);
                var entryEntities = saveEntity["fentity"] as DynamicObjectCollection;
                if (entryEntities != null)
                {
                    var entryEntity = entryEntities.FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(dataEntity["id"]));
                    if (entryEntity == null)
                    {
                        entryEntity = new DynamicObject(entryEntityModel.DynamicObjectType);
                        entryEntities.Add(entryEntity);
                        entryEntity["id"] = dataEntity["id"];
                        entryEntity["fenumitem"] = dataEntity["fenumitem"];
                        entryEntity["fgroup"] = dataEntity["fgroup"];
                        entryEntity["forder"] = dataEntity["forder"];
                        entryEntity["fdisabled"] = dataEntity["fforbidstatus"];
                        entryEntity["fispreset"] = dataEntity["fissys"];
                        entryEntity["fenmainorgid"] = dataEntity["fmainorgid"];
                    }

                    entryEntity["ffromchaindataid"] = dataEntity["ffromchaindataid"];
                    entryEntity["fpublishcid_txt"] = dataEntity["fpublishcid_txt"];
                    entryEntity["fpublishcid"] = dataEntity["fpublishcid"];
                    entryEntity["fpublishcid_pid"] = dataEntity["fpublishcid_pid"];
                    entryEntity["fdataorigin"] = dataEntity["fdataorigin"];
                    entryEntity["fdownloaddate"] = dataEntity["fdownloaddate"];
                }
            }

            if (saveEntities == null || saveEntities.Count <= 0)
            {
                return true;
            }

            var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(userContext, enumForm, saveEntities.ToArray(), OperateOption.Create());

            for (int i = 0, nSize = 200; i < saveEntities.Count; i += nSize)
            {
                var splitDatas = saveEntities.GetRange(i, Math.Min(nSize, saveEntities.Count - i));
                dm.Save(splitDatas, null, OperateOption.InstanceBulkCopyAndNoCache);
            }
             
            var comboSvc = this.Container.GetService<IComboDataService>();
            foreach (var name in saveEntities.Select(x => Convert.ToString(x["fname"])))
            {
                comboSvc.ClearCache(this.Context, name);
            }

            return true;
        }

        private string[] getIds(UserContext userContext, List<DynamicObject> dataEntities, HtmlForm htmlForm)
        {
            var names = dataEntities.Select(x => Convert.ToString(x["fcategory"])).Distinct().ToList();
            var sqlParams = new List<SqlParam>();
            var sql = new StringBuilder($"select t1.fid from {htmlForm.BillHeadTableName} t1 ");
            var dbService = userContext.Container.GetService<IDBService>();

            if (names.Count == 1)
            {
                sql.Append(" where fname=@fname");
                sqlParams.Add(new SqlParam("@fname", System.Data.DbType.String, names[0]));
            }
            else if (names.Count <= 50)
            {
                sql.Append(" where fname in (");
                sql.Append(string.Join(",", names.Select((x, i) => string.Format("@fname{0}", i))));
                sql.Append(")");
                sqlParams.AddRange(names.Select((x, i) => new SqlParam(string.Format("@fname{0}", i), System.Data.DbType.String, x)));
            }

            var results = new List<string>();
            if (names.Count <= 50)
            {
                using (var dataReader = dbService.ExecuteReader(userContext, sql.ToString(), sqlParams))
                {
                    while (dataReader.Read())
                    {
                        results.Add(Convert.ToString(dataReader.GetValue(0)));
                    }
                }
            }
            else
            {
                using (var trans = userContext.CreateTransaction())
                {
                    var tableName = dbService.CreateTempTableWithDataList(userContext, names,false);
                    sql.Append($" inner join {tableName} t2 on t1.fname=t2.fid ");
                    using (var dataReader = dbService.ExecuteReader(userContext, sql.ToString()))
                    {
                        while (dataReader.Read())
                        {
                            results.Add(Convert.ToString(dataReader.GetValue(0)));
                        }
                    }
                    trans.Complete();

                    dbService.DeleteTempTableByName(userContext, tableName, true);
                }
            }

            return results.Distinct().ToArray();
        }
    }
}
