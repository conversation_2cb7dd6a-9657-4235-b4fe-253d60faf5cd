using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.MainAuxProp
{
    /// <summary>
    /// 主辅属性约束：删除检查
    /// </summary>
    [InjectService]
    [FormId("bas_mainauxprop")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //校验辅属性信息的属性值是否已被业务表单引用
            var headMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return this.CheckBillHead(newData, out headMessage);
            }).WithMessage("{0}", (billObj, propObj) => headMessage));
        }

        /// <summary>
        /// 校验辅属性信息的属性值是否已被业务表单引用
        /// </summary>
        private bool CheckBillHead(DynamicObject newData, out string message)
        {
            message = "";

            var auxPropId = "";
            var refEnumValues = new List<string> { };

            var entrys = newData["fentity"] as DynamicObjectCollection;
            foreach (var entry in entrys)
            {
                var _auxPropId = Convert.ToString(entry?["fauxpropid"]);
                var _refEnumValue = Convert.ToString((entry?["frefenumvalue"]));
                if (_auxPropId.IsNullOrEmptyOrWhiteSpace() || _refEnumValue.IsNullOrEmptyOrWhiteSpace()) continue;

                auxPropId = _auxPropId; //目前的辅助属性类别固定为颜色，任取一个即可
                refEnumValues.Add(_refEnumValue);
            }

            var result = MainAuxPropHelper.CheckAuxPropValue(this.Context, auxPropId, refEnumValues);
            if (!result.IsSuccess)
            {
                message = string.Join("，", result.ComplexMessage.ErrorMessages);
            }

            return result.IsSuccess;
        }
    }
}