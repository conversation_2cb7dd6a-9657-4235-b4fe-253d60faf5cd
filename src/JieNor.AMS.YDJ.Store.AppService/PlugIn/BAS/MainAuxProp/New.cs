using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.MainAuxProp
{
    /// <summary>
    /// 主辅属性约束：新增
    /// </summary>
    [InjectService]
    [FormId("bas_mainauxprop")]
    [OperationNo("new")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "aftercreateuidata":
                    ProcAfterCreateUiData(this.Context, e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理UI数据打包逻辑
        /// </summary>
        /// <param name="e"></param>
        public static void ProcAfterCreateUiData(UserContext userCtx, OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as JObject;
            if (eventData == null) return;

            var material = userCtx.LoadBizDataByFilter("bd_auxproperty",
                " fmainprop='1' and fvaluesource='val_type_01' and frefenumformid='5b0f5e6d1db7a3' and fname='材质' ").FirstOrDefault();

            var color = userCtx.LoadBizDataByFilter("bd_auxproperty",
                " fmainprop='0' and fvaluesource='val_type_01' and frefenumformid='5b0fd271f4956f' and fname='颜色' ").FirstOrDefault();

            eventData["material"] = JToken.FromObject(new
            {
                id = material?["id"],
                fname = material?["fname"],
                fnumber = material?["fnumber"]
            });

            eventData["color"] = JToken.FromObject(new
            {
                id = color?["id"],
                fname = color?["fname"],
                fnumber = color?["fnumber"]
            });
        }
    }
}