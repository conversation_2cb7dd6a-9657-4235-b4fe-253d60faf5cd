using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MobileShop
{
    /// <summary>
    /// 获取全部员工
    /// </summary>
    [InjectService]
    [FormId("ydj_phonebook")]
    [OperationNo("getallstaff")]
    public class GetAllStaff : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            string sql = @"select s.fid as id,s.fname as name,s.fphone as phone,s.femail as email,s.fname_py as pinYinOfFirstName,
		                            s.fname_py2 as pinYinOfFullName,e.fenumitem as position,d.fid as deptId,d.fname as deptName,
		                            u.fimage as photo           
		                            from T_BD_STAFF s
		                            left join T_BD_DEPARTMENT d on s.fdeptid=d.fid and d.fmainorgid=s.fmainorgid
		                            left join T_BD_ENUMDATAENTRY e on e.fid='fa104d958c0a451c879e326d938pf3e7' and e.fentryid=s.fposition
		                            left join T_SEC_USER u on u.fphone=s.fphone and u.fmainorgid=s.fmainorgid and len(u.fphone)>0
		                            where s.fmainorgid=@fmainorgid";

            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("fmainorgid",System.Data.DbType.String,this.Context.Company)
            };

            List<Dictionary<string, object>> srvData = new List<Dictionary<string, object>>();

            using (var dataReader = this.DBService.ExecuteReader(this.Context, sql, sqlParams))
            {
                while (dataReader.Read())
                {
                    var depts = new List<Dictionary<string, object>>();

                    srvData.Add(new Dictionary<string, object>
                    {
                        { "id",dataReader.GetValueToString("id") },
                        { "name",dataReader.GetValueToString("name") },
                        { "photo",dataReader.GetValueToString("photo").GetSignedFileUrl(false) },
                        { "phone",dataReader.GetValueToString("phone") },
                        { "email",dataReader.GetValueToString("email") },
                        { "pinYinOfFirstName",dataReader.GetValueToString("pinYinOfFirstName") },
                        { "pinYinOfFullName",dataReader.GetValueToString("pinYinOfFullName") },
                        { "departments",depts}
                    });

                    var deptId = dataReader["deptId"];

                    if (Convert.IsDBNull(deptId) == false)
                    {
                        depts.Add(new Dictionary<string, object>
                        {
                            { "id",deptId},
                            { "name",dataReader.GetValueToString("deptName")},
                            { "position",dataReader.GetValueToString("position")}
                        });
                    }
                }
            }

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "获取全部员工成功!";
            this.Result.SrvData = srvData;
        }
    }
}
