using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MobileShop
{
    /// <summary>
    /// 获取全部部门
    /// </summary>
    [InjectService]
    [FormId("ydj_phonebook")]
    [OperationNo("getalldepartments")]
    public class GetAllDepartments: AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            string sql = @"select d1.fid as id,d1.fname as name,d2.fid as parentId,d2.fname as parentName,s.fid as leaderId,s.fname as leaderName
                           from T_BD_DEPARTMENT d1
                           left join T_BD_DEPARTMENT d2 on d1.fparentid=d2.fid
                           left join T_BD_STAFF s on d1.fleaderid=s.fid
                           where d1.fmainorgid=@fmainorgid";

            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("fmainorgid",System.Data.DbType.String,this.Context.Company)
            };

            List<Dictionary<string, object>> srvData = new List<Dictionary<string, object>>();

            using (var dataReader = this.DBService.ExecuteReader(this.Context, sql, sqlParams))
            {
                while (dataReader.Read())
                {
                    var leaders = new List<Dictionary<string, object>>();

                    var parentId = dataReader["parentId"];
                    var parentName = dataReader["parentName"];

                    srvData.Add(new Dictionary<string, object>
                    {
                        { "id",dataReader["id"] },
                        { "name",dataReader["name"] },
                        { "parentId", Convert.IsDBNull(parentId)?string.Empty:Convert.ToString(parentId)},
                        { "parentName",Convert.IsDBNull(parentName)?string.Empty:Convert.ToString(parentName) },
                        { "leaders",leaders}
                    });

                    var leaderId = dataReader["leaderId"];

                    if (Convert.IsDBNull(leaderId) == false)
                    {
                        leaders.Add(new Dictionary<string, object>
                        {
                            { "id",leaderId},
                            { "name",dataReader["leaderName"]}
                        });
                    }
                }
            }

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "获取全部部门成功!";
            this.Result.SrvData = srvData;
        }
    }
}
