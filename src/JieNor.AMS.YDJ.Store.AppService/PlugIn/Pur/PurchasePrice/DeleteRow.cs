using JieNor.AMS.YDJ.Store.AppService.Plugin.Price;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchasePrice
{
    /// <summary>
    /// 采购价目：删除行
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseprice|ydj_selfpurchaseprice")]
    [OperationNo("DeleteRow")]
    public class DeleteRow : BaseDelete
    {
        protected override string PromptFieldId
        {
            get
            {
                return this.HtmlForm.NumberFldKey;
            }
        }

        protected override string ProductIdKey
        {
            get
            {
                return "fproductid_e";
            }
        }
    }
}
