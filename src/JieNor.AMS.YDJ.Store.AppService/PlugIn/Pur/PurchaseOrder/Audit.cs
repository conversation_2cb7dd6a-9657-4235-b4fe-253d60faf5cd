using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Helpers;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using System.Data;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：审核
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {

        /// <summary>
        /// 初始化服务插件上下文时触发的事件
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="serviceList"></param>
        public new void InitializeOperationContext(OperationContext operCtx, params object[] serviceList)
        {
            // 启用幂等性检查
            var serCtrlOpt = serviceList.FirstOrDefault(o => o is ServiceControlOption) as ServiceControlOption;
            serCtrlOpt.SupportIdemotency = true;

            base.InitializeOperationContext(operCtx, serviceList);
        }

        /// <summary>
        /// 当前二级经销商是否不管理库存
        /// </summary>
        private bool IsNotMgrInv { get; set; }

        /// <summary>
        /// 当前采购订单未审核的采购订单变更单ids(每张采购订单取最新一张未审核的采购订单变更单)
        /// </summary>
        private Dictionary<string, string> unAuditPurchaseorderChgIds { get; set; } = new Dictionary<string, string>();


        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            IPurchaseOrderService purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
            var billTypeService = this.Context.Container.GetService<IBillTypeService>();

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return new PurchaseOrderCommon(this.Context).CheckDeptByBillType(newData);
            }).WithMessage("采购部门不能为空！"));

            //审核变更校验
            var changeMsg = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string billtype = Convert.ToString(newData["fbilltypeid"]);
                var billtypeName = billTypeService.GetBillTypeInfor(this.Context, billtype).fname;
                if (billtypeName == "总部手工单")
                {
                    return true;
                }

                var changeStatus = Convert.ToString(newData["fchangestatus"]);
                var status = Convert.ToString(newData["fstatus"]);
                if (status == "D" && changeStatus == "1")
                {
                    changeMsg = $"{this.HtmlForm.Caption}【{newData["fbillno"]}】变更状态不是“变更已提交”，请先提交变更后再审核！";
                    return false;
                }

                return true;
            }).WithMessage("{0}", (billObj, propObj) => changeMsg));

            string errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string billtype = Convert.ToString(newData["fbilltypeid"]);
                var billtypeName = billTypeService.GetBillTypeInfor(this.Context, billtype).fname;
                //如果是二级经销商，这边放开非标限制
                if (billtypeName == "总部手工单" /*||this.Context.IsSecondOrg*/)
                {
                    return true;
                }

                return purchaseOrderService.CheckUnstdStatus(this.Context, newData, out errorMsg);

            }).WithMessage("{0}", (billObj, propObj) => errorMsg));
            e.Rules.Add(new AuditValidation());
        }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "parseconvertinfo":
                    parseConvertInfo(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 分析当前字段与对应下游字段是否参与允许变更的条件
        /// </summary>
        /// <param name="e"></param>
        private void parseConvertInfo(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null)
            {
                return;
            }

            var ruleId = Convert.ToString(eventData.GetValue("ruleId", string.Empty));
            var targetFieldId = Convert.ToString(eventData.GetValue("targetFieldId", string.Empty));

            if (ruleId.EqualsIgnoreCase("ydj_purchaseorder2pur_receiptnotice") &&
                targetFieldId.EqualsIgnoreCase("fplanqty"))
            {
                //不需要参与
                e.Cancel = true;
                return;
            }
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            //获取未审核的采购订单变更单信息
            foreach (var item in e.DataEntitys)
            {
                var fbillno = Convert.ToString(item["fbillno"]).Trim();

                if (!fbillno.IsNullOrEmptyOrWhiteSpace())
                {
                    var sqlParams = new List<SqlParam>
                    {
                        new SqlParam("@fsourcenumber", System.Data.DbType.String, fbillno)
                    };

                    var purchaseorderChgObj = this.Context.LoadBizBillHeadDataByACLFilter("ydj_purchaseorder_chg",
                            " fsourcenumber=@fsourcenumber and fstatus!='E' ", "fsourcenumber,fid,fcreatedate",
                            sqlParams)
                        .OrderByDescending(o => o["fcreatedate"]).FirstOrDefault();

                    if (!purchaseorderChgObj.IsNullOrEmptyOrWhiteSpace())
                    {
                        var fsourcenumber = Convert.ToString(purchaseorderChgObj["fsourcenumber"]).Trim();
                        var fid = Convert.ToString(purchaseorderChgObj["fid"]).Trim();

                        if (!fsourcenumber.IsNullOrEmptyOrWhiteSpace() && !fid.IsNullOrEmptyOrWhiteSpace())
                            unAuditPurchaseorderChgIds.Add(fsourcenumber, fid);
                    }

                }

            }

        }

        /// <summary>
        /// 采购订单审核后需要反写上游单据状态
        /// 修改人：zpf
        /// 日  期：2022-02-08
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;
            if (this.Context.IsSecondOrg)
            {
                // 当前二级经销商信息
                var currentAgent =
                    this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fisnotmgrinv");
                this.IsNotMgrInv = Convert.ToString(currentAgent?["fisnotmgrinv"]) == "1";
            }
            //1、先找到平台生成的采购订单变更单

            /*9.如果发货方经销商将转单的《销售合同》下推生成《采购订单》时, 《采购订单》审核后要更新累加采购数量
                1)更新 接单方 与 发货方《转单申请单》商品明细对应商品的【基本单位已采购数量】
                2)更新 接单方《销售合同》商品明细的【已采购数量】(要考虑单位换算) 与【基本单位已采购数量】
                3)更新 发货方《销售合同》商品明细的【已采购数量】与【基本单位已采购数量】 (已有标准功能)
                4)同样的如果后来该《采购订单》反审核, 要扣减上述所更新的数量
            */

            AuditOrUnAuditHelper.OrderReviewAuditOrUnAudit(this.HtmlForm.Id, e.DataEntitys, this.Context, true);

            //处理【初始辅助属性】反写
            var orderService = this.Container.GetService<IOrderService>();
            orderService.writeAttrinfoFirst(this.Context, this.HtmlForm, e.DataEntitys,1);

            foreach (var dataEntity in e.DataEntitys)
            {
                //PushPurOrderChg(dataEntity);
                Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(dataEntity, string.Empty, !IsNotMgrInv);
            }

            this.Context.SaveBizData(this.HtmlForm.Id, e.DataEntitys);

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);

            var purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
            purchaseOrderService.UpdatePostockin(this.Context, e.DataEntitys, unAuditPurchaseorderChgIds);
            //UpdatePostockin(purchaseOrder);
            //更新采购入库单
            //UpdatePostockin(e.DataEntitys, unAuditPurchaseorderChgIds);

            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 处理将生成的采购订单变更单同步到中台
        /// </summary>
        /// <param name="dataEntity"></param>
        private void PushPurOrderChg(DynamicObject dataEntity)
        {
            //变更状态 '0':'正常','1':'变更中','2':'变更完成','3':'变更已提交'
            //单据状态：已提交（status == "D"） 变更状态： 变更已提交（fchangestatus =="3"）
            var status = Convert.ToString(dataEntity["fstatus"]);
            var fchangestatus = Convert.ToString(dataEntity["fchangestatus"]);
            if (status == "E" && fchangestatus == "2")
            {
                //如果是采购订单提交变更后 审核，需要通过 提交总部的校验 然后生成采购订单变更单并同步到中台
                var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_purchaseorder",
                    new DynamicObject[] { dataEntity }, "SubmitHQ_Chg", null);
                result.ThrowIfHasError(true, $"{this.HtmlForm.Caption}{this.OperationName}失败！");
            }
        }

        /// <summary>
        /// 在经销商做了《采购订单变更单》如果修改了《采购订单》的【采购单价】、【成交单价】、【成交金额】、【金额】、【折扣额】、【折扣率】
        /// 审核《采购订单变更单》后；在原功能逻辑不变下，增加逻辑：通过《采购订单》的【单据编号】+【商品编码】+【辅助属性】+【定制说明】+【行号】
        /// 去匹配关联的下游《采购入库单》，并更新《采购入库单》的【成交单价】、【采购单价】、【成交金额】、【金额】、【修改日期】、【修改人】；
        /// 并且操作记录要记录此次修改的记录，操作人为《采购订单变更单》的创建人。
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void UpdatePostockin(DynamicObject[] dataEntitys, Dictionary<string, string> unAuditPurChgIds)
        {
            foreach (var dataEntity in dataEntitys)
            {
                var fbillno = Convert.ToString(dataEntity["fbillno"]).Trim();
                var purFentitys = dataEntity["fentity"] as DynamicObjectCollection;
                if (unAuditPurChgIds.ContainsKey(fbillno))
                {
                    var purchaseorderchgObj =
                        this.Context.LoadBizDataById("ydj_purchaseorder_chg", unAuditPurChgIds[fbillno]);
                    if (!purchaseorderchgObj.IsNullOrEmptyOrWhiteSpace())
                    {
                        var userId = Convert.ToString(purchaseorderchgObj["fcreatorid"]).Trim();

                        //获取下游采购入库单
                        var sqlParams = new List<SqlParam>
                        {
                            new SqlParam("@fsourcenumber", System.Data.DbType.String, fbillno)
                        };
                        var postockinObjs = this.Context.LoadBizDataByFilter("stk_postockin",
                            " fsourcenumber=@fsourcenumber ", false, sqlParams);


                        List<DynamicObject> changePostockinObjs = new List<DynamicObject>();

                        foreach (var item in postockinObjs)
                        {
                            var needChange = false;

                            var postockinEntitys = item["fentity"] as DynamicObjectCollection;

                            foreach (var postockinEntity in postockinEntitys)
                            {
                                var existEntry = purFentitys.Where(o =>
                                        Convert.ToString(o["id"]).Trim()
                                            .Equals(Convert.ToString(postockinEntity["fsourceentryid"]).Trim()))
                                    .FirstOrDefault();

                                if (!existEntry.IsNullOrEmptyOrWhiteSpace())
                                {
                                    //《采购入库单》【金额】=【采购单价】*【实收数量】；【成交金额】=【成交单价】*【实收数量】

                                    //《采购入库单》更新前的相关金额
                                    var fprice = Convert.ToDecimal(postockinEntity["fprice"]);
                                    var fpoprice = Convert.ToDecimal(postockinEntity["fpoprice"]);
                                    var famount = Convert.ToDecimal(postockinEntity["famount"]);
                                    var fprfpoamountice = Convert.ToDecimal(postockinEntity["fpoamount"]);

                                    //《采购入库单》即将更新后的相关金额
                                    var fbizqty = Convert.ToDecimal(postockinEntity["fbizqty"]);
                                    var fprice_new = Convert.ToDecimal(existEntry["fdealprice"]);
                                    var fpoprice_new = Convert.ToDecimal(existEntry["fprice"]);
                                    var famount_new = fprice_new * fbizqty;
                                    var fprfpoamountice_new = fpoprice_new * fbizqty;

                                    if (fprice != fprice_new || fpoprice != fpoprice_new || famount != famount_new ||
                                        fprfpoamountice != fprfpoamountice_new)
                                    {

                                        postockinEntity["fprice"] = fprice_new; //成交单价
                                        postockinEntity["fpoprice"] = fpoprice_new; //采购单价
                                        postockinEntity["famount"] = famount_new; //成交金额
                                        postockinEntity["fpoamount"] = fprfpoamountice_new; //金额

                                        needChange = true;
                                    }

                                }
                            }

                            if (needChange)
                            {

                                item["fupdatepricedate"] = DateTime.Now; //更新价格日期
                                item["fisupdateprice"] = "1"; //是否更新价格

                                changePostockinObjs.Add(item);
                            }

                        }

                        if (changePostockinObjs.Count > 0)
                        {
                            var ctx = this.Context.CreaeteaAgentDBContextWithUser(this.Context.Company, userId);
                            var res = this.Gateway.InvokeBillOperation(ctx, "stk_postockin", changePostockinObjs,
                                "save", new Dictionary<string, object>() { });
                            res.ThrowIfHasError();
                        }


                    }
                }
            }
        }

    }
}