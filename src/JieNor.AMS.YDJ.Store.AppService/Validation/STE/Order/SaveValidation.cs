using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.DataTransferObject;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Text.RegularExpressions;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework.Enums;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.DataTransferObject.Enums;

namespace JieNor.AMS.YDJ.Store.AppService.Validation.STE.Order
{
    /// <summary>
    /// 销售合同：保存校验器
    /// </summary>
    public class SaveValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }
        public SaveValidation(bool autosubmit, bool autoaudit)
        {
            this._autosubmit = autosubmit;
            this._autoaudit = autoaudit;
        }
        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 保存提交
        /// </summary>
        private bool _autosubmit { get; set; }

        /// <summary>
        /// 保存审核
        /// </summary>
        private bool _autoaudit { get; set; }
        /// <summary>
        /// 是否是从采购订单【提交一级经销】触发的保存
        /// </summary>
        private bool FromPurchaseOrderSubmitAgent { get; set; }

        /// <summary>
        /// 业务表单模型
        /// </summary>
        private HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            this.FromPurchaseOrderSubmitAgent = option.GetVariableValue("FromPurchaseOrderSubmitAgent", false);
            this.HtmlForm = formInfo;

            // 所有的商品ID
            var productIds = dataEntities?.SelectMany(o =>
            {
                var entrys = o["fentry"] as DynamicObjectCollection;
                var _productIds = entrys
                .Select(entry => Convert.ToString(entry["fproductid"]))
                .Where(productId => !productId.IsNullOrEmptyOrWhiteSpace());
                return _productIds;
            })
            ?.Distinct()
            ?.ToList();

            // 批量加载商品信息
            DynamicObjectCollection productObjs = null;
            DynamicObjectCollection seriesObjs = null;
            if (productIds != null && productIds.Any())
            {
                productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fnumber,fname,fsuiteflag,fispresetprop,funstdtype,fmainorgid,fseriesid");
                if (productObjs != null)
                {
                    var seriesIdList = productObjs.Where(x => !Convert.ToString(x["fseriesid"]).IsNullOrEmptyOrWhiteSpace())
                                                .Select(y => Convert.ToString(y["fseriesid"]))
                                                ?.Distinct()
                                                ?.ToList();
                    if (seriesIdList != null && seriesIdList.Any())
                    {
                        seriesObjs = this.Context.LoadBizBillHeadDataById("ydj_series", seriesIdList, "fnumber,fname,fmainorgid,fisnewchannel");
                    }


                }
            }

            // 系统参数服务
            var profileService = userCtx.Container.GetService<ISystemProfile>();

            // 启用手机号位数控制
            var enablePhoneLengthCtrl = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablephonelengthctrl", false);

            // 启用手机号按国内电信号段规范控制
            var phoneStandardControl = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fphonestandardcontrol", false);

            // 无统一零售价商品禁止下单
            var noOrders = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fnoorders", false);

            // 销售合同允许超额收款
            var canExcess = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanexcess", false);

            // 销售退换货允许变更合同
            var returnmodifyorder = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "freturnmodifyorder", false);



            var orderCommon = new OrderCommon(this.Context);
            var saleTransferOrderBillTypeId = orderCommon.GetSaleTransferOrderBillTypeId();

            var billTypeService = userCtx.Container.GetService<IBillTypeService>();

            foreach (var dataEntity in dataEntities)
            {
                //this.CheckCustomdesAndAttrinfoEmpty(dataEntity, productObjs);

                this.CheckIsExistZ2Shop(dataEntity);

                this.CheckStaffDept(dataEntity, saleTransferOrderBillTypeId);
                //【无统一零售价商品禁止下单】，原为保存的时候触发校验，改为提交的时候触发此校验；
                //this.CheckUniformRetailPrice(dataEntity, productObjs, noOrders);

                this.CheckCanExcess(dataEntity, canExcess);

                this.CheckPhone(dataEntity, enablePhoneLengthCtrl, phoneStandardControl);

                this.CheckIsExistTopProEmptyResultBrand(dataEntity, productObjs);

                var isNoCheckIsBizOutQtyFull = false;
                this.CheckIsBizOutQtyFullWhenChangeSaves(dataEntity, productObjs, returnmodifyorder, out isNoCheckIsBizOutQtyFull);
                if (!isNoCheckIsBizOutQtyFull) this.CheckIndbQtyIsFullWhenChangeSaves(dataEntity, productObjs, returnmodifyorder);

                this.CheckIsExistEmptyStoreHouse(dataEntity, productObjs);

                this.CheckProjectCustomer(dataEntity);

                this.CheckCustomCategory(dataEntity, billTypeService);
                this.CheckDropShipment(dataEntity, productObjs);
                this.CheckPackQty(this.Context, dataEntity, formInfo);
                this.CheckPiecesTag(this.Context, dataEntity, formInfo);

            }

            //this.CheckHaveTransOrder(dataEntities);

            this.CheckExpenseItem(dataEntities);

            this.CheckMallOrderNo(dataEntities);

            this.CheckOrderAddress(dataEntities);

            //75249 【250492】 【慕思现场-04-14】标准销售合同可以下单新渠道商品且合作渠道未校验
            this.CheckProductCateGoryIsNewChannel(userCtx, dataEntities, billTypeService, productObjs, seriesObjs);
            this.CheckNeedInvoiceNeedFieldStatus(userCtx, dataEntities);
            //this.CheckZYOrder(dataEntities);
            this.CheckOrderChannel(userCtx, dataEntities);

            this.CheckSalTransferOrder(userCtx, dataEntities, saleTransferOrderBillTypeId);

            return this.Result;
        }

        /// <summary>
        /// 校验合同【焕新订单标记】=是 and 【单据类型】=’v6定制柜合同
        /// </summary>
        private void CheckCustomCategory(DynamicObject dataEntity, IBillTypeService billTypeService)
        {
            // 不校验
            if (this.FromPurchaseOrderSubmitAgent) return;

            var renewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
            var billType = billTypeService.GetBillTypeById(this.Context, Convert.ToString(dataEntity["fbilltype"]));
            if (renewalflag && Convert.ToString(billType["fname"]) == "v6定制柜合同")
            {
                if (dataEntity["fcustomcategory"].IsNullOrEmptyOrWhiteSpace())
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】焕新定制柜类别不能为空！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        private void CheckOrderAddress(IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var typename = JNConvert.ToStringAndTrim((dataEntity["fbilltype_ref"] as DynamicObject)?["fname"]);
                if (Convert.ToString(dataEntity["fbilltype"]).EqualsIgnoreCase("ydj_order_vsix") || Convert.ToString(typename).EqualsIgnoreCase("v6定制柜合同"))
                {
                    if (Convert.ToString(dataEntity["frenewtype"]).IsNullOrEmptyOrWhiteSpace()) continue;
                    var RenewtypeObj = (dataEntity["frenewtype_ref"] as DynamicObject);
                    var fenableaddress = Convert.ToBoolean(RenewtypeObj?["fenableaddress"] ?? false);
                    var fenableaddress_gb = Convert.ToBoolean(RenewtypeObj?["fenableaddress_gb"] ?? false);
                    //如果启用地址的话
                    if (fenableaddress)
                    {
                        var fsettlprogress = Convert.ToString(dataEntity["fsettlprogress"]);
                        var IsMatch = AddressMatch(RenewtypeObj, dataEntity, "");
                        if (!IsMatch && fsettlprogress.EqualsIgnoreCase(Enu_RenewalSettleProgress.待发起))
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"当前焕新合同【{dataEntity["fbillno"]}】的地址，不符合[{RenewtypeObj["fname"]}]要求，请确认！",
                                DataEntity = dataEntity,
                            });
                        }
                    }
                }
            }
        }

        private bool AddressMatch(DynamicObject RenewtypeObj, DynamicObject order, string GbStr)
        {
            bool isMatch = true;
            var fprovince = Convert.ToString(RenewtypeObj?["fprovince"]) ?? "";
            var fcity = Convert.ToString(RenewtypeObj?["fcity"]) ?? "";
            var fregion = Convert.ToString(RenewtypeObj?["fregion"]) ?? "";
            var fprovince_od = Convert.ToString(order?[$"fprovince{GbStr}"]) ?? "";
            var fcity_od = Convert.ToString(order?[$"fcity{GbStr}"]) ?? "";
            var fregion_od = Convert.ToString(order?[$"fregion{GbStr}"]) ?? "";
            //先取焕新配置里最细维度。如果设置到了区，那合同只能以按区去匹配是否满足
            if (!fregion.IsNullOrEmptyOrWhiteSpace())
            {
                return fregion.EqualsIgnoreCase(fregion_od);
            }
            //如果焕新配置只设置到了市，则合同只匹配市即可
            if (!fcity.IsNullOrEmptyOrWhiteSpace())
            {
                return fcity.EqualsIgnoreCase(fcity_od);
            }
            //如果焕新配置只设置到了省，则合同只匹配省即可
            if (!fprovince.IsNullOrEmptyOrWhiteSpace())
            {
                return fprovince.EqualsIgnoreCase(fprovince_od);
            }

            return isMatch;
        }

        /// <summary>
        /// 校验【销售部门和销售员】
        /// </summary>
        private void CheckStaffDept(DynamicObject dataEntity, string saleTransferOrderBillTypeId)
        {
            // 不校验
            if (this.FromPurchaseOrderSubmitAgent) return;

            if (dataEntity["fdeptid"].IsNullOrEmptyOrWhiteSpace() || dataEntity["fstaffid"].IsNullOrEmptyOrWhiteSpace())
            {
                //当前销售合同的单据类型是否【销售转单】类型
                var isSaleTransferOrder = saleTransferOrderBillTypeId.EqualsIgnoreCase(Convert.ToString(dataEntity["fbilltype"]));
                if (!isSaleTransferOrder)
                {
                    //判断是否通过转单申请单审核系统创建的【销售转单】《销售合同》
                    isSaleTransferOrder = Convert.ToBoolean(dataEntity["fissaletransferorder"]);
                }
                if (!isSaleTransferOrder)
                {
                    if (dataEntity["fdeptid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】门店名称不能为空！",
                            DataEntity = dataEntity,
                        });
                    }
                    if (dataEntity["fstaffid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】销售员不能为空！",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 校验【无统一零售价商品禁止下单】
        /// </summary>
        private void CheckUniformRetailPrice(DynamicObject dataEntity, DynamicObjectCollection productObjs, bool noOrders)
        {
            var typename = JNConvert.ToStringAndTrim((dataEntity["fbilltype_ref"] as DynamicObject)?["fname"]);
            if (Convert.ToString(dataEntity["fbilltype"]).EqualsIgnoreCase("ydj_order_vsix") || Convert.ToString(typename).EqualsIgnoreCase("v6定制柜合同")) return;

            if (productObjs == null || !productObjs.Any()) return;

            var entrys = dataEntity["fentry"] as DynamicObjectCollection;

            var orderService = this.Context.Container.GetService<IOrderService>();

            foreach (var entry in entrys)
            {
                var productId = Convert.ToString(entry["fproductid"]);
                var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                if (productObj == null) continue;

                var dic = new Dictionary<string, object>();
                dic.Add("fissuit", Convert.ToString(productObj["fsuiteflag"]) == "1");
                dic.Add("funstdtype", entry["funstdtype"]);
                dic.Add("fprice", entry["fprice"]);
                dic.Add("fisgiveaway", entry["fisgiveaway"]);
                dic.Add("fmainorgid", Convert.ToString(productObj["fmainorgid"]));

                if (!orderService.CheckNoOrders(this.Context, dic))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】第{entry["fseq"]}行编码为【{productObj["fnumber"]}】的商品零售价为0，禁止下单！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 校验【是否已经转单，如果已经转单则禁止修改，需提示：对不起，合同已转单处理】
        /// </summary>
        private void CheckHaveTransOrder(DynamicObject[] dataEntitys)
        {
            var ids = dataEntitys?.Select(o => Convert.ToString(o["id"]))?.ToList();
            if (ids == null || !ids.Any()) return;

            var sqlText = "";
            if (ids.Count == 1)
            {
                sqlText = $"select top 1 fid from t_ydj_orderentry with(nolock) where fid='{ids[0]}' and ftransferorderstatus in (1,2)";
            }
            else
            {
                sqlText = $"select distinct fid from t_ydj_orderentry with(nolock) where fid in('{string.Join("','", ids)}') and ftransferorderstatus in (1,2)";
            }
            var dynObjs = this.DBService.ExecuteDynamicObject(this.Context, sqlText);
            if (!dynObjs.Any()) return;

            foreach (var dataEntity in dataEntitys)
            {
                var exists = dynObjs.FirstOrDefault(o => Convert.ToString(o["fid"]).EqualsIgnoreCase(Convert.ToString(dataEntity["id"])));
                if (exists != null)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】已转单处理！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 校验【商场合同号】
        /// </summary>
        private void CheckMallOrderNo(DynamicObject[] dataEntitys)
        {
            var deptIds = dataEntitys
                ?.Where(x => !x["fdeptid"].IsNullOrEmptyOrWhiteSpace() && x["fmallorderno"].IsNullOrEmptyOrWhiteSpace())
                ?.Select(x => Convert.ToString(x["fdeptid"]))
                ?.Distinct()
                ?.ToList();

            if (deptIds == null || !deptIds.Any()) return;

            deptIds = this.Context.LoadBizBillHeadDataById("ydj_dept", deptIds, "fisunifiedorder")
                ?.Where(x => Convert.ToString(x["fisunifiedorder"]) == "1")
                ?.Select(x => Convert.ToString(x["id"]))
                ?.ToList();

            if (deptIds == null || !deptIds.Any()) return;

            foreach (var dataEntity in dataEntitys)
            {
                if (!dataEntity["fmallorderno"].IsNullOrEmptyOrWhiteSpace()) continue;

                var deptId = Convert.ToString(dataEntity["fdeptid"]);
                if (!deptIds.Contains(deptId)) continue;

                this.Result.Errors.Add(new ValidationResultEntry()
                {
                    ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】的商场合同号不能为空！",
                    DataEntity = dataEntity,
                });
            }
        }

        /// <summary>
        /// 校验【是否允许超额收款】
        /// </summary>
        private void CheckCanExcess(DynamicObject dataEntity, bool canExcess)
        {
            if (canExcess) return;

            //如果不允许超额收款，那么订单总额必须大于确认已收金额
            var sumAmount = Convert.ToDecimal(dataEntity["fsumamount"]); //订单总额
            var receivable = Convert.ToDecimal(dataEntity["freceivable"]); //确认已收
            if (sumAmount < receivable)
            {
                this.Result.Errors.Add(new ValidationResultEntry()
                {
                    ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】不允许超额收款，订单总额必须大于等于确认已收金额！",
                    DataEntity = dataEntity,
                });
            }
        }

        /// <summary>
        /// 校验【手机号】
        /// </summary>
        private void CheckPhone(DynamicObject dataEntity, bool enablePhoneLengthCtrl, bool phoneStandardControl)
        {
            var phone = Convert.ToString(dataEntity["fphone"]);
            if (phone.IsNullOrEmptyOrWhiteSpace()) return;

            if (enablePhoneLengthCtrl)
            {
                if (phone.IndexOf(" ") > 0)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】手机号存在空格不符合规范！",
                        DataEntity = dataEntity,
                    });
                }
                if (!CheckPhoneNum.isNumeric(phone.Replace(" ", "")))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】手机号存在特殊字符不符合规范！",
                        DataEntity = dataEntity,
                    });
                }
                var phoneLength = phone.Trim().Length;
                if (phoneLength != 11)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】手机号请填写11位数字，当前位数：{phoneLength}！",
                        DataEntity = dataEntity,
                    });
                }
            }

            if (!CheckPhoneNum.CheckPhone(this.Context, phone, phoneStandardControl))
            {
                this.Result.Errors.Add(new ValidationResultEntry()
                {
                    ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】手机号前3位不符合国内电信号段规范，请填入国内真实手机号！",
                    DataEntity = dataEntity,
                });
            }
        }

        /// <summary>
        /// 校验【费用项目】
        /// </summary>
        private void CheckExpenseItem(DynamicObject[] dataEntitys)
        {
            var expensEntity = this.HtmlForm.GetEntryEntity("fexpenseentry");
            var disburseEntity = this.HtmlForm.GetEntryEntity("fdisburseentry");

            // 分析重复的费用项目ID
            var _itemIds = new List<string>();
            var _dataEntitysKv1 = new Dictionary<DynamicObject, List<string>>();
            var _dataEntitysKv2 = new Dictionary<DynamicObject, List<string>>();
            foreach (var dataEntity in dataEntitys)
            {
                var _itemIds1 = TryGetItemIds(expensEntity, dataEntity);
                if (_itemIds1.Any())
                {
                    _itemIds.AddRange(_itemIds1);
                    _dataEntitysKv1[dataEntity] = _itemIds1;
                }
                var _itemIds2 = TryGetItemIds(disburseEntity, dataEntity);
                if (_itemIds2.Any())
                {
                    _itemIds.AddRange(_itemIds2);
                    _dataEntitysKv2[dataEntity] = _itemIds2;
                }
            }
            _itemIds = _itemIds.Distinct().ToList();
            if (!_itemIds.Any()) return;

            // 批量加载费用项目信息
            var itemObjs = this.Context.LoadBizBillHeadDataById("ydj_expenseitem", _itemIds, "fname");

            FillErrors(expensEntity, _dataEntitysKv1);
            FillErrors(disburseEntity, _dataEntitysKv2);

            //填充错误信息
            void FillErrors(HtmlEntity entity, Dictionary<DynamicObject, List<string>> dataEntitysKv)
            {
                foreach (var item in dataEntitysKv)
                {
                    var itemNames = new List<string>();
                    foreach (var itemId in item.Value)
                    {
                        var itemObj = itemObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(itemId));
                        var itemName = Convert.ToString(itemObj?["fname"]);
                        if (itemName.IsNullOrEmptyOrWhiteSpace())
                        {
                            itemNames.Add(itemName);
                        }
                    }
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"{this.HtmlForm.Caption}【{item.Key["fbillno"]}】{entity.Caption}明细里的费用项目【{string.Join(",", itemNames)}】不允许重复！",
                        DataEntity = item.Key,
                    });
                }
            }

            //获取重复的费用项目ID
            List<string> TryGetItemIds(HtmlEntity entity, DynamicObject dataEntity)
            {
                var __itemIds = new List<string>();

                var entrys = dataEntity[entity.PropertyName] as DynamicObjectCollection;
                var groups = entrys
                    ?.Where(o => !o["fexpenseitemid"].IsNullOrEmptyOrWhiteSpace())
                    ?.Select(o => Convert.ToString(o["fexpenseitemid"]))
                    ?.GroupBy(o => o)
                    ?.Where(o => o.Count() > 1);

                if (groups != null && groups.Any())
                {
                    __itemIds.AddRange(groups.Select(o => o.Key));
                }

                return __itemIds;
            }
        }

        /// <summary>
        /// 校验【是否存在总部商品且业绩品牌为空的商品明细】
        /// </summary>
        private void CheckIsExistTopProEmptyResultBrand(DynamicObject dataEntity, DynamicObjectCollection productObjs)
        {
            if (productObjs == null || !productObjs.Any()) return;

            //总部企业标识
            var topOrgId = this.Context.IsTopOrg ? this.Context.Company : this.Context.TopCompanyId;

            var entrys = dataEntity["fentry"] as DynamicObjectCollection;
            foreach (var entry in entrys)
            {
                var resultBrandId = Convert.ToString(entry["fresultbrandid"]);
                var productId = Convert.ToString(entry["fproductid"]);
                var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                var productOrgId = Convert.ToString(productObj?["fmainorgid"]);

                //商品是否是总部商品
                if (topOrgId.EqualsIgnoreCase(productOrgId) && resultBrandId.IsNullOrEmptyOrWhiteSpace())
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】第{Convert.ToInt32(entry["fseq"])}行商品【{Convert.ToString(productObj["fname"])}】业绩品牌为空，无法保存！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 校验【当变更时保存时部分关闭明细行的[销售数量]是否小于[销售已出库数]】
        /// </summary>
        private void CheckIsBizOutQtyFullWhenChangeSaves(DynamicObject dataEntity, DynamicObjectCollection productObjs, bool returnmodifyorder, out bool isNoCheckIsBizOutQtyFull)
        {
            isNoCheckIsBizOutQtyFull = false;
            if (productObjs == null || !productObjs.Any()) return;

            if (Convert.ToString(dataEntity["fchangestatus"]).Trim() != "1") return;

            var entrys = dataEntity["fentry"] as DynamicObjectCollection;
            foreach (var entry in entrys)
            {
                var productId = Convert.ToString(entry["fproductid"]);
                if (productId.IsNullOrEmptyOrWhiteSpace()) continue;

                int closeStatus = 0;
                decimal bizQty = 0M, bizOutQty = 0M, bizReturnQty = 0M;
                int.TryParse(Convert.ToString(entry["fclosestatus_e"]), out closeStatus);
                decimal.TryParse(Convert.ToString(entry["fbizqty"]), out bizQty);
                decimal.TryParse(Convert.ToString(entry["fbizoutqty"]), out bizOutQty);
                decimal.TryParse(Convert.ToString(entry["fbizreturnqty"]), out bizReturnQty);

                if (!returnmodifyorder)
                {
                    if (closeStatus == (int)CloseStatus.Part && bizQty < bizOutQty - bizReturnQty)
                    {
                        isNoCheckIsBizOutQtyFull = true;
                        var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                        var bizQtyStr = Math.Round(bizQty, 2).ToString("0.##");
                        var bizReturnQtyStr = Math.Round(bizOutQty - bizReturnQty, 2).ToString("0.##");
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】第{entry["fseq"]}行商品【{productObj["fname"] ?? ""}】的销售数量[{bizQtyStr}]低于[{bizReturnQtyStr}]（销售已出库数-销售已退换数量），请修改销售数量重新保存！",
                            DataEntity = dataEntity,
                        });
                    }
                }
                else
                {
                    //销售管理参数【销售退换货允许变更合同】开启时
                    decimal returningQty = 0M;
                    decimal.TryParse(Convert.ToString(entry["fbizreturningqty"]), out returningQty);//销售退换中数量
                    if (bizQty < bizOutQty - bizReturnQty - returningQty)
                    {
                        isNoCheckIsBizOutQtyFull = true;
                        var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                        var bizQtyStr = Math.Round(bizQty, 2).ToString("0.##");
                        var bizReturnQtyStr = Math.Round(bizOutQty - bizReturnQty - returningQty, 2).ToString("0.##");
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】第{entry["fseq"]}行商品【{productObj["fname"] ?? ""}】的销售数量[{bizQtyStr}]低于[{bizReturnQtyStr}]（销售已出库数-销售已退换数量-销售退换中数量），请修改销售数量重新保存！",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 校验【商品明细.销售数量】≥【商品明细.已推出库数-商品明细.销售已退换数量】且【商品明细.销售数量】≥【商品明细.调拨在途量】】
        /// </summary>
        private void CheckIndbQtyIsFullWhenChangeSaves(DynamicObject dataEntity, DynamicObjectCollection productObjs, bool returnmodifyorder)
        {
            if (productObjs == null || !productObjs.Any()) return;

            if (Convert.ToString(dataEntity["fchangestatus"]).Trim() != "1") return;

            var entrys = dataEntity["fentry"] as DynamicObjectCollection;
            foreach (var entry in entrys)
            {
                var productId = Convert.ToString(entry["fproductid"]);
                if (productId.IsNullOrEmptyOrWhiteSpace()) continue;

                decimal indbQty = 0M, bizQty = 0M, transoutQty = 0M, bizReturnQty = 0M;
                //调拨在途量
                decimal.TryParse(Convert.ToString(entry["findbqty"]), out indbQty);
                //销售数量
                decimal.TryParse(Convert.ToString(entry["fbizqty"]), out bizQty);
                //已推出库数
                decimal.TryParse(Convert.ToString(entry["ftransoutqty"]), out transoutQty);
                //销售已退换数量
                decimal.TryParse(Convert.ToString(entry["fbizreturnqty"]), out bizReturnQty);

                //销售数量>=已推出库数-销售已退换数量并且销售数量》=调拨在途量
                //if (bizQty < indbQty || bizQty < (transoutQty - bizReturnQty))
                var flag = bizQty < indbQty || bizQty < (transoutQty - bizReturnQty);
                if (returnmodifyorder)
                {
                    //当开启参数时，需考虑退换中数量
                    decimal returningQty = 0M;
                    decimal.TryParse(Convert.ToString(entry["fbizreturningqty"]), out returningQty);//销售退换中数量
                    flag = bizQty < indbQty || bizQty < (transoutQty - bizReturnQty - returningQty);
                }
                if (flag)
                {
                    var proObj = entry["fproductid_ref"] as DynamicObject;
                    var detailMsg = $"校验行号【{entry["FSeq"]}】,商品编码【{proObj["fnumber"]}】";
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $@"对不起，当前合同下游单据在作业中，禁止保存！{detailMsg}",
                        DataEntity = dataEntity,
                    });
                    break;
                }
            }
        }
        /// <summary>
        /// 明细勾选【出现货】但是没选【仓库】要先提示出来 后面再去校验库存
        /// </summary>
        private void CheckIsExistEmptyStoreHouse(DynamicObject dataEntity, DynamicObjectCollection productObjs)
        {
            if (productObjs == null || !productObjs.Any()) return;

            var fchangestatus = Convert.ToString(dataEntity["fchangestatus"]);
            // 变更中，变更已提交
            var changingStatuses = new string[] { "1", "3" };

            var entrys = dataEntity["fentry"] as DynamicObjectCollection;
            foreach (var entry in entrys)
            {
                var productId = Convert.ToString(entry["fproductid"]);
                if (productId.IsNullOrEmptyOrWhiteSpace()) continue;

                var fclosestatus = Convert.ToInt32(entry["fclosestatus_e"]);

                // 对于【行关闭状态】=自动关闭或手动关闭的合同商品明细行，变更保存时，要跳过“出现货，请先输入仓库”这一个校验。
                // 变更中，且【行关闭状态】=自动关闭或手动关闭
                if (changingStatuses.Contains(fchangestatus) && (fclosestatus == (int)CloseStatus.Auto || fclosestatus == (int)CloseStatus.Manual))
                {
                    continue;
                }

                //是否勾选出现货
                var isOutSpot = Convert.ToBoolean(entry["fisoutspot"]);
                var storeHouseId = Convert.ToString(entry["fstorehouseid"]);
                if (isOutSpot && storeHouseId.IsNullOrEmptyOrWhiteSpace())
                {
                    var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】第{entry["fseq"]}行商品【{productObj["fname"] ?? ""}】是出现货，请先选择仓库再保存！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }
        /// <summary>
        /// 校验若商品档案勾选了<允许选配或非标产品>标识，则在销售合同下单时新增判断校验：当定制说明+辅助属性2个字段都为空时，则不允许下单
        /// </summary>
        private void CheckCustomdesAndAttrinfoEmpty(DynamicObject dataEntity, DynamicObjectCollection productObjs)
        {
            if (productObjs == null || !productObjs.Any()) return;

            var entrys = dataEntity["fentry"] as DynamicObjectCollection;
            foreach (var entry in entrys)
            {
                var productId = Convert.ToString(entry["fproductid"]);
                var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));

                //商品档案勾选了<允许选配或非标产品>标识
                if (Convert.ToString(productObj["fispresetprop"]) == "1" || Convert.ToString(productObj["funstdtype"]) == "1")
                {
                    if (Convert.ToString(entry["fattrinfo"]).IsNullOrEmptyOrWhiteSpace() &&
                        Convert.ToString(entry["fcustomdes_e"]).IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】商品行{Convert.ToInt32(entry["fseq"])}商品【{Convert.ToString(productObj["fname"])}】是定制编号，但未进行定制，需要定制后再操作订单;",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        }

        private void CheckProjectCustomer(DynamicObject dataEntity)
        {
            string fcustomerid = Convert.ToString(dataEntity["fcustomerid"]);
            string fprojectnumber = Convert.ToString(dataEntity["fprojectnumber"]);
            if (!string.IsNullOrWhiteSpace(fprojectnumber))
            {
                if ((dataEntity["fprojectnumber_ref"] as DynamicObject) == null)
                {
                    // 加载数据
                    var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                    refObjMgr?.Load(this.Context, new[] { dataEntity }, true, this.HtmlForm, new List<string> { "fprojectnumber" });
                }
                string projectcustomerid = (dataEntity["fprojectnumber_ref"] as DynamicObject)?["fcustomerid"].ToString();
                if (projectcustomerid != fcustomerid)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】关联合同失败，消费者不是同一人，请重新关联！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 校验是否存在系列为【慕思经典-新渠道" (Z2)】的商品,若有 合作渠道必填
        /// http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=72943
        /// 调整为：
        /// 门店上样合同，明细存在 商品.业绩品牌=“慕思经典-新渠道”（编码：Z2）时合作渠道必录校验。
        /// #75244 【250491】 【慕思现场4.10-4.14】新渠道系列拆分Z2 慕思经典-甄选 ，Z5 慕思经典-优选
        /// </summary>
        private void CheckIsExistZ2Shop(DynamicObject dataEntity)
        {
            string fchannel = Convert.ToString(dataEntity["fchannel"]);
            DynamicObject fbill = dataEntity["fbilltype_ref"] as DynamicObject;
            if (fchannel.IsNullOrEmptyOrWhiteSpace() && ((fbill["fnumber"].ToString().EqualsIgnoreCase("MDSYXSHT_SYS_01") || fbill["fname"].ToString().EqualsIgnoreCase("门店上样") || (fbill["fnumber"].ToString().EqualsIgnoreCase("XSZD_SYS_01") || fbill["fname"].ToString().EqualsIgnoreCase("销售转单")))))
            {
                List<string> seriesids = (dataEntity["fentry"] as DynamicObjectCollection).Select(p => Convert.ToString(p["fresultbrandid"])).ToList();
                var sqlText = $"select top 1 fid from t_ydj_series with(nolock) where fid in('{string.Join("','", seriesids)}') and fisnewchannel='1' AND fmainorgid='{this.Context.TopCompanyId}'";
                var dynObjs = this.DBService.ExecuteDynamicObject(this.Context, sqlText);
                if (dynObjs.Count > 0)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $@"存在新渠道商品,请维护【合作渠道】,谢谢！！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 商品系列为【甄选】或者【优选】属于新渠道商品,单据类型要为大客户销售合同
        /// 75249 【250492】 【慕思现场-04-14】标准销售合同可以下单新渠道商品且合作渠道未校验
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="billTypeService"></param>
        /// <param name="productObjs"></param>
        /// <param name="seriesObjs"></param>
        private void CheckProductCateGoryIsNewChannel(UserContext userCtx, DynamicObject[] dataEntitys, IBillTypeService billTypeService, DynamicObjectCollection productObjs, DynamicObjectCollection seriesObjs)
        {
            foreach (var dataEntity in dataEntitys)
            {

                var fisresellorder = Convert.ToBoolean(dataEntity["fisresellorder"]);
                //二级销售合同不校验
                if (fisresellorder) continue;
                var productEntrys = dataEntity["fentry"] as DynamicObjectCollection;

                if (productEntrys != null && productEntrys.Any())
                {
                    var billType = billTypeService.GetBillTypeById(this.Context, Convert.ToString(dataEntity["fbilltype"]));

                    var billTypeName = Convert.ToString(billType["fname"]);

                    var errorMsgList = new List<string>();

                    //【单据类型】不等于【大客户销售合同】或者不等于【门店上样】
                    if (!billTypeName.EqualsIgnoreCase("大客户销售合同") && !billTypeName.EqualsIgnoreCase("门店上样") && !billTypeName.EqualsIgnoreCase("销售转单"))
                    {
                        //未出现货的明细行
                        var notOutSpotProductEntrys = productEntrys.Where(x => !Convert.ToBoolean(x["fisoutspot"])).ToList();

                        foreach (var notOutSpotProductEntry in notOutSpotProductEntrys)
                        {
                            var entryCloseStatus = Convert.ToString(notOutSpotProductEntry["fclosestatus_e"]);

                            //销售数量
                            var bizQty = Convert.ToDecimal(notOutSpotProductEntry["fbizqty"]);

                            var autoCloseStatus = Convert.ToString(Convert.ToInt32(CloseStatus.Auto));

                            var manualCloseStatus = Convert.ToString(Convert.ToInt32(CloseStatus.Manual));

                            //行关闭状态:正常
                            //var defaultCloseStatus = Convert.ToString(Convert.ToInt32(CloseStatus.Default));

                            //行关闭状态为自动关闭或者手动关闭
                            //销售合同在变更的时候
                            //task 75814 【内部提单】【慕思现场-4.28】历史销售合同变更保存校验了商品系列
                            //进行退换货的时候，销售数量变成0，这时候就不校验新渠道的了
                            if (entryCloseStatus.Equals(autoCloseStatus) || entryCloseStatus.Equals(manualCloseStatus) || bizQty <= 0)
                            {
                                continue;
                            }

                            var productId = Convert.ToString(notOutSpotProductEntry["fproductid"]);

                            var findProduct = productObjs.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(productId));

                            if (findProduct != null)
                            {
                                //Z2 慕思经典-甄选
                                var productOrgId = Convert.ToString(findProduct["fmainorgid"]);

                                //总部商品
                                if (productOrgId.EqualsIgnoreCase(userCtx.TopCompanyId))
                                {
                                    var seriesId = Convert.ToString(findProduct["fseriesid"]);
                                    if (!seriesId.IsNullOrEmptyOrWhiteSpace())
                                    {
                                        if (seriesObjs != null && seriesObjs.Any())
                                        {
                                            var findSeriesDy = seriesObjs.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(seriesId));
                                            if (findSeriesDy != null)
                                            {
                                                var seriesOrgId = Convert.ToString(findSeriesDy["fmainorgid"]);
                                                if (seriesOrgId.Equals(userCtx.TopCompanyId))
                                                {
                                                    if (Convert.ToString(findSeriesDy["fisnewchannel"]) == "1" || Convert.ToString(findSeriesDy["fisnewchannel"]) == "true")
                                                    {
                                                        var fseq = Convert.ToString(notOutSpotProductEntry["fseq"]);
                                                        errorMsgList.Add($"{fseq}");
                                                    }
                                                }

                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (errorMsgList != null && errorMsgList.Any())
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $@"商品明细行中的第{string.Join("、", errorMsgList.Select(x => $"【{x.ToString()}】"))}行商品为新渠道商品，若非出现货请切换至【大客户销售合同】下单！",
                                DataEntity = dataEntity,
                            });
                        }
                    }


                }
            }





        }


        /// <summary>
        /// 校验【一件代发】
        /// </summary>
        private void CheckDropShipment(DynamicObject dataEntity, DynamicObjectCollection productObjs)
        {
            if (Convert.ToBoolean(dataEntity["fpiecesendtag"]) && Convert.ToBoolean(dataEntity["fneedtransferorder"]))
            {
                this.Result.Errors.Add(new ValidationResultEntry()
                {
                    ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】合同已经勾选【一件代发】，就不允许勾选【需转单】！",
                    DataEntity = dataEntity,
                });
            }
            var entrys = dataEntity["fentry"] as DynamicObjectCollection;

            var orderService = this.Context.Container.GetService<IOrderService>();
            if (Convert.ToBoolean(dataEntity["fpiecesendtag"]))
            {

                foreach (var entry in entrys)
                {
                    var productId = Convert.ToString(entry["fproductid"]);
                    var fmainorgid = Convert.ToString((entry["fproductid_ref"] as DynamicObject)?["fmainorgid"]);
                    var deliverytype = Convert.ToString(entry["fdeliverytype"]);
                    var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                    if (string.IsNullOrWhiteSpace(deliverytype))
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】第{entry["fseq"]}行编码为【{productObj["fnumber"]}】的【交货方式】不能为空！",
                            DataEntity = dataEntity,
                        });
                    }

                    if (!fmainorgid.Equals(this.Context.TopCompanyId))
                    {
                        if (deliverytype.Equals("delivery_type_01"))
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】第{entry["fseq"]}行编码为【{productObj["fnumber"]}】的商品属于经销商自建商品，则【交货方式】不能为“总部直发”",
                                DataEntity = dataEntity,
                            });
                        }
                    }
                }
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="formInfo"></param>
        /// <param name="result"></param>
        /// <param name="noOrders"></param>
        private void CheckPackQty(UserContext userCtx, DynamicObject dataEntity, HtmlForm formInfo)
        {
            if (Convert.ToBoolean(dataEntity["fpiecesendtag"]))
            {
                var entry = dataEntity["fentry"] as DynamicObjectCollection;
                if (entry != null)
                {
                    foreach (var item in entry)
                    {
                        var bizqty = Convert.ToDecimal(item["fbizqty"]);
                        var fseq = Convert.ToDecimal(item["fseq"]);
                        var fdeliverytype = Convert.ToString(item["fdeliverytype"]);
                        if (fdeliverytype.Equals("delivery_type_01"))
                        {
                            var matNumber = Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fnumber"]);
                            var packqty = Convert.ToDecimal((item["fproductid_ref"] as DynamicObject)?["fpackqty"]);
                            if (packqty > 1)
                            {
                                if (bizqty % packqty > 0)
                                {
                                    this.Result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $@"{formInfo.Caption}{dataEntity["fbillno"]},第[{fseq}]行商品[{matNumber}]属于箱类商品，交货方式为“总部直发”，但是销售数量不满足整箱倍数，不允许一件代发操作，请核查！",
                                        DataEntity = dataEntity,
                                    });
                                }
                            }
                        }

                    }
                }
            }
        }

        private void CheckPiecesTag(UserContext userCtx, DynamicObject dataEntity, HtmlForm formInfo)
        {
            if (Convert.ToBoolean(dataEntity["fpiecesendtag"]))
            {
                var entry = dataEntity["fentry"] as DynamicObjectCollection;
                if (entry != null)
                {
                    foreach (var item in entry)
                    {
                        var fseq = Convert.ToDecimal(item["fseq"]);
                        var fdeliverytype = Convert.ToString(item["fdeliverytype"]);
                        var isOutSpot = Convert.ToBoolean(item["fisoutspot"]);
                        if (fdeliverytype.Equals("delivery_type_01") && isOutSpot)
                        {
                            var matNumber = Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fnumber"]);
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $@"{formInfo.Caption}{dataEntity["fbillno"]},第[{fseq}]行商品[{matNumber}]交货方式为“总部直发”，不允许勾选出现货！如需出现货，请修改交货方式为“门店直发”，谢谢！",
                                DataEntity = dataEntity,
                            });

                        }
                    }
                }
                var orderId = entry.Select(a => Convert.ToString(a["id"])).ToList();
                var purSql = $@"SELECT TOP 1 1 FROM t_ydj_purchaseorder a WITH(NOLOCK) INNER JOIN t_ydj_poorderentry b WITH(NOLOCK) on a.fid=b.fid
                WHERE b.fsoorderentryid in ('{string.Join("','", orderId)}') and fstatus<>'A' AND fchangestatus = '1'  and fcancelstatus='0'";
                using (var dr = this.DBService.ExecuteReader(this.Context, purSql))
                {
                    if (dr.Read())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】下游存在变更中的采购订单，暂不允许合同变更，请核查！",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 检查需开票的字段是否都有值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orderDys"></param>
        private void CheckNeedInvoiceNeedFieldStatus(UserContext userCtx, DynamicObject[] orderDys)
        {
            foreach (var orderDy in orderDys)
            {
                var isNeedInvoice = Convert.ToBoolean(Convert.ToInt32(orderDy["fisinvoiceneed"]));
                if (isNeedInvoice)
                {
                    //开票类型
                    var invoiceType = Convert.ToString(orderDy["finvoicetype"]);
                    //购买方全称
                    var buyerFullName = Convert.ToString(orderDy["fbuyerfullname"]);
                    //纳税人识别号
                    var taxPayerIdentify = Convert.ToString(orderDy["ftaxpayeridentify"]);
                    //电子邮箱
                    var invoiceEmail = Convert.ToString(orderDy["finvoiceemail"]);
                    //地址
                    var invoiceAddress = Convert.ToString(orderDy["finvoiceaddress"]);
                    //电话
                    var invoicePhone = Convert.ToString(orderDy["finvoicephone"]);
                    //开户银行
                    var depositBankName = Convert.ToString(orderDy["fdepositbankname"]);
                    //银行账户
                    var bankAccount = Convert.ToString(orderDy["fbankaccount"]);
                    if (invoiceType.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = "开票类型不能为空",
                            DataEntity = orderDy
                        });
                    }

                    if (buyerFullName.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = "购买方全称不能为空",
                            DataEntity = orderDy
                        });
                    }

                    if (taxPayerIdentify.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = "购买方全称不能为空",
                            DataEntity = orderDy
                        });
                    }

                    if (invoiceEmail.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = "电子邮箱不能为空",
                            DataEntity = orderDy
                        });
                    }

                    if (invoiceAddress.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = "地址不能为空",
                            DataEntity = orderDy
                        });
                    }

                    if (invoicePhone.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = "电话不能为空",
                            DataEntity = orderDy
                        });
                    }

                    if (depositBankName.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = "开户银行不能为空",
                            DataEntity = orderDy
                        });
                    }

                    if (bankAccount.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = "银行账户不能为空",
                            DataEntity = orderDy
                        });
                    }
                    
                    ValidateInvoiceEmails(this.Context, new DynamicObject[] { orderDy }, this.Result);
                    CheckInvoiceEntryBankAccountIsNumber(this.Context,new DynamicObject[] {orderDy},this.Result);
                }
            }
        }

        private void CheckZYOrder(IEnumerable<DynamicObject> dataEntities)
        {
            if (this.Context.IsDirectSale)
            {
                foreach (var dataEntity in dataEntities)
                {
                    var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                    // 判断所有商品行是否都为赠品（fisgiveaway=1）
                    bool allDirectGiveaway = entrys.All(x => Convert.ToBoolean(Convert.ToInt32(x["fisgiveaway"])));
                    if (allDirectGiveaway)
                    {
                        var relatedOrderNo = Convert.ToString(dataEntity["frelevanceorderno"]);
                        if (string.IsNullOrWhiteSpace(relatedOrderNo))
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = "直营销售订单商品行全部为赠品时，关联合同号必填",
                                DataEntity = dataEntity
                            });
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 检查销售合同关联的渠道是否合规(直营)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orderDys"></param>
        private void CheckOrderChannel(UserContext userCtx, DynamicObject[] orderDys)
        {
            if (userCtx.IsDirectSale())
            {
                //有关联合作渠道的销售合同数据包
                var hasChannelDys = orderDys.Where(x => !Convert.ToString(x["fchannel"]).IsNullOrEmptyOrWhiteSpace());

                if (hasChannelDys != null && hasChannelDys.Any())
                {
                    var refMgr = userCtx.Container.GetService<LoadReferenceObjectManager>();

                    refMgr.Load(userCtx, hasChannelDys.ToArray(), false, this.HtmlForm, new List<string>() { "fchannel" });
                    foreach (var hasChannelDy in hasChannelDys)
                    {
                        //合作渠道数据包
                        var channelDy = hasChannelDy["fchannel_ref"] as DynamicObject;
                        if (channelDy != null)
                        {
                            //这里交易流水号是传给慕思中台，当作外部供应商id,这里需要判断是否为空，不为空就行
                            var suppliercode = Convert.ToString(channelDy["fsuppliercode"]);
                            var billNo = Convert.ToString(hasChannelDy["fbillno"]);
                            var channelName = Convert.ToString(channelDy["fname"]);
                            if (suppliercode.IsNullOrEmptyOrWhiteSpace())
                            {
                                this.Result.Errors.Add(new ValidationResultEntry()
                                {
                                    ErrorMessage = $"销售合同【{billNo}】关联的合作渠道【{channelName}】的sap供应商编码信息待完善，请前往《合作渠道》档案补充完整。",
                                    DataEntity = hasChannelDy
                                });
                                continue;
                            }

                            var channelEntrys = channelDy["fentry"] as DynamicObjectCollection;
                            if (channelEntrys != null && channelEntrys.Any())
                            {
                                //flowerlimit 金额下限 fratiofupperlimit 提成比例 fupperlimit 金额上限
                                var isRowAny = channelEntrys.Any(x => Convert.ToDecimal(x["flowerlimit"]) > 0 || Convert.ToDecimal(x["fratio"]) > 0 || Convert.ToDecimal(x["fupperlimit"]) > 0);
                                if (!isRowAny)
                                {
                                    this.Result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $"销售合同【{billNo}】关联的合作渠道【{channelName}】的返佣比例待完善，请前往《合作渠道》档案补充完整。",
                                        DataEntity = hasChannelDy
                                    });
                                    continue;
                                }
                            }
                            else
                            {
                                this.Result.Errors.Add(new ValidationResultEntry()
                                {
                                    ErrorMessage = $"销售合同【{billNo}】关联的合作渠道【{channelName}】的返佣比例待完善，请前往《合作渠道》档案补充完整。",
                                    DataEntity = hasChannelDy
                                });
                                continue;
                            }
                        }
                    }
                }

            }


        }


        /// <summary>
        /// 转单校验，当送货方保存时，需要校验转单数据是否已转单审核通过，没通过则提示不允许保存，避免因为接口下发数据，前端保存出现，数据覆盖的问题
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orderDys"></param>
        private void CheckSalTransferOrder(UserContext userCtx, DynamicObject[] orderDys, string saleTransferOrderBillTypeId)
        {
            var zdOrders = orderDys.Where(a => Convert.ToString(a["fbilltype"]).Equals(saleTransferOrderBillTypeId)).ToList();
            foreach (var orderItem in zdOrders)
            {
                var billNo= Convert.ToString(orderItem["fbillno"]);
                var entry = orderItem["fentry"] as DynamicObjectCollection;
                var sourceEntryIds = entry.Where(a => !Convert.ToString(a["fsourceentryid_e"]).IsNullOrEmptyOrWhiteSpace()).Select(a => Convert.ToString(a["fsourceentryid_e"])).ToList();
                string sql = $@"select ftransferorderstatus,fentryid from t_ydj_orderentry with(nolock) where fentryid in ('{string.Join("','",sourceEntryIds)}')";
                using (var dr=userCtx.ExecuteReader(sql,null))
                {
                    while (dr.Read())
                    {
                        var transferOrderStatus = Convert.ToInt32(dr["ftransferorderstatus"]);
                        var entryId = Convert.ToString(dr["fentryid"]);
                        var seq= Convert.ToInt32(entry.FirstOrDefault(a=>Convert.ToString(a["fsourceentryid_e"]).Equals(entryId))?["fseq"]);
                        if (transferOrderStatus != 2)
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"销售合同【{billNo}】第【{seq}】行关联的转单未审批通过，不允许保存。",
                                DataEntity = orderItem
                            });
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// 判断开票信息中的银行账号是否为数字
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="result"></param>
        public void CheckInvoiceEntryBankAccountIsNumber(UserContext ctx, DynamicObject[] dataEntitys, ValidationResult result)
        {
            var checkNumRegex = new Regex(@"^\d+$");
            foreach (var dataEntity in dataEntitys)
            {
                var bankAccountStr = Convert.ToString(dataEntity["fbankaccount"]);
                if (!bankAccountStr.IsNullOrEmptyOrWhiteSpace())
                {
                    var isMatch = checkNumRegex.IsMatch(bankAccountStr);
                    if (!isMatch)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"需开票中的银行账户输入的格式有误，请检查格式!",
                            DataEntity = dataEntity,
                        });
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 检查开票信息中的电子邮箱格式是否正确
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="result"></param>
        public void ValidateInvoiceEmails(UserContext ctx, DynamicObject[] dataEntitys, ValidationResult result)
        {
            Regex emailRegex = new Regex(@"^[^\s@]+@[^\s@]+\.[^\s@]+$");

            foreach (var dataEntity in dataEntitys)
            {
                
                //电子邮箱
                var invoiceEmail = Convert.ToString(dataEntity["finvoiceemail"]);
                
                if (!invoiceEmail.IsNullOrEmptyOrWhiteSpace())
                {
                    string[] emails = invoiceEmail.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                    foreach (var emailStr in emails)
                    {
                        var isMatch = emailRegex.IsMatch(emailStr);
                        if (!isMatch)
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $@"需开票的电子邮箱格式有误，格式类似于***********或者多个邮箱格式************,<EMAIL>",
                                DataEntity = dataEntity
                            });
                            break;
                        }
                    }
                }
                
            }
        }
    }
}