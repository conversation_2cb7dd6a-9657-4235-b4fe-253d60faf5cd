using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.DataTransferObject;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Validation
{
    /// <summary>
    /// 选配商品明细辅助属性校验器：检查选配商品明细是否有辅助属性。
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlValidator_SelProductAuxPropValidation)]
    public class SelProductAuxPropValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 商品字段标识
        /// </summary>
        public string ProductFieldKey { get; set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);

            var joParam = validExpr?.FromJson<JObject>();
            this.ProductFieldKey = joParam.GetJsonValue("productFieldKey", "");
            if (this.ProductFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                throw new Exception("辅助属性校验操作参数配置错误，请显式提供商品字段标识！");
            }
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            var productField = formInfo.GetField(this.ProductFieldKey) as HtmlBaseDataField;
            if (productField == null)
            {
                throw new BusinessException($"{formInfo.Caption}不存在商品字段 {this.ProductFieldKey} 请检查表单模型！");
            }

            //商品关联的辅助属性字段
            var auxPropFields = formInfo.GetFieldList()
                .Where(o => o is HtmlAuxPropertyField && (o as HtmlAuxPropertyField).ControlFieldKey.EqualsIgnoreCase(productField.Id))
                .ToList();
            if (!auxPropFields.Any())
            {
                throw new BusinessException($"{formInfo.Caption}的{productField.Caption}字段 {this.ProductFieldKey} 没有关联到辅助属性字段，请检查表单模型！");
            }

            //商品ID
            var productIds = new List<string>();
            foreach (var dataEntitie in dataEntities)
            {
                var _productIds = (dataEntitie[productField.Entity.PropertyName] as DynamicObjectCollection)
                    ?.Select(o => Convert.ToString(o[productField.PropertyName]))
                    ?.ToList();
                productIds.AddRange(_productIds);
            }
            productIds = productIds.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (!productIds.Any()) return result;

            //批量加载商品
            var productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productIds, "fnumber,fname,fispresetprop");
            if (productObjs == null || !productObjs.Any()) return result;

            foreach (var dataEntitie in dataEntities)
            {
                var entrys = dataEntitie[productField.Entity.PropertyName] as DynamicObjectCollection;
                if (entrys == null) continue;

                foreach (var entry in entrys)
                {
                    var productId = Convert.ToString(entry[productField.PropertyName]);
                    if (productId.IsNullOrEmptyOrWhiteSpace()) continue;

                    var productObj = productObjs?.FirstOrDefault(o =>
                        Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                    if (productObj == null) continue;

                    if (Convert.ToString(productObj["fispresetprop"]) != "1") continue;

                    foreach (var auxPropField in auxPropFields)
                    {
                        if (entry[auxPropField.PropertyName].IsNullOrEmptyOrWhiteSpace())
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"{productField.Entity.Caption}行的商品" +
                                $"【{productObj["fnumber"]}/{productObj["fname"]}】启用了允许选配，{auxPropField.Caption}为必填项！",
                                DataEntity = dataEntitie
                            });
                        }
                    }
                }
            }

            return result;
        }
    }
}