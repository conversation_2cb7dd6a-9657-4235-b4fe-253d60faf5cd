using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.CustomException;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi
{
    /// <summary>
    /// 慕思响应
    /// </summary>
    public class MuSiResponse<T>
    {
        public int status { get; set; }
        public string message { get; set; }
        public bool State { get; set; }

        public int Code { get; set; }

        public string Msg { get; set; }

        public T data { get; set; }

        public object Page { get; set; }

        public string type { get; set; }

        public static MuSiResponse<T> SUCCESS { get; private set; } = new MuSiResponse<T> { State = true, Code = 200, Msg = "操作成功！" };
    }


    /// <summary>
    /// 慕思响应
    /// 中台直接对接SAP，我们按SAP的结构处理
    /// </summary>
    public class ZYMuSiResponse<T>
    {
        public string STATUS { get; set; }
        public string MESSAGE { get; set; }

        public T ITMS { get; set; }
        public bool State { get; set; }

        public int Code { get; set; }

        public string Msg { get; set; }

        public static ZYMuSiResponse<T> SUCCESS { get; private set; } = new ZYMuSiResponse<T> { State = true, Code = 200, Msg = "操作成功！" };
    }

    public static class MuSiResponseExtensions
    {
        /// <summary>将操作结果转化为异常抛出</summary>
        /// <param name="resp"></param>
        /// <param name="throwWhenNull"></param>
        /// <param name="errMsg"></param>
        public static void ThrowIfHasError<T>(
            this MuSiResponse<T> resp,
            bool throwWhenNull = false,
            string errMsg = "")
        {
            if (resp == null)
            {
                if (throwWhenNull)
                    throw new BusinessException(errMsg);
            }
            else
            {
                if (resp.Code != 200)
                {
                    throw new BusinessException(resp.Msg ?? errMsg);
                }
            }
        }
    }
}
