using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Service
{
    /// <summary>
    /// 标准品映射 错误数据修复
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("InitProductAuth_OtherData")]
    public class InitProductAuth_OtherData: AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            var htmlForm = this.Context.Container.GetService<IMetaModelService>()
                             .LoadFormModel(this.Context, "ydj_productauth_other");

            base.AfterExecuteOperationTransaction(e);
            var ProductAuthObjs_deliver = this.LoadProductAuthByDeliver();
            if (ProductAuthObjs_deliver == null || !ProductAuthObjs_deliver.Any())
            {
                this.Result.SimpleMessage = "没有查询到商品授权数据，当前无需处理。";
                return;
            }
            Task.Run(() =>
            {
                IMuSiService muSiService = this.Container.GetService<IMuSiService>();
                muSiService.SaveProductAuth_other(this.Context, htmlForm.Id, ProductAuthObjs_deliver);
            });
        }

        /// <summary>
        /// 加载城市+经销商 匹配送达方 的商品授权清单
        /// </summary>
        private DynamicObjectCollection LoadProductAuthByDeliver()
        {
            //总部企业标识
            var topOrgId = this.Context.IsTopOrg ? this.Context.Company : this.Context.TopCompanyId;
            var sqlText = $@"select distinct deliver.fid as deliverid,deliver.fsaleorgid,pauth.fid,'4' as forgtype  
                            from t_ydj_productauth pauth
                            inner join T_BAS_DELIVER deliver on pauth.forgid = deliver.fagentid and pauth.fcityid = deliver.fcity
                            where forgtype ='4' and fcityid !='' and deliver.fforbidstatus = 0 and pauth.fmainorgid = '{topOrgId}'
                            union
                            select distinct deliver.fid as deliverid,deliver.fsaleorgid,pauth.fid,'5' as forgtype
                            from t_ydj_productauth pauth
                            inner join T_BAS_STORE store on pauth.forgid = store.fid 
                            inner join T_BAS_DELIVER deliver on store.fagentid = deliver.fagentid and store.fmycity = deliver.fcity 
                            where forgtype ='5' and fcityid !='' and store.fforbidstatus = 0 and deliver.fforbidstatus = 0 and pauth.fmainorgid = '{topOrgId}'
";

            var dynObs = this.DBService.ExecuteDynamicObject(this.Context, sqlText);

            return dynObs;
        }
    }
}
