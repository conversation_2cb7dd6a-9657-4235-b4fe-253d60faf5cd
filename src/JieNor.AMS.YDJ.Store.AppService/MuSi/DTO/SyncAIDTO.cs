using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.DTO
{
    /// <summary>
    /// 同步慕思AI云
    /// </summary>
    public class SyncAIDTO : BaseDTO
    {
        private DynamicObject purchaseOrder;

        public SyncAIDTO(DynamicObject purchaseOrder)
        {
            this.purchaseOrder = purchaseOrder;
        }

        /// <summary>
        /// 订单ID（此属性是指CID）
        /// </summary>
        public string OrderId { get; set; }

        /// <summary>
        /// 子测量ID列表
        /// </summary>
        public List<Submeasure> SubMeasures { get; set; } = new List<Submeasure>();

        /// <summary>
        /// 成品床的宽度，单位CM，例如成品床长宽为(200CM*180CM)，这里就传180
        /// </summary>
        public int BedWidth { get; set; }

        /// <summary>
        /// 成品床的长度，单位CM，例如成品床长宽为(200CM*180CM)，这里就传200
        /// </summary>
        public int BedLength { get; set; }

        /// <summary>
        /// 订单详情对象，要求为json格式的金蝶订单的详情，依据金蝶系统对象实际情况传参
        /// </summary>
        public JObject OrderDetail { get; set; }

        /// <summary>
        /// 订单金额
        /// </summary>
        public decimal OrderAmount { get; set; }

        /// <summary>
        /// 门店编码
        /// </summary>
        public string StoreShopNumber { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        public string StoreName { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 用户地址
        /// </summary>
        public string UserAddr { get; set; }

        /// <summary>
        /// 用户手机号
        /// </summary>
        public string UserMobile { get; set; }

        /// <summary>
        /// 获取数据包里的编码
        /// </summary>
        /// <returns></returns>
        public override List<string> GetNumbers()
        {
            return new List<string> { Convert.ToString(this.purchaseOrder?["fbillno"]) };
        }

        public class Submeasure
        {
            /// <summary>
            /// 子测量项的顺序，从0（左侧）开始
            /// </summary>
            public int Orders { get; set; }

            /// <summary>
            /// 子测量项的测量报告ID
            /// </summary>
            public string SubMeasureId { get; set; }
        }
    }
}
