using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.CustomerRecord
{
    /// <summary>
    /// 下发商机
    /// </summary>
    [InjectService]
    [FormId("ydj_customerrecord")]
    [OperationNo("MSSaveSync")]
    public class MSSaveSync : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "BeforeConvertBaseData":
                    this.BeforeConvertBaseData(e);
                    break;
                case "AfterPackSourceBill":
                    this.AfterPackSourceBill(e);
                    break;
                case "BeforeSaveSourceBill":
                    this.BeforeSaveSourceBill(e);
                    break;
                case "SourceBillFieldMapping":
                    this.SourceBillFieldMapping(e);
                    break;
                case "BeforeCreateBaseData":
                    this.BeforeCreateBaseData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// //基础资料字段值转换前事件：可指定当前需要转换的基础资料字段
        /// </summary>
        /// <param name="e"></param>
        private void BeforeConvertBaseData(OnCustomServiceEventArgs e)
        {
        }

        /// <summary>
        /// 来源单据打包后事件：可对打包后的数据包进行处理或者直接覆盖整个数据包都是可以的
        /// </summary>
        /// <param name="e"></param>
        private void AfterPackSourceBill(OnCustomServiceEventArgs e)
        {

        }

        /// <summary>
        /// 创建基础资料前事件：可更改哪些基础资料不可创建
        /// </summary>
        /// <param name="e"></param>
        private void BeforeCreateBaseData(OnCustomServiceEventArgs e)
        {
            e.Cancel = true;
            e.Result = new List<string>
            {

            };
        }

        /// <summary>
        /// 来源单据保存前事件：可对当前要保存的数据包做处理
        /// </summary>
        /// <param name="e"></param>
        private void BeforeSaveSourceBill(OnCustomServiceEventArgs e)
        {
            var dataEntitys = (e.EventData as DynamicObject[])?.ToList();
            if (dataEntitys == null || dataEntitys.Count < 1) return;
            //得到户型枚举
            var htList = GetHouseTypeList();
            //装修进度
            var enumList = GetEnumAllList();
            foreach (var dataEntity in dataEntitys)
            {
                #region 户型
                var house_size = Convert.ToString(dataEntity["house_type"]);
                if (!house_size.IsNullOrEmptyOrWhiteSpace())
                {
                    var froomIndex = GetStrIndex(house_size, "室");//室位置
                    var fhallIndex = GetStrIndex(house_size, "厅");//厅位置
                    var ftoiletIndex = GetStrIndex(house_size, "卫");//卫位置
                    var fbalconyIndex = GetStrIndex(house_size, "阳");//阳台位置
                    if (froomIndex != -1)
                    {
                        //取室
                        var froomstr = house_size.Substring(froomIndex - 1, 1);
                        var numItem = ConvertLowerNumber(froomstr);
                        var fentryid = htList.Where(x => Convert.ToInt32(x["fenumitem"]) == numItem).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        dataEntity["froom_enum"] = fentryid;
                    }
                    if (fhallIndex != -1)
                    {
                        //取厅
                        var fhallstr = house_size.Substring(fhallIndex - 1, 1);
                        var numItem = ConvertLowerNumber(fhallstr);
                        var fentryid = htList.Where(x => Convert.ToInt32(x["fenumitem"]) == numItem).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        dataEntity["fhall_enum"] = fentryid;
                    }
                    if (ftoiletIndex != -1)
                    {
                        //取卫
                        var fhallstr = house_size.Substring(ftoiletIndex - 1, 1);
                        var numItem = ConvertLowerNumber(fhallstr);
                        var fentryid = htList.Where(x => Convert.ToInt32(x["fenumitem"]) == numItem).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        dataEntity["ftoilet_enum"] = fentryid;
                    }
                    if (fbalconyIndex != -1)
                    {
                        //取阳台
                        var fhallstr = house_size.Substring(fbalconyIndex - 1, 1);
                        var numItem = ConvertLowerNumber(fhallstr);
                        var fentryid = htList.Where(x => Convert.ToInt32(x["fenumitem"]) == numItem).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        dataEntity["fbalcony_enum"] = fentryid;
                    }
                }
                #endregion

                //装修进度
                var frenovation = Convert.ToString(dataEntity["frenovation"]);
                var rvid = enumList.Where(x => Convert.ToString(x["fenumitem"]) == frenovation).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                dataEntity["frenovation"] = rvid;

                //需求类型
                var frequirement = Convert.ToString(dataEntity["frequirement_type"]);
                var frequirementid = enumList.Where(x => Convert.ToString(x["fenumitem"]) == frequirement).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                dataEntity["frequirement_type"] = frequirementid;

                //购买空间
                var fspace = Convert.ToString(dataEntity["fspace"]);
                var fspaceid = enumList.Where(x => Convert.ToString(x["fenumitem"]) == fspace).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                dataEntity["fspace"] = fspaceid;

                //产品类型
                var fproduct_type = Convert.ToString(dataEntity["fprod_type"]);
                var dataptype = GetCategoryByName(fproduct_type).ToList();
                var pids = "";
                var pstrs = "";
                foreach (var item in dataptype)
                {
                    pids += Convert.ToString(item["fid"]) + ",";
                    pstrs += Convert.ToString(item["fname"]) + ",";
                }
                dataEntity["fproduct_type"] = pids.TrimEnd(',');
                dataEntity["fproduct_type_txt"] = pstrs.TrimEnd(',');

                #region 省市区
                var fprovince = Convert.ToString(dataEntity["fprovince"]);
                if (!fprovince.IsNumeric())
                {
                    var fprovinceid = enumList.Where(x => Convert.ToString(x["fenumitem"]).Contains(fprovince)).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                    dataEntity["fprovince"] = fprovinceid;
                    var fcity = Convert.ToString(dataEntity["fcity"]);
                    var fcityid = enumList.Where(x => Convert.ToString(x["fenumitem"]).Contains(fcity)).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                    if (!fcity.IsNullOrEmptyOrWhiteSpace() && fcityid.IsNullOrEmptyOrWhiteSpace() || fcityid.Length == 2)
                    {
                        fcityid = enumList.Where(x => Convert.ToString(x["fgroup"]).Contains(fprovinceid)).OrderBy(x => Convert.ToString(x["fentryid"])).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                    }
                    dataEntity["fcity"] = fcityid;
                    var fregion = Convert.ToString(dataEntity["fregion"]);
                    var fregionid = enumList.Where(x => Convert.ToString(x["fenumitem"]).Contains(fregion)).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                    dataEntity["fregion"] = fregionid;
                }

            
                #endregion

                //接口过来的默认总部下发
                dataEntity["fcustomersource"] = "164743697441886213";


            }
        }

        /// <summary>
        /// 转小写数字
        /// </summary>
        /// <returns></returns>
        private int ConvertLowerNumber(string numberStr)
        {
            int number = 0;
            #region 转换
            switch (numberStr)
            {
                case "壹":

                case "一":

                    number = 1;

                    break;

                case "两":

                case "贰":

                case "二":

                    number = 2;

                    break;

                case "叁":

                case "三":

                    number = 3;

                    break;

                case "肆":

                case "四":

                    number = 4;

                    break;

                case "伍":

                case "五":

                    number = 5;

                    break;

                case "陆":

                case "六":

                    number = 6;

                    break;

                case "柒":

                case "七":

                    number = 7;

                    break;

                case "捌":

                case "八":

                    number = 8;

                    break;

                case "玖":

                case "九":

                    number = 9;

                    break;

                case "拾":

                case "十":
                    number = 10;
                    break;
            }

            #endregion 
            return number;
        }

        /// <summary>
        /// 字符串中取对应位置
        /// </summary>
        /// <param name="strAll"></param>
        /// <param name="selStr"></param>
        /// <returns></returns>
        private int GetStrIndex(string strAll, string selStr)
        {
            if (strAll.IsNullOrEmptyOrWhiteSpace())
                return 0;
            return strAll.IndexOf(selStr);
        }

        /// <summary>
        /// 得到户型枚举
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetHouseTypeList()
        {
            string strSql = "select fentryid,fenumitem from T_BD_ENUMDATAENTRY where fid = '2770abfbd5c84b4389992966fa7ce859'";
            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return data;
        }

        /// <summary>
        /// 得到户型枚举
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetCategoryByName(string fnames)
        {
            string strSql = "select fid,fname from  ser_ydj_category where fname in  ('{0}')".Fmt(string.Join("','", fnames.Split(',')));
            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return data;
        }


        /// <summary>
        /// 得到装修进度、需求类型、需求等级、购买空间、产品类型 省市区
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetEnumAllList()
        {
            string strSql = "select fentryid,fenumitem,fgroup from T_BD_ENUMDATAENTRY where fid in('f00772dcfb674965aeb9f6a42c772c7f','860106413254639617','e4e1f9dd3bc544ababe0073d24cfd33b','2db5b72c650541e981d1b643fed9d096','860106413254639628','208b665d355d446f890fcc46568a3784','4ce1b6b47093415585878493ec1ad98b','6916ea2d44f24379a377cca5362bd7cf') ";
            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return data;
        }

        /// <summary>
        /// 来源单据字段映射事件：可对已存在的数据包做映射覆盖
        /// </summary>
        /// <param name="e"></param>
        private void SourceBillFieldMapping(OnCustomServiceEventArgs e)
        {
        }
    }
}
