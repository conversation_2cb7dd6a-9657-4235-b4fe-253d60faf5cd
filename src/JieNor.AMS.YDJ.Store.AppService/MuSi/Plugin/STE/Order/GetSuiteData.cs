using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.Order
{
    /// <summary>
    /// 根据套件组合码返回对应套件信息
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getsuitedata")]
    public class GetSuiteData : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var fsuitcombnumber = this.GetQueryOrSimpleParam<string>("fsuitcombnumber");
            var fsuitproductid = this.GetQueryOrSimpleParam<string>("fsuitproductid");
            var suite = this.Context.LoadBizDataByACLFilter("sel_suite",
                "fproductid=@fproductid",
                true,
                new[] { new SqlParam("@fproductid", System.Data.DbType.String, fsuitproductid)
            }).FirstOrDefault();
            var propSuiteCount = this.Context.LoadBizDataByFilter("sel_prop", "fname = '套件总件数'").FirstOrDefault();
            this.Result.SrvData = new Dictionary<string, object>()
                {
                    { "suite", suite },
                    { "fsuitcombnumber",fsuitcombnumber},
                    { "propsuitecount", (propSuiteCount == null ? "" : JsonConvert.SerializeObject(propSuiteCount)) }
                };
            this.Result.IsSuccess = true;
        }
    }
}
