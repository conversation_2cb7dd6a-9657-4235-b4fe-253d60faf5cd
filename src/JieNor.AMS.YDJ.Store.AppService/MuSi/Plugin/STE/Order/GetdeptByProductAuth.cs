using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Service;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同查找授权清单中的门店
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("GetdeptByProductAuth")]
    public class GetdeptByProductAuth : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            //得到门店商品授权清单系列
            string strSql = "SELECT DISTINCT te.fserieid,t.forgid FROM t_ydj_productauth t with(nolock)" +
                " INNER JOIN t_ydj_productauthbs te with(nolock) ON te.fid = t.fid";

            //返回门店只授权的某个品牌的系列。
            var result = this.DBService.ExecuteDynamicObject(this.Context, strSql).ToList();

            //返回慕思经典 商品授权清单中的门店
            var getSeries = GetStoreList(result);

            this.Result.SrvData = getSeries;
            this.Result.IsSuccess = true;
        }

        //返回包含慕思经典的系列
        private List<string> GetStoreList(List<DynamicObject> data)
        {
            var AllSeries = this.Context.LoadBizDataByFilter("ydj_series", " fisresultbrand =1 ");
            var list = AllSeries.Where(o => Convert.ToString(o["fname"]).IndexOf("慕思经典") > -1).Select(o => o["id"].ToString()).ToList();

            //慕思经典 商品授权清单中的门店
            var fstorelist = new List<string>();
            if (list != null)
            {
                foreach (var item in data)
                {
                    var fserieids = Convert.ToString(item["fserieid"]).Split(',');
                    foreach (var val in fserieids)
                    {
                        if (list.Contains(val))//慕思经典清单中 如果系列存在，将门店ID加入
                        {
                            var orgid = Convert.ToString(item["forgid"]);
                            if (!fstorelist.Contains(orgid))
                            {
                                fstorelist.Add(orgid);
                            }
                            break;
                        }
                    }
                }
            }
            return fstorelist;
        }

    }
}
