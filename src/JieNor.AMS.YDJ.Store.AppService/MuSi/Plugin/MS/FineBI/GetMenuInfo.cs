using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.MS.FineBI
{
    /// <summary>
    /// FineBI-通用获取请求地址
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("getmenuinfo")]
    public class GetMenuInfo : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var formParameter = this?.ParentPageSession?.FormParameter as FormParameter;

            // 刷新父页面的父页面
            var pageId = formParameter?.ParentPageId;

            var paramterSql = $"select fparameter from t_sys_menuitem  with(nolock) where fbillformid='{this.HtmlForm.Id.ToLower()}'";
            var paramter = string.Empty;
            using (var reader = this.Context.ExecuteReader(paramterSql, null))
            {
                while (reader.Read())
                {
                    paramter = reader["fparameter"] as string;
                }
            }
            var reportId = string.Empty;
            foreach (var item in paramter.Split(','))
            {
                var detail = item.Split(':');
                if (detail[0].Replace("\"", "").ToLower() == "reportid")
                {
                    reportId = detail[1].Replace("\"", "");
                }
            }

            //如果是总部的话
            //如果参数表中没有查询对应的
            var isNewTab = 0;
            if (this.Context.IsTopOrg)
            {
                isNewTab = OpenNewTabOrIFrame(this.Context);
            }
            else
            {
                var topCtx = this.Context.CreateTopOrgDBContext();

                isNewTab = OpenNewTabOrIFrame(topCtx);
            }

            //var linkurl = this.Context.GetAppConfig("ms.bi.linkUrl");
            //var token = JwtUtil.CreateSSOTOKEN(this.Context, this.Context.UserName);
            //this.Result.SrvData = SendDataByGET(url, token);
            var returnUrl = $"{this.Context.GetAppConfig("ms.bi.linkUrl")}webroot/decision/v10/entry/access/{reportId}?preview=true";

            var result = new Dictionary<string,object>();

            result.Add("openNewTabOrIFrame",isNewTab);
            result.Add("returnUrl",returnUrl);

            this.Result.SrvData = result;
            this.Result.IsSuccess = true;
        }

        #region 同步通过GET方式发送数据
        /// <summary>
        /// 通过GET方式发送数据
        /// </summary>
        /// <param name="Url">url</param>
        /// <param name="postDataStr">GET数据</param>
        /// <param name="cookie">GET容器</param>
        /// <returns></returns>
        public string SendDataByGET(string Url, string token)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(Url);

            var cookieContainer = new CookieContainer();
            var cookie = new Cookie("fine_auth_token", token);
            cookie.Domain = "139.9.100.47";
            cookieContainer.Add(cookie);
            request.CookieContainer = cookieContainer;
            request.Headers.Add("Authorization", "Bearer " + token);
            request.Method = "GET";
            request.ContentType = "text/html;charset=UTF-8";

            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            Stream myResponseStream = response.GetResponseStream();
            StreamReader myStreamReader = new StreamReader(myResponseStream, Encoding.GetEncoding("utf-8"));
            string retString = myStreamReader.ReadToEnd();
            myStreamReader.Close();
            myResponseStream.Close();

            return retString;
        }
        #endregion

        /// <summary>
        /// 是否是打开新标签或者是内嵌
        /// </summary>
        /// <param name="topCtx"></param>
        /// <returns></returns>
        private int OpenNewTabOrIFrame(UserContext topCtx)
        {
            var htmlFormId = this.HtmlForm.Id;
            //是否打开新页签，默认是0：不打开，1：打开
            var isNewTab = 0;
            //获取配置类
            var profile = topCtx.Container.GetService<ISystemProfile>();

            var Dy = profile.GetSystemParameter(topCtx, "bas_iframeornewtabparam");

            if (Dy == null)
            {
                isNewTab = 0;
                return isNewTab;
            }
            else
            {
                var entrys = Dy["fentry"] as DynamicObjectCollection;

                if (entrys == null || entrys.Count <= 0)
                {
                    isNewTab = 0;
                }
                else
                {
                    var findEntry = entrys.Where(x=>Convert.ToString(x["fbizobject"]).Equals(htmlFormId)).FirstOrDefault();
                    
                    if (findEntry == null)
                    {
                        isNewTab = 0;
                    }
                    else
                    {
                        //如果没有开启，那么也是用内嵌的方式弄
                        var isTrue = Convert.ToBoolean(findEntry["fistabopen"]);
                        if (!isTrue)
                        {
                            isNewTab = 0;
                        }
                        else
                        {
                            isNewTab = 1;
                        }
                        
                    }
                }

                return isNewTab;

            }
        }

    }
}
