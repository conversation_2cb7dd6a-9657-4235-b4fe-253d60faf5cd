using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report.StoreSalesConversionRate
{
    [InjectService]
    [FormId("rpt_storesalesconversionrate")]
    [OperationNo("QueryListReport")]
    public class QueryListReport: AbstractReportServicePlugIn
    {
        protected override void OnExecuteLogic()
        {
            base.OnExecuteLogic();

            setPageSessionValueFormYdjTarget();
        }

        /// <summary>
        /// 获取从销售目标小组件的参数
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        /// <param name="storeidArray"></param>
        private void setPageSessionValueFormYdjTarget()
        {
            var sender = this.GetQueryOrSimpleParam<string>("sender");
            if (false == sender.EqualsIgnoreCase("ydj_target"))
            {
                return;
            }

            var dtType = this.GetQueryOrSimpleParam<string>("dtType");
            var dataPermId = this.GetQueryOrSimpleParam<string>("dataPermId");

            this.SetPageSessionValue("sender", sender, this.HtmlForm.Id);
            this.SetPageSessionValue("dataPermId", dataPermId, this.HtmlForm.Id);
            this.SetPageSessionValue("dtType", dtType, this.HtmlForm.Id);
        }
    }
}
