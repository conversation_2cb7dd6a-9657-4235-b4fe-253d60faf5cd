using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Report.StorePerformanceExecute
{
    [InjectService]
    [FormId("rpt_storeperformanceexecute")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 店面绩效分析表
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {
            // CustomFilterObject 自定义过滤条件（从每个业务表单提供过滤子表单中收集而来的过滤条件）
            if (this.CustomFilterObject.IsNullOrEmpty())
            {
                throw new BusinessException("过滤条件不可以为空！");
            }
            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtDateFrom == null)
            {
                throw new BusinessException("过滤条件【销售日期从】必录！");
            }
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtDateTo == null)
            {
                throw new BusinessException("过滤条件【销售日期至】必录！");
            }
            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【结束日期】不能小于【开始日期】！");
            }
        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.CheckDataEnvironment();
            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            DateTime? dtStart;
            DateTime? dtEnd;
            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);
            StringBuilder sbSql = new StringBuilder();
            StringBuilder sbInsertSql = new StringBuilder();
            StringBuilder sbSelectSql = new StringBuilder();
            StringBuilder sbFromSql = new StringBuilder();
            StringBuilder sbWhereSql = new StringBuilder();

            //var whereSqlList = new List<string>();

            //销售部门 
            //定义一个字典类型的数组，存放多个销售部门,用','隔开
            //如果销售部门为空值，则不取
            var fdeptid = this.CustomFilterObject["fdeptid"] as string;
            var fdeptidArray = fdeptid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            //合同编号 模糊查询
            var fbillno = this.CustomFilterObject["fbillno"] as string;
            //定金单号 模糊查询
            var fsourcenumber = this.CustomFilterObject["fsourcenumber"] as string;

            var sqlParam = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
              new SqlParam("@fdeptid",System.Data.DbType.String,fdeptid),
              new SqlParam("@fbillno",System.Data.DbType.String,fbillno),
              new SqlParam("@fsourcenumber",System.Data.DbType.String,fsourcenumber),
              new SqlParam("@fdatefrom",System.Data.DbType.DateTime,dtStart),
              new SqlParam("@fdateto",System.Data.DbType.DateTime,dtEnd)
            };

            sbWhereSql.Append(" where o.fmainorgid=@fmainorgid");
            //如果日期不为空
            if (dtStart != null && dtEnd != null)
            {
                sbWhereSql.Append(" and o.facceptdate between @fdatefrom  and @fdateto ");
            }
            if (fdeptidArray != null && fdeptidArray.Length > 0)
            {
                sqlParam.AddRange(fdeptidArray.Select((x, i) => new SqlParam($"@fdeptid{i}", System.Data.DbType.String, x)));
                sbWhereSql.Append(string.Format(" and o.fdeptid in ({0}) ", string.Join(",", fdeptidArray.Select((x, i) => $"@fdeptid{i}"))));
            }
            if (fbillno != null)
            {
                sbWhereSql.Append(string.Format(" and o.fbillno like '%{0}%' ", @fbillno));
            }
            if (fsourcenumber != null)
            {
                sbWhereSql.Append(string.Format(" and o.fsourcenumber like '%{0}%' ", @fsourcenumber));
            }
            sbSelectSql.Append($@"select distinct ' ' fdatatype, o.fwithin fcostbillno,o.fbilltype fbilltype,
                                o.fdeptid fdeptid,dept.fname fdept, o.forderdate forderdate,o.facceptdate facceptdate, 
                                o.fbillno fbillno,o.fsourcenumber fsourcenumber, o.fcustomerid fcustomerid,o.fphone fphone, 
                                concat(concat(concat(penum.fenumitem,cenum.fenumitem),renum.fenumitem),o.faddress) as faddress, 
                                o.fchannel fchannel,(case when (c.faccountscale>0) then (c.faccountscale) else 0 end) fdistrate, 
                                o.fdealamount fdealamount,c.faccountamount ffaceamount, o.funreceived funreceived,
                                c.fsumsettleamount fsumcost,o.fsumamount fsumamount, c.faccountcostamount fsumactualcostamount,
                                o.fexpense fexpense, o.fstaffid fstaffid, 
                                fassistid=(stuff((select ',' + s.fname from t_ydj_orderduty od1 left join t_bd_staff s on s.fid=od1.fdutyid where od.fid = od1.fid and od1.fismain=0 for xml path('')),1,1,'')), 
                                o.fstylistid fstylistid, d.fapproveid fapproveid, o.fbrandid_e fbrandid,br.fname fbrand,
                                fee.fsumtaxamount fbrokerage,o.fdescription fdescription,o.fapproveid forderauditor "
             );
            sbFromSql.Append(@" 
                            from t_ydj_order o 
                            left join t_ydj_orderduty od on o.fid=od.fid 
                            left join t_ydj_costaccounting c on o.fbillno=c.fsourcenumber and c.fmainorgid=@fmainorgid 
                            left join t_ydj_designscheme d on o.fdesignscheme =d.fnumber and d.fmainorgid=@fmainorgid  --编码关联设计方案
                            --left join t_ydj_designscheme d on (o.fbillno=d.fsourcenumber or o.fsourcenumber=d.fsourcenumber) and d.fmainorgid=@fmainorgid 
                            left join t_bd_department dept on dept.fid=o.fdeptid 
                            left join t_Ydj_Brand br on br.fid=o.fbrandid_e 
                            left join v_bd_enum penum on penum.fid=o.fprovince 
                            left join v_bd_enum cenum on cenum.fid=o.fcity 
                            left join v_bd_enum renum on renum.fid=o.fregion 
                            left join t_ste_registfee fee on fee.fsourcetype='ydj_order' and fee.fsourcenumber=o.fbillno and fee.fmainorgid=o.fmainorgid and fee.fcancelstatus='0' and fee.fstatus='E' "
            );
            sbInsertSql.Append("/*dialect*/");
            sbInsertSql.Append($@"insert into {this.DataSourceTableName}(fid,fjnidentityid,fdatatype,
                                fcostbillno,fbilltype,fdeptid,fdept,forderdate,facceptdate,
                                fbillno,fsourcenumber,fcustomerid,fphone,faddress,fchannel,fdistrate,fdealamount,
                                ffaceamount, funreceived,fsumcost,fsumamount,fsumactualcostamount,fexpense,fstaffid,fassistid,
                                fstylistid,fapproveid,fbrandid,fbrand,fbrokerage,fdescription,forderauditor)"
             );
            sbSql.Append($@"select newid() fid, 
                            row_number() over(order by fdeptid asc,fdatatype asc,fbrandid asc) as fjnidentityid,
                            fdatatype,(case when(fcostbillno is null) then '' else fcostbillno end) as fcostbillno,
                            (case when(fbilltype is null) then '' else fbilltype end) as fbilltype,
                            fdeptid,fdept,forderdate,facceptdate,fbillno,fsourcenumber,fcustomerid,fphone,
                            (case when(faddress is null) then '' else faddress end) as faddress, 
                            (case when(fchannel is null) then '' else fchannel end) as fchannel, 
                            (case when(fdistrate is null) then 0 else fdistrate end) as fdistrate, 
                            (case when(fdealamount is null) then 0 else fdealamount end) as fdealamount, 
                            (case when(ffaceamount is null) then 0 else ffaceamount end) as ffaceamount, 
                            (case when(funreceived is null) then 0 else funreceived end) as funreceived, 
                            (case when(fsumcost is null) then 0 else fsumcost end) as fsumcost, 
                            (case when(fsumamount is null) then 0 else fsumamount end) as fsumamount, 
                            (case when(fsumactualcostamount is null) then 0 else fsumactualcostamount end) as fsumactualcostamount, 
                            (case when(fexpense is null) then 0 else fexpense end) as fexpense, fstaffid,
                            (case when(fassistid is null) then '' else fassistid end) as fassistid,
                            (case when(fstylistid is null) then '' else fstylistid end) as fstylistid, 
                            (case when(fapproveid is null) then '' else fapproveid end) as fapproveid, 
                            (case when(fbrandid is null) then '' else fbrandid end) as fbrandid, 
                            (case when(fbrand is null) then '' else fbrand end) as fbrand, 
                            (case when(fbrokerage is null) then 0 else fbrokerage end) as fbrokerage,
                            fdescription,isnull(forderauditor,'') as forderauditor 
                            from ( {sbSelectSql.ToString()}{sbFromSql.ToString()}{sbWhereSql.ToString()} 
                            union all 
                            select '品牌总计' fdatatype, '','','','', cast(nullif('','') as datetime) forderdate,
                            cast(nullif('','') as datetime) facceptdate, '','', '','', '', '',
                            (case when (sum(o.ffaceamount)>0) then (sum(o.fdealamount)/sum(o.ffaceamount)) else 0 end) fdistrate, 
                            sum(o.fdealamount) fdealamount,sum(o.ffaceamount) ffaceamount, 
                            sum(o.funreceived) funreceived,sum(o.fsumcost) fsumcost,sum(o.fsumamount) fsumamount, 
                            sum(o.fsumactualcostamount) fsumactualcostamount,sum(o.fexpense) fexpense, '', '','','', 
                            o.fbrandid fbrandid,(case when(o.fbrand is null) then '空品牌(总计)' else o.fbrand+'(总计)' end) fbrand,
                            sum(o.fbrokerage) fbrokerage,'','' 
                            from( {sbSelectSql.ToString()}{sbFromSql.ToString()}{sbWhereSql.ToString()}) o 
                            group by o.fbrandid,o.fbrand 
                            union all 
                            select '部门汇总' fdatatype, '','','', '部门汇总' as fdept, 
                            cast(nullif('','') as datetime) forderdate,
                            cast(nullif('','') as datetime) facceptdate, '','', '','', '', '',
                            (case when (sum(o1.ffaceamount)>0) then (sum(o1.fdealamount)/sum(o1.ffaceamount)) else 0 end) fdistrate, 
                            sum(o1.fdealamount) fdealamount,sum(o1.ffaceamount) ffaceamount, 
                            sum(o1.funreceived) funreceived,sum(o1.fsumcost) fsumcost,sum(o1.fsumamount) fsumamount,
                            sum(o1.fsumactualcostamount) fsumactualcostamount,sum(o1.fexpense) fexpense, '', '','','', '','',
                            sum(o1.fbrokerage) fbrokerage,'','' 
                            from( {sbSelectSql.ToString()}{sbFromSql.ToString()}{sbWhereSql.ToString()} ) o1 
                            union all 
                            select '小计' fdatatype, '','', o.fdeptid fdeptid,o.fdept fdept, 
                            cast(nullif('','') as datetime) forderdate,cast(nullif('','') as datetime) facceptdate, '','', '','', '', '',
                            (case when (sum(o.ffaceamount)>0) then (sum(o.fdealamount)/sum(o.ffaceamount)) else 0 end) fdistrate, sum(o.fdealamount) fdealamount,sum(o.ffaceamount) ffaceamount, sum(o.funreceived) funreceived,
                            sum(o.fsumcost) fsumcost,sum(o.fsumamount) fsumamount, 
                            sum(o.fsumactualcostamount) fsumactualcostamount,
                            sum(o.fexpense) fexpense, '', '','','', o.fbrandid fbrandid,
                            (case when(o.fbrand is null) then '空品牌(小计)' else o.fbrand+'(小计)' end) fbrand,
                            sum(o.fbrokerage) fbrokerage,'','' 
                            from( {sbSelectSql.ToString()}{sbFromSql.ToString()}{sbWhereSql.ToString()} ) o 
                            group by o.fdeptid,o.fdept,o.fbrand,o.fbrandid 
                            union all 
                            select '总计' fdatatype, '','', o.fdeptid fdeptid,
                            (o.fdept+'(总计)') fdept, 
                            cast(nullif('','') as datetime) forderdate,
                            cast(nullif('','') as datetime) facceptdate, '','', '','', '', '',
                            (case when (sum(o.ffaceamount)>0) then (sum(o.fdealamount)/sum(o.ffaceamount)) else 0 end) fdistrate, 
                            sum(o.fdealamount) fdealamount,sum(o.ffaceamount) ffaceamount, 
                            sum(o.funreceived) funreceived,sum(o.fsumcost) fsumcost,sum(o.fsumamount) fsumamount, 
                            sum(o.fsumactualcostamount) fsumactualcostamount,
                            sum(o.fexpense) fexpense, '', '','','', '','',
                            sum(o.fbrokerage) fbrokerage,'','' 
                            from ( {sbSelectSql.ToString()}{sbFromSql.ToString()}{sbWhereSql.ToString()} ) o 
                            group by o.fdeptid, o.fdept ) t1 "
            );
            var strSql = $@"{sbInsertSql.ToString()}{sbSql.ToString()}";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.Execute(this.Context, strSql, sqlParam);
        }
    }
}
