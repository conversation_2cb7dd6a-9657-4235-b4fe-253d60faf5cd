using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.PdaVersion
{
    /// <summary>
   /// PDA版本管理：保存
   /// </summary>
    [InjectService]
    [FormId("pda_version")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((n, o) =>
            {
                return !n["fversionpackage"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("请上传版本包"));
        }
        /// <summary>
        /// 操作
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            foreach (var dataEntity in e.DataEntitys)
            {

                string fversionpackage_txt = dataEntity["fversionpackage_txt"].ToString();
                var arr = fversionpackage_txt.Split('-');
                int fversionnumber = 0;
                if (arr.Length == 4)
                {
                    if (int.TryParse(arr[2], out fversionnumber))
                    {
                        dataEntity["fversionnumber"] = fversionnumber;
                        dataEntity["fversionnumbershow"] = arr[3].Replace(".apk", "");

                    }
                    else { throw new BusinessException("版本号不规范，请检查是否上传正确的版本包"); }
                }
                else
                {
                    throw new BusinessException("版本号不规范，请检查是否上传正确的版本包");
                }
            }

        }
    }
}
