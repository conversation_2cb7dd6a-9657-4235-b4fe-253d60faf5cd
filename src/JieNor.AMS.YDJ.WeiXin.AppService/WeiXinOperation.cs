using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.User;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.Entities;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.WeiXin.AppService
{
    public class WeiXinOperation : ServiceStack.Service
    {
        /// <summary>
        /// 新关注用户添加到数据库
        /// </summary>
        /// <param name="accessToken"></param>
        /// <param name="openId"></param>
        /// <param name="shareOpenId"></param>
        public void AddUserToBackStage(string accessToken, string openId, string shareOpenId = null)
        {
            var container = this.TryResolve<IServiceContainer>().BeginNewLifetimeScope();
            UserInfoJson u = UserApi.Info(accessToken, openId);
            var wxInvoker = container.GetService<IHttpMessageInvoker>();
            var resp = wxInvoker.Invoke(new CallerContext()
            {
                UserId = "ydj",
                UserName = openId,
                Product = "wx"
            }, new CommonBillDTO()
            {
                FormId = "wx_micromember",
                OperationNo = "Save",
                BillData = "[{\"id\":\"" + u.openid + "\",\"fopenid\":\"" + u.openid + "\",\"fheadimgurl\":\"" + u.headimgurl + "\",\"fcountry\":\"" + u.country + "\",\"fprovince\":\"" + u.province + "\",\"flanguage\":\"" + u.language + "\",\"fcity\":\"" + u.city + "\",\"fsex\":\"sex" + u.sex + "\",\"fsubscribe_time\":\"" + WeiXinHelper.TimeStampToDateTime(u.subscribe_time) + "\",\"fnickname\":\"" + u.nickname + "\"}]",
                SimpleData = new Dictionary<string, string>() { { "openid", u.openid } },
            });
        }
        /// <summary>
        /// 关注消息推送
        /// </summary>
        /// <param name="requestMessage"></param>
        /// <returns></returns>
        public JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.Entities.ResponseMessageBase PushMsg(RequestMessageEvent_Subscribe requestMessage)
        {
            var container = this.TryResolve<IServiceContainer>().BeginNewLifetimeScope();
            var wxInvoker = container.GetService<IHttpMessageInvoker>();
            var resp = wxInvoker.Invoke(new CallerContext()
            {
                UserId = "ydj",
                UserName = requestMessage.FromUserName,
                Product = "wx"
            }, new CommonBillDTO()
            {
                FormId = "wx_micromember",
                OperationNo = "isBindPhone",
                SimpleData = new Dictionary<string, string>() { { "openid", requestMessage.FromUserName } },
            });
            bool b = (resp as DynamicDTOResponse).OperationResult.IsSuccess;
            var responseMessage = ResponseMessageBase.CreateFromRequestMessage<ResponseMessageText>(requestMessage);
            if (b)
            {
                responseMessage.Content = "您好，欢迎关注易到家。";
            }
            else
            {
                var url = OAuthApi.GetAuthorizeUrl(WebConfig.GetServerappId, WebConfig.WebUrl + "/views/ydj/mobile/WeChat/binding.html", "", OAuthScope.snsapi_base);
                responseMessage.Content = "您好，欢迎关注易到家。\r\n为了更好地为您提供服务，请绑定手机号，点击以下链接完成绑定操作\r\n<a href=\"" + url + "\">手机绑定</a>";
            }
            return responseMessage;

        }

        public JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.Entities.ResponseMessageBase Evaluation(RequestMessageEvent_Subscribe requestMessage, string Id)
        {
            var responseMessage = ResponseMessageBase.CreateFromRequestMessage<ResponseMessageText>(requestMessage);
            responseMessage.Content = Evaluation(Id);
            return responseMessage;
        }



        public string Evaluation(string Eid)
        {
            string[] Arr = Eid.Split('|');
            //string url = OAuthApi.GetAuthorizeUrl(WebConfig.GetServerappId, WebConfig.WebUrl + "/views/ydj/mobile/WeChat/evaluation.html?id=" + Arr[0]+"&companyid="+Arr[2]+"&productid="+Arr[3], "", OAuthScope.snsapi_base);
            string url = OAuthApi.GetAuthorizeUrl(WebConfig.GetServerappId, WebConfig.WebUrl + "/views/ydj/mobile/WeChat/evaluation.html?id=" + Arr[0], "", OAuthScope.snsapi_base);
            return "您好，很高兴为您服务! \r\n单号:" + Arr[1] + "\r\n请对我们安装师傅的服务进行\r\n<a href=\"" + url + "\">评价</a>";
        }
    }
}
