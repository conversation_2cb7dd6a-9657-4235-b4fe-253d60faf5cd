using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API
{
    /// <summary>
    /// 企业微信用户信息模型
    /// </summary>
    public class QyWxUserInfoModel
    {
        /// <summary>
        /// 远程用户ID，AC站点用户ID
        /// </summary>
        public string RemoteUserId { get; set; }

        /// <summary>
        /// 本地用户Id，如果是直接通过 UserHelper.GetQyWxInfoByUserName 方法调用获取时，该 UserId 暂时为空。
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 企业微信客户标识：微信用户在使用企业微信小程序时所绑定的企业编码
        /// </summary>
        public string QyWxCustomerId { get; set; }

        /// <summary>
        /// 企业微信业务企业标识：微信用户在使用企业微信小程序时所绑定的业务系统企业标识，比如：麦浩企业标识
        /// </summary>
        public string QyWxCompanyId { get; set; }

        /// <summary>
        /// 企业微信用户标识：微信用户在企业微信中的员工标识
        /// </summary>
        public string QyWxUserId { get; set; }

        /// <summary>
        /// 企业微信企业标识：微信用户在企业微信中的企业标识
        /// </summary>
        public string QyWxCorpId { get; set; }

        /// <summary>
        /// 企业微信第三方服务商用户标识：微信用户在第三方服务商中的用户标识
        /// </summary>
        public string QyWxOpenUserId { get; set; }
    }
}