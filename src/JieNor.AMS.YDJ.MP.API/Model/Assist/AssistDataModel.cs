using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 辅助资料数据模型
    /// </summary>
    public class AssistDataModel : ComboDataModel
    {
        /// <summary>
        /// 是否禁用
        /// </summary>
        public bool Disable { get; set; }

        public override string ToString()
        {
            return $"{this.Id} {this.Name} {(this.Disable ? "已禁用" : "未禁用")}";
        }
    }
}