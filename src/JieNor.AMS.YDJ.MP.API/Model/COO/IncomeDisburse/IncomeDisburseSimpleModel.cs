using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class IncomeDisburseSimpleModel : BaseDataModel
    {
    

        [IgnoreDataMember]
        public new string Name { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 支付方式
        /// </summary>
        public ComboDataModel Way { get; set; }

        /// <summary>
        /// 支付日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 业务状态
        /// </summary>
        public ComboDataModel BizStatus { get; set; }

        /// <summary>
        /// 核销状态
        /// </summary>
        public ComboDataModel VerificStatus { get; set; }
        /// <summary>
        /// 用途
        /// </summary>
        public ComboDataModel Purpose { get; set; }

        /// <summary>
        /// 数据状态
        /// </summary>
        public ComboDataModel Status { get; set; } = new ComboDataModel();
    }
}
