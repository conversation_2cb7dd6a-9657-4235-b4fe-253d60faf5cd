using JieNor.AMS.YDJ.MP.API.DTO.COO.Receipt;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Model.COO.Receipt;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.COO.Receipt
{
    /// <summary>
    /// 微信小程序：收款单编辑接口
    /// </summary>
    public class ReceiptSaveController : BaseController
    {
        //等在线付款和麦浩合并付款功能完成后才能编写此接口
        public object Any(ReceiptSaveDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            //检查参数
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 ID 不能为空！";
                resp.Success = false;
                resp.Data = null;
                return resp;
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "coo_incomedisburse",
                    OperationNo = "save",
                    BillData = BuildBillData(dto),
                    Id = dto.Id
                });
            var result = response?.OperationResult;
         
            resp = result.ToResponseModel<BaseDataModel>();

            return resp;
        }

        private string BuildBillData(ReceiptSaveDTO dto)
        {
            var data =
                new Dictionary<string, object>
                {
                    { "id", dto.Id },
                    { "famount", dto.Amount },
                    { "fdate", dto.Date },
                    { "fway", dto.Way },
                    { "fmybankid", dto.Mybankid },
                    { "paymentdesc", dto.PaymentDesc },
                    { "fdescription", dto.Description },
                    { "fdeptid", dto.Deptid },
                    { "fcontactunitid", dto.Unitid },
                    { "fcontactunittype", dto.UnitType },
                    //40936 客户充值默认携带业务员，允许业务员修改-后端开发
                    { "fstaffid", dto.Staffid },
                    //收款小票号
                    { "freceiptno",dto.ReceiptNo },
                    //客户收款账号
                    { "fcusacount",dto.cusacount  }
                };

            if (dto.Certificates != null && dto.Certificates.Any())
            {
                data.Add("fimage", string.Join(",", dto.Certificates.Select(s => s.Id)));
                data.Add("fimage_txt", string.Join(",", dto.Certificates.Select(s => s.Name)));
            }

            #region 销售员
            var joinStaffs = new List<Dictionary<string, object>>();
            foreach (var js in dto.JoinStaffs)
            {
                joinStaffs.Add(new Dictionary<string, object>
                {
                    { "id", js.Id },
                    { "fismain", js.IsMain },
                    { "fdutyid", js.Duty?.Id },
                    { "fdeptid_ed", js.Dept?.Id },
                    { "fratio", js.Ratio },
                    { "famount_ed", js.Amount },
                    { "fdeptperfratio", js.DeptPerfRatio }, 
                    { "fdescription_ed", js.Description }
                });
            }
            data.Add("fdutyentry", joinStaffs);
            #endregion

            var billData = (new List<Dictionary<string, object>> { data }).ToJson();

            return billData;
        }

    }
}
