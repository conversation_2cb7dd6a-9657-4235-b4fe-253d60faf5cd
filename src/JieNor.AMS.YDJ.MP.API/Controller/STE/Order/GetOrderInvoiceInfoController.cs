using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.DTO.STE.Order;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    public class GetOrderInvoiceInfoController:BaseOrderController
    {
        public object Any(GetOrderInvoiceInfoDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<Dictionary<string,object>>();

            if (dto.CustomerId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 customerId 不能为空！";
                return resp;
            }

            var customerDy = this.Context.LoadBizDataById("ydj_customer",dto.CustomerId);

            if (customerDy == null)
            {
                resp.Success = false;
                resp.Message = $"参数 customerId 对应的客户不存在或者已删除，请检查";
                return resp;
            }

            SetResponseData(dto, customerDy, resp);

            return resp;
        }

        private void SetResponseData(GetOrderInvoiceInfoDTO dto,DynamicObject customerDy,BaseResponse<Dictionary<string,object>> resp)
        {
            var invoiceEntrys = customerDy["finvoiceentry"] as DynamicObjectCollection;
            
            var dataInvoiceEntries = new List<InvoiceEntry>();

            //var invoiceEntry = new InvoiceEntry();

            if (invoiceEntrys != null && invoiceEntrys.Any())
            {
                var findDefaultEntry = invoiceEntrys.FirstOrDefault(x=>Convert.ToBoolean(Convert.ToInt32(x["finvoicedefault"])));

                foreach (var tempEntry in invoiceEntrys)
                {
                    var entry = new InvoiceEntry();

                    SetInvoiceInfo(entry, tempEntry);
                    
                    dataInvoiceEntries.Add(entry);
                }
                
                /*if (findDefaultEntry != null)
                {

                    SetInvoiceInfo(invoiceEntry, findDefaultEntry);
                }
                else
                {
                    var firstEntry = invoiceEntrys[0];
                    SetInvoiceInfo(invoiceEntry,firstEntry);
                }*/
                var resultDic = new Dictionary<string,object>();
                resultDic.Add("invoiceInfos",dataInvoiceEntries);
                resp.Success = true;
                resp.Message = "取数成功！";
                resp.Data = resultDic;
            }
            else
            {
                var resultDic = new Dictionary<string,object>();
                resultDic.Add("invoiceInfos", new List<InvoiceEntry>());
                resp.Success = true;
                resp.Message = "请求成功，但客户没有设置开票信息";
                resp.Data = resultDic;
            }
            
        }

        
        /// <summary>
        /// 设置开票信息
        /// </summary>
        /// <param name="invoiceEntry"></param>
        /// <param name="findDefaultEntry"></param>
        public void SetInvoiceInfo(InvoiceEntry invoiceEntry,DynamicObject findDefaultEntry)
        {
            invoiceEntry.Id = Convert.ToString(findDefaultEntry["id"]);
            invoiceEntry.IsDefault = Convert.ToBoolean(Convert.ToInt32(findDefaultEntry["finvoicedefault"]));
            invoiceEntry.InvoiceType = JNConvert.ToStringAndTrim(findDefaultEntry["finvoicetype"]);
            invoiceEntry.BuyerFullName = JNConvert.ToStringAndTrim(findDefaultEntry["fbuyerfullname"]);
            invoiceEntry.TaxpayerIdentify = JNConvert.ToStringAndTrim(findDefaultEntry["ftaxpayeridentify"]);
            invoiceEntry.InvoiceEmail = JNConvert.ToStringAndTrim(findDefaultEntry["finvoiceemail"]);
            invoiceEntry.InvoiceAddress = JNConvert.ToStringAndTrim(findDefaultEntry["finvoiceaddress"]);
            invoiceEntry.InvoicePhone = JNConvert.ToStringAndTrim(findDefaultEntry["finvoicephone"]);
            invoiceEntry.DepositBankName = JNConvert.ToStringAndTrim(findDefaultEntry["fdepositbankname"]);
            invoiceEntry.BankAccount = JNConvert.ToStringAndTrim(findDefaultEntry["fbankaccount"]);
        }
    }
}