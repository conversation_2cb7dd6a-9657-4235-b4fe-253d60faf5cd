using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 合同单提交接口
    /// </summary>
    [Api("合同单提交接口")]
    [Route("/mpapi/order/submit")]
    [Authenticate]
    [CheckBillLockFilter("ydj_order", "submit")]
    public class OrderSubmitDTO : CheckBillLockDTO
    {
    }
}