using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.IM.Notice
{
    /// <summary>
    //企业微信小程序- 我的企业公告保存已读人员接口
    /// </summary>
    [Api(" 我的企业公告保存已读人员接口")]
    [Route("/mpapi/mynotice/setreader")]
    [Authenticate]
    public class MyNoticeSetReaderDTO
    {

        /// <summary>
        ///  公告 Id
        /// </summary>
        public string Id { get; set; }
    }
}
