using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SER.Service
{
    /// <summary>
    /// 服务单/售后反馈单/回访单根据经销商id获取授权城市接口
    /// </summary>
    [Api("服务单/售后反馈单/回访单根据经销商id获取授权城市接口")]
    [Route("/mpapi/service/getauthcitylist")]
    [Authenticate]
    public class SerGetAuthCityDTO
    {
        /// <summary>
        /// 经销商id
        /// </summary>
        public string AgentId { get; set; }
    }
}
