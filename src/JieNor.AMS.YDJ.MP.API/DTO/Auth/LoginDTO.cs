using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Config;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 登录接口
    /// </summary>
    [Api("登录接口")]
    [Route("/mpapi/login")]
    public class LoginDTO : BaseDTO
    {
        /// <summary>
        /// 客户编码
        /// </summary>
        public string CustomerNo { get; set; } = ConfigExtentions.GetDefaultCustomer().Number;

        /// <summary>
        /// 业务企业标识，比如：麦浩企业标识
        /// </summary>
        public string CompanyId { get; set; }

        /// <summary>
        /// 登录方式：企业微信 或 微信，默认为企业微信
        /// </summary>
        public string Way { get; set; } = "qywx";

        /// <summary>
        /// 微信用户登录凭证
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }
    }
}