using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 意向单提交接口
    /// </summary>
    [Api("意向单提交接口")]
    [Route("/mpapi/saleintention/submit")]
    [Authenticate]
    public class SaleIntentionSubmitDTO : BaseDTO
    {
        public string Id { get; set; }
    }
}