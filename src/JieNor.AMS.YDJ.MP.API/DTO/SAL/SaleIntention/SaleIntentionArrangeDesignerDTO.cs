using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 销售意向指派设计师接口
    /// </summary>
    [Api("销售意向指派设计师接口")]
    [Route("/mpapi/saleintention/arrange/designer")]
    [Authenticate]
    public class SaleIntentionArrangeDesignerDTO : BaseDetailDTO
    {
        /// <summary>
        /// 设计师Id
        /// </summary>
        public string DesignerId { get; set; }
    }
}