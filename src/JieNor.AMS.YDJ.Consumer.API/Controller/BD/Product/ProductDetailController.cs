using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.Utils;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.BD.Product
{
    /// <summary>
    /// 消费者小程序：商品详情取数接口
    /// </summary>
    public class ProductDetailController : BaseNotAuthController
    {
        /// <summary>
        /// 商品表单模型
        /// </summary>
        protected HtmlForm ProductForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ProductDetailModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //根据唯一标识获取数据
            this.ProductForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var productObj = this.ProductForm.GetBizDataById(this.Context, dto.Id, true);

            //设置响应数据包
            this.SetResponseData(dto, productObj, resp);

            return resp;
        }

        /// <summary>
        /// 设置响应数据包
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="result"></param>
        /// <param name="resp"></param>
        private void SetResponseData(ProductDetailDTO dto, DynamicObject productObj, BaseResponse<ProductDetailModel> resp)
        {
            if (productObj == null)
            {
                resp.Message = "商品不存在或已被删除！";
                resp.Success = false;
                return;
            }

            resp.Message = "取数成功！";
            resp.Success = true;

            var productId = Convert.ToString(productObj["id"]);
            var brandObj = productObj["fbrandid_ref"] as DynamicObject;
            var spaceObj = productObj["fspace_ref"] as DynamicObject;
            var styleObj = productObj["fstyle_ref"] as DynamicObject;
            var salUnitObj = productObj["fsalunitid_ref"] as DynamicObject;
            var seriesObj = productObj["fseriesid_ref"] as DynamicObject;

            //简单下拉框模型数据
            var deliveryModeId = Convert.ToString(productObj["fdeliverymode"]).Trim();
            var deliveryModeName = this.ProductForm.GetSimpleSelectItemText(productObj, "fdeliverymode");

            //是否预设辅助属性（现在叫允许选配）
            var isPresetProp = Convert.ToBoolean(productObj["fispresetprop"]);
            var defAuxPropValId = Convert.ToString(productObj["fdefattrinfo"]);

            //如果前端有传递辅助属性组合值ID，则返回对应的辅助属性值选中状态
            if (!dto.AuxPropValId.IsNullOrEmptyOrWhiteSpace())
            {
                defAuxPropValId = dto.AuxPropValId;
            }

            resp.Data.Id = productId;
            resp.Data.Number = Convert.ToString(productObj["fnumber"]);
            resp.Data.Name = Convert.ToString(productObj["fname"]);
            resp.Data.ImageUrl = Convert.ToString(productObj["fimage"]).GetSignedFileUrl();
            resp.Data.ImageList = ProductUtil.GetImages(this.Context, productObj, defAuxPropValId);
            resp.Data.SalPrice = Convert.ToDecimal(productObj["fsalprice"]);
            resp.Data.GuidePrice = Convert.ToDecimal(productObj["fguideprice"]);
            resp.Data.Brand = Convert.ToString(brandObj?["fname"]);
            resp.Data.Space = Convert.ToString(spaceObj?["fenumitem"]);
            resp.Data.Style = Convert.ToString(styleObj?["fenumitem"]);
            resp.Data.Specifica = Convert.ToString(productObj["fspecifica"]);
            resp.Data.Size = Convert.ToString(productObj["fsize"]);
            resp.Data.Suttle = Convert.ToDecimal(productObj["fsuttle"]);
            resp.Data.GrossLoad = Convert.ToDecimal(productObj["fgrossload"]);
            resp.Data.Volume = Convert.ToDecimal(productObj["fvolume"]);
            resp.Data.IsCustom = Convert.ToBoolean(productObj["fcustom"]);
            resp.Data.IsPresetProp = isPresetProp;
            resp.Data.IsFixProp = Convert.ToBoolean(productObj["fisfixprop"]);
            resp.Data.DeliveryMode = new ComboDataModel
            {
                Id = deliveryModeId,
                Name = deliveryModeName
            };
            resp.Data.SalUnit = new BaseDataSimpleModel(salUnitObj);
            resp.Data.Content = Convert.ToString(productObj["fcontent"]);
            resp.Data.SeriesId = Convert.ToString(productObj["fseriesid"]);
            if (seriesObj!=null)
            {
                resp.Data.Series = Convert.ToString(seriesObj["fname"]);
            }
            //如果商品勾选了预置辅助属性，则获取对应的辅助属性列表
            if (isPresetProp)
            {
                var auxPropInfo = ProductUtil.GetAuxPropInfo(this.Context, productId, dto.AuxPropValId);
                resp.Data.AuxPropInfo = auxPropInfo;
            }

            //获取销售价，如果没有匹配到销售价目表销售价，则取商品销售价
            resp.Data.SalPrice = ProductUtil.GetLocalSalPrice(this.Context, productId, defAuxPropValId, resp.Data.SalPrice);

            //获取即时库存、在途量、预留量
            var realStockQty = ProductUtil.GetLocalRealStockQty(this.Context, productId, defAuxPropValId);
            var intransitQty = ProductUtil.GetIntransitQty(this.Context, productId, defAuxPropValId);
            var reserveQty = ProductUtil.GetReserveQty(this.Context, productId, defAuxPropValId);

            //可用量 = 库存量 + 在途量 - 预留量
            var usableQty = realStockQty + intransitQty - reserveQty;

            resp.Data.UsableStockQty = usableQty;
            resp.Data.RealStockQty = realStockQty;
        }
    }
}