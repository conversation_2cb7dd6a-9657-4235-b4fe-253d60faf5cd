using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.Framework;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Consumer.API.Utils;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：微信解绑接口
    /// </summary>
    public class UnbindUserController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(UnbindUserDTO dto)
        {
            this.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            var customerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");

            var customer = customerForm.GetBizDataById(this.Context, this.Context.UserId, true);
            if (customer == null)
            {
                resp.Success = false;
                resp.Message = "解析失败，当前用户不存在，请检查！";
                return resp;
            }

            if (customer["fwxopenid"].IsNullOrEmptyOrWhiteSpace() == false)
            {
                customer["fwxopenid"] = string.Empty;

                var dt = customerForm.GetDynamicObjectType(this.Context);
                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, dt);
                dm.Save(customer);
            }

            resp.Success = true;
            resp.Message = "解绑成功！";

            return resp;
        }

    }
}