using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Consumer.API.Utils
{
    /// <summary>
    /// 表单模型扩展类
    /// </summary>
    public static class HtmlFormExtentions
    {
        /// <summary>
        /// 获取（辅助资料、简单下拉框、单据类型）字段数据源
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="fieldKey">字段标识，多个字段标识之间用逗号“,”分隔</param>
        /// <returns>数据源</returns>
        public static Dictionary<string, List<Dictionary<string, object>>> GetComboDataSource(this HtmlForm formMeta,
            UserContext userCtx,
            string fieldKey)
        {
            var comboDataSource = new Dictionary<string, List<Dictionary<string, object>>>();

            var fieldKeys = fieldKey?.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)?.Distinct();
            if (fieldKeys == null || !fieldKeys.Any())
            {
                return comboDataSource;
            }

            //辅助资料字段
            var assistFieldKeys = new List<string>();
            var simpleSelectFieldKeys = new List<string>();
            var billTypeFieldKey = string.Empty;

            foreach (var _fieldKey in fieldKeys)
            {
                var field = formMeta.GetField(_fieldKey);
                if (field == null) continue;

                //辅助资料字段
                if (field is HtmlComboField)
                {
                    assistFieldKeys.Add(field.Id);
                }
                //简单下拉框字段
                else if (field is HtmlSimpleSelectField)
                {
                    simpleSelectFieldKeys.Add(field.Id);
                }
                //单据类型字段
                else if (field is HtmlBillTypeField)
                {
                    billTypeFieldKey = field.Id;
                }
            }

            //辅助资料数据源
            if (assistFieldKeys.Any())
            {
                var assistDs = formMeta.GetAssistDataSource(userCtx, string.Join(",", assistFieldKeys));
                if (assistDs.Count > 0)
                {
                    foreach (var item in assistDs)
                    {
                        comboDataSource[item.Key] = item.Value;
                    }
                }
            }

            //简单下拉框数据源
            if (simpleSelectFieldKeys.Any())
            {
                var assistDs = formMeta.GetSimpleSelectDataSource(userCtx, string.Join(",", simpleSelectFieldKeys));
                if (assistDs.Count > 0)
                {
                    foreach (var item in assistDs)
                    {
                        comboDataSource[item.Key] = item.Value;
                    }
                }
            }

            //单据类型数据源
            if (!billTypeFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                var billTypeDs = formMeta.GetBillTypeDataSource(userCtx);
                comboDataSource[billTypeFieldKey] = billTypeDs;
            }

            return comboDataSource;
        }

        /// <summary>
        /// 获取辅助资料字段数据源
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="fieldKey">字段标识，多个字段标识之间用逗号“,”分隔</param>
        /// <returns>数据源</returns>
        public static Dictionary<string, List<Dictionary<string, object>>> GetAssistDataSource(this HtmlForm formMeta,
            UserContext userCtx,
            string fieldKey)
        {
            var comboDataSource = new Dictionary<string, List<Dictionary<string, object>>>();

            var fieldKeys = fieldKey?.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)?.Distinct();
            if (fieldKeys == null || !fieldKeys.Any())
            {
                return comboDataSource;
            }

            //调用平台接口
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var response = gateway.InvokeLocal<CommonBillDTOResponse>(userCtx,
                new CommonBillDTO()
                {
                    FormId = formMeta.Id,
                    OperationNo = "querycombobatch",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "fieldKey", fieldKey }
                    }
                });
            var result = response?.OperationResult;

            var srvDataJson = result?.SrvData?.ToJson();
            if (!srvDataJson.IsNullOrEmptyOrWhiteSpace())
            {
                var source = JsonConvert.DeserializeObject<Dictionary<string, List<Dictionary<string, object>>>>(srvDataJson);

                //移除多余的辅助资料编码
                foreach (var fieldItem in source)
                {
                    foreach (var item in fieldItem.Value)
                    {
                        item.Remove("number");
                    }

                    // 排除禁用状态选项
                    comboDataSource.Add(fieldItem.Key, fieldItem.Value.Where(s => !(bool)s["disable"]).ToList());
                }
            }

            return comboDataSource;
        }

        /// <summary>
        /// 获取多类别基础资料类型字段数据源
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="fieldKey">字段标识，多个字段标识之间用逗号“,”分隔</param>
        /// <returns>数据源</returns>
        public static Dictionary<string, List<Dictionary<string, object>>> GetMulClassTypeDataSource(this HtmlForm formMeta,
            UserContext userCtx,
            string fieldKey)
        {
            return formMeta.GetSimpleSelectDataSource(userCtx, fieldKey);
        }

        /// <summary>
        /// 获取简单下拉框字段数据源
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="fieldKey">字段标识，多个字段标识之间用逗号“,”分隔</param>
        /// <returns>数据源</returns>
        public static Dictionary<string, List<Dictionary<string, object>>> GetSimpleSelectDataSource(this HtmlForm formMeta,
            UserContext userCtx,
            string fieldKey)
        {
            var comboDataSource = new Dictionary<string, List<Dictionary<string, object>>>();

            var fieldKeys = fieldKey?.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)?.Distinct();
            if (fieldKeys == null || !fieldKeys.Any())
            {
                return comboDataSource;
            }

            try
            {
                //从表单字段模型中解析数据源
                foreach (var _fieldKey in fieldKeys)
                {
                    var field = formMeta?.GetField(_fieldKey) as HtmlSimpleSelectField;
                    var items = field?.GetDropdownItems();
                    if (items == null || items.Count < 1) continue;

                    var list = new List<Dictionary<string, object>>();
                    foreach (var item in items)
                    {
                        list.Add(new Dictionary<string, object>
                        {
                            { "id", item.Key },
                            { "name", item.Value }
                        });
                    }
                    comboDataSource[_fieldKey] = list;
                }
            }
            catch { }

            return comboDataSource;
        }

        /// <summary>
        /// 获取单据类型字段数据源
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <returns>数据源</returns>
        public static List<Dictionary<string, object>> GetBillTypeDataSource(this HtmlForm formMeta,
            UserContext userCtx)
        {
            var billTypeFieldKey = string.Empty;
            return formMeta.GetBillTypeDataSource(userCtx, out billTypeFieldKey);
        }

        /// <summary>
        /// 获取单据类型字段数据源
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="billTypeFieldKey">单据类型字段标识</param>
        /// <returns>数据源</returns>
        public static List<Dictionary<string, object>> GetBillTypeDataSource(this HtmlForm formMeta,
            UserContext userCtx,
            out string billTypeFieldKey)
        {
            billTypeFieldKey = string.Empty;

            var comboDataSource = new List<Dictionary<string, object>>();

            var billTypeField = formMeta.GetBillTypeField();
            if (billTypeField == null) return comboDataSource;

            billTypeFieldKey = billTypeField.Id;

            //调用平台接口
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var response = gateway.InvokeLocal<CommonBillDTOResponse>(userCtx,
                new CommonBillDTO()
                {
                    FormId = formMeta.Id,
                    OperationNo = "querybilltype"
                });
            var result = response?.OperationResult;

            var srvDataDic = new Dictionary<string, List<Dictionary<string, object>>>();
            var srvDataJson = result?.SrvData?.ToJson();
            if (!srvDataJson.IsNullOrEmptyOrWhiteSpace())
            {
                srvDataDic = JsonConvert.DeserializeObject<Dictionary<string, List<Dictionary<string, object>>>>(srvDataJson);
            }

            comboDataSource = srvDataDic["data"];

            return comboDataSource;
        }

        /// <summary>
        /// 获取指定的简单下拉框字段数据项文本
        /// </summary>
        /// <param name="formMeta">简单下拉框字段所在的表单模型</param>
        /// <param name="fieldKey">简单下拉框字段标识</param>
        /// <param name="itemKey">简单下拉框字段数据项标识</param>
        /// <returns>数据项文本</returns>
        public static string GetSimpleSelectItemText(this HtmlForm formMeta, string fieldKey, string itemKey)
        {
            var itemText = "";
            var field = formMeta?.GetField(fieldKey) as HtmlSimpleSelectField;
            if (field == null) return itemText;
            var items = field?.GetDropdownItems();
            items?.TryGetValue(itemKey, out itemText);
            return itemText;
        }

        /// <summary>
        /// 获取指定的简单下拉框字段数据项文本
        /// </summary>
        /// <param name="formMeta">简单下拉框字段所在的表单模型</param>
        /// <param name="formMeta">简单下拉框字段所在的表单数据包</param>
        /// <param name="fieldKey">简单下拉框字段标识</param>
        /// <returns>数据项文本</returns>
        public static string GetSimpleSelectItemText(this HtmlForm formMeta, DynamicObject dataEntity, string fieldKey)
        {
            var itemText = "";
            var field = formMeta?.GetField(fieldKey) as HtmlSimpleSelectField;
            if (field == null) return itemText;
            var items = field?.GetDropdownItems();
            var itemKey = Convert.ToString(dataEntity[field.PropertyName]);
            items?.TryGetValue(itemKey, out itemText);
            return itemText;
        }

        /// <summary>
        /// 获取指定的简单下拉框字段数据源
        /// </summary>
        /// <param name="formMeta">简单下拉框字段所在的表单模型</param>
        /// <param name="fieldKey">简单下拉框字段标识</param>
        /// <returns>数据源</returns>
        public static Dictionary<string, string> GetSimpleSelectItems(this HtmlForm formMeta, string fieldKey)
        {
            var items = new Dictionary<string, string>();
            var field = formMeta?.GetField(fieldKey) as HtmlSimpleSelectField;
            if (field == null) return items;
            items = field?.GetDropdownItems();
            return items;
        }

        /// <summary>
        /// 获取指定表单的（省、市、区）字段组合值文本
        /// </summary>
        /// <param name="formMeta">省市区字段所在的表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="dataEntity">基础资料或业务单据的数据包</param>
        /// <param name="provinceFieldKey">省份字段标识</param>
        /// <param name="cityFieldKey">城市字段标识</param>
        /// <param name="regionFieldKey">区县字段标识</param>
        /// <returns>组合值文本</returns>
        public static string GetDistrictText(this HtmlForm formMeta,
            UserContext userCtx,
            DynamicObject dataEntity,
            string provinceFieldKey = "fprovince",
            string cityFieldKey = "fcity",
            string regionFieldKey = "fregion")
        {
            var provinceField = formMeta.GetField(provinceFieldKey) as HtmlComboField;
            var cityField = formMeta.GetField(cityFieldKey) as HtmlComboField;
            var regionField = formMeta.GetField(regionFieldKey) as HtmlComboField;
            if (dataEntity == null || provinceField == null || cityField == null || regionField == null)
            {
                return string.Empty;
            }

            var provinceObj = provinceField.RefDynamicProperty.GetValue<DynamicObject>(dataEntity);
            var cityObj = cityField.RefDynamicProperty.GetValue<DynamicObject>(dataEntity);
            var regionObj = regionField.RefDynamicProperty.GetValue<DynamicObject>(dataEntity);

            var province = Convert.ToString(provinceObj?["fenumitem"]);
            var city = Convert.ToString(cityObj?["fenumitem"]);
            var region = Convert.ToString(regionObj?["fenumitem"]);

            return $"{province}{city}{region}";
        }

        /// <summary>
        /// 获取指定表单的（省、市、区、详细地址）字段组合值文本
        /// </summary>
        /// <param name="formMeta">省市区字段所在的表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="dataEntity">基础资料或业务单据的数据包</param>
        /// <param name="provinceFieldKey">省份字段标识</param>
        /// <param name="cityFieldKey">城市字段标识</param>
        /// <param name="regionFieldKey">区县字段标识</param>
        /// <param name="addressFieldKey">详细地址字段标识</param>
        /// <returns>组合值文本</returns>
        public static string GetDistrictFullText(this HtmlForm formMeta,
            UserContext userCtx,
            DynamicObject dataEntity,
            string provinceFieldKey = "fprovince",
            string cityFieldKey = "fcity",
            string regionFieldKey = "fregion",
            string addressFieldKey = "faddress")
        {
            var district = formMeta.GetDistrictText(userCtx, dataEntity, provinceFieldKey, cityFieldKey, regionFieldKey);

            var address = "";
            var addressField = formMeta.GetField(addressFieldKey);
            if (addressField != null)
            {
                address = addressField.DynamicProperty.GetValue<string>(dataEntity);
            }

            return $"{district}{address}";
        }

        /// <summary>
        /// 获取指定表单的（省、市、区）字段组合值文本
        /// </summary>
        /// <param name="formMeta">省市区字段所在的表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="pkid">基础资料或业务单据的主键Id</param>
        /// <param name="provinceFieldKey">省份字段标识</param>
        /// <param name="cityFieldKey">城市字段标识</param>
        /// <param name="regionFieldKey">区县字段标识</param>
        /// <returns>组合值文本</returns>
        public static string GetDistrictText(this HtmlForm formMeta,
            UserContext userCtx,
            string pkid,
            string provinceFieldKey = "fprovince",
            string cityFieldKey = "fcity",
            string regionFieldKey = "fregion")
        {
            return formMeta.GetDistrictText(userCtx, pkid, provinceFieldKey, cityFieldKey, regionFieldKey, string.Empty);
        }

        /// <summary>
        /// 获取指定表单的（省、市、区、详细地址）字段组合值文本
        /// </summary>
        /// <param name="formMeta">省市区字段所在的表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="pkid">基础资料或业务单据的主键Id</param>
        /// <param name="provinceFieldKey">省份字段标识</param>
        /// <param name="cityFieldKey">城市字段标识</param>
        /// <param name="regionFieldKey">区县字段标识</param>
        /// <param name="addressFieldKey">详细地址字段标识</param>
        /// <returns>组合值文本</returns>
        public static string GetDistrictFullText(this HtmlForm formMeta,
            UserContext userCtx,
            string pkid,
            string provinceFieldKey = "fprovince",
            string cityFieldKey = "fcity",
            string regionFieldKey = "fregion",
            string addressFieldKey = "faddress")
        {
            return formMeta.GetDistrictText(userCtx, pkid, provinceFieldKey, cityFieldKey, regionFieldKey, addressFieldKey);
        }

        /// <summary>
        /// 获取指定表单的（省、市、区、详细地址）字段组合值文本
        /// </summary>
        /// <param name="formMeta">省市区字段所在的表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="pkid">基础资料或业务单据的主键Id</param>
        /// <param name="provinceFieldKey">省份字段标识</param>
        /// <param name="cityFieldKey">城市字段标识</param>
        /// <param name="regionFieldKey">区县字段标识</param>
        /// <param name="addressFieldKey">详细地址字段标识</param>
        /// <returns>组合值文本</returns>
        public static string GetDistrictText(this HtmlForm formMeta,
            UserContext userCtx,
            string pkid,
            string provinceFieldKey,
            string cityFieldKey,
            string regionFieldKey,
            string addressFieldKey)
        {
            var provinceField = formMeta.GetField(provinceFieldKey);
            var cityField = formMeta.GetField(cityFieldKey);
            var regionField = formMeta.GetField(regionFieldKey);
            if (pkid.IsNullOrEmptyOrWhiteSpace() || provinceField == null || cityField == null || regionField == null)
            {
                return string.Empty;
            }

            var addressFieldName = "";
            var addressFieldNameSelect = "";
            if (!addressFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                addressFieldName = formMeta.GetField(addressFieldKey).FieldName;
                addressFieldNameSelect = "," + addressFieldName;
            }

            var sqlText = $@"
            select p.fenumitem province, c.fenumitem city, r.fenumitem region {addressFieldNameSelect} from {formMeta.BillHeadTableName} d 
            left join t_bd_enumdataentry p on p.fentryid = d.{provinceField.FieldName}
            left join t_bd_enumdataentry c on c.fentryid = d.{cityField.FieldName}
            left join t_bd_enumdataentry r on r.fentryid = d.{regionField.FieldName}
            where d.fid = @fid";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fid", System.Data.DbType.String, pkid)
            };

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObj = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam)?.FirstOrDefault();
            var province = Convert.ToString(dynObj?["province"]) ?? "";
            var city = Convert.ToString(dynObj?["city"]) ?? "";
            var region = Convert.ToString(dynObj?["region"]) ?? "";

            var address = "";
            if (!addressFieldName.IsNullOrEmptyOrWhiteSpace())
            {
                address = Convert.ToString(dynObj?["faddress"]) ?? "";
            }

            return GetDistrictText(province, city, region, address);
        }

        /// <summary>
        /// 获取省、市、区、详细地址组合值文本
        /// </summary>
        /// <param name="province">省份</param>
        /// <param name="city">城市</param>
        /// <param name="region">区县</param>
        /// <param name="address">详细地址</param>
        /// <returns>组合值文本</returns>
        public static string GetDistrictText(string province, string city, string region, string address)
        {
            return $"{province}{city}{region}{address}";
        }

        /// <summary>
        /// 根据where条件获取业务表单动态对象
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="sqlWhere">主键ID</param>
        /// <param name="sqlParams">参数</param>
        /// <param name="loadRef">是否加载引用数据</param>
        /// <returns>动态对象</returns>
        public static IEnumerable<DynamicObject> GetBizDataByWhere(this HtmlForm formMeta,
            UserContext userCtx,
            string sqlWhere,
            List<SqlParam> sqlParams,
            bool loadRef = false)
        {
            var reader = userCtx.GetPkIdDataReader(formMeta, sqlWhere, sqlParams);

            var formDt = formMeta.GetDynamicObjectType(userCtx);
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, formDt);

            var dynObjs = dm.SelectBy(reader).OfType<DynamicObject>().ToList();
            if (dynObjs != null && loadRef)
            {
                //加载引用数据
                var refMgr = userCtx.Container.GetService<LoadReferenceObjectManager>();
                refMgr.Load(userCtx, formDt, dynObjs, false);
            }
            return dynObjs;
        }

        /// <summary>
        /// 按某个字段（如单据编码、基础资料编号）获取业务表单动态对象
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="no">编号</param>
        /// <param name="fldKey">字段名</param>
        /// <param name="loadRef">是否加载引用数据</param>
        /// <returns>动态对象</returns>
        public static DynamicObject GetBizDataByNo(this HtmlForm formMeta,
            UserContext userCtx,
            string no,
            string fldKey,
            bool loadRef = false)
        {
            if (no.IsNullOrEmptyOrWhiteSpace()) return null;

            string sqlWhere = $"fmainorgid=@fmainorgid and {fldKey}=@no";
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("@no", DbType.String, no),
            };

            var dynObjs = formMeta.GetBizDataByWhere(userCtx, sqlWhere, sqlParams, loadRef);
            return dynObjs?.FirstOrDefault();
        }

        /// <summary>
        /// 根据主键ID获取业务表单动态对象
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="pkid">主键ID</param>
        /// <param name="loadRef">是否加载引用数据</param>
        /// <returns>动态对象</returns>
        public static DynamicObject GetBizDataById(this HtmlForm formMeta,
            UserContext userCtx,
            object pkid,
            bool loadRef = false)
        {
            if (pkid.IsNullOrEmptyOrWhiteSpace()) return null;
            var dynObjs = formMeta.GetBizDataById(userCtx, new object[] { pkid }, loadRef);
            return dynObjs?.FirstOrDefault();
        }

        /// <summary>
        /// 根据主键ID集合获取业务表单动态对象集合
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="pkids">主键ID集合</param>
        /// <param name="loadRef">是否加载引用数据</param>
        /// <returns>动态对象集合</returns>
        public static IEnumerable<DynamicObject> GetBizDataById(this HtmlForm formMeta,
            UserContext userCtx,
            IEnumerable<object> pkids,
            bool loadRef = false)
        {
            if (pkids == null || !pkids.Any()) return null;

            var formDt = formMeta.GetDynamicObjectType(userCtx);
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, formDt);

            var dynObjs = dm.Select(pkids).OfType<DynamicObject>();
            if (dynObjs != null && loadRef)
            {
                //加载引用数据
                var refMgr = userCtx.Container.GetService<LoadReferenceObjectManager>();
                refMgr.Load(userCtx, formDt, dynObjs, false);
            }
            return dynObjs;
        }
    }
}