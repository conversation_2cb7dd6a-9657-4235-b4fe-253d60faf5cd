using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO.KingdeePay
{
    /// <summary>
    /// 金蝶金融调用通用接口
    /// </summary>
    [Route("/gateway/api")]
    public class KingdeePayReqDto
    {
        /// <summary>
        /// 业务参数的集合，除公共参数外所有请求参数都必须放在这个参数中传递，具体参照各产品快速接入文档
        /// </summary>
        public string bizContent { get; set; }
        /// <summary>
        /// 商户,金蝶平台配給商户的商户号
        /// </summary>
        public string merchantNo { get; set; }
        /// <summary>
        /// 服务代码
        /// </summary>
        public string service { get; set; }
        /// <summary>
        /// 签名
        /// </summary>
        public string sign { get; set; }
        /// <summary>
        /// 签名类型
        /// </summary>
        public string signType { get; set; }
        /// <summary>
        /// 加密类型
        /// </summary>
        public string encType { get; set; }
        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string timestamp { get; set; }
        /// <summary>
        /// 版本号
        /// </summary>
        public string version { get; set; }
        /// <summary>
        /// 随机字符串
        /// </summary>
        public string nonceStr { get; set; }

    }
    /// <summary>
    /// 请求金蝶webkpi客户主扫接口业务数据原型
    /// </summary>
    public class KingdeeQrCodePay
    {
        /// <summary>
        /// 交易金额(必填)
        /// </summary>
        public string amount { get; set; }
        /// <summary>
        /// 业务订单号(必填)
        /// </summary>
        public string bizNo { get; set; }
        /// <summary>
        /// 机构编码(必填)
        /// </summary>
        public string orgCode { get; set; }
        /// <summary>
        /// 订单来源(必填)
        /// </summary>
        public int? orderSrc { get; set; }
        /// <summary>
        /// 通知地址
        /// </summary>
        public string notifyUrl { get; set; } 
        /// <summary>
        /// 用户ip(必填)
        /// </summary>
        public string ip { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string productName { get; set; } 
        /// <summary>
        /// 产品类型
        /// </summary>
        public string productType { get; set; } 
        /// <summary>
        /// 产品描述
        /// </summary>
        public string productDesc { get; set; } 
        /// <summary>
        /// 订单超时时间
        /// </summary>
        public int? expireTimeout { get; set; } 
        /// <summary>
        /// 分账说明
        /// </summary>
        public string attach { get; set; } 
        /// <summary>
        /// 销售员
        /// </summary>
        public string saleMan { get; set; } 
        /// <summary>
        /// 门店号
        /// </summary>
        public string storeNum { get; set; }
    }
    /// <summary>
    /// 10.公众号/小程序支付接口
    /// </summary>
    public class KingdeePrePay
    {
        /// <summary>
        /// 业务订单号
        /// </summary>
        public string bizNo { get; set; }
        /// <summary>
        /// 交易金额
        /// </summary>
        public Decimal amount { get; set; }
        /// <summary>
        /// 机构编码
        /// </summary>
        public string orgCode { get; set; }
        /// <summary>
        /// 订单来源
        /// </summary>
        public int? orderSrc { get; set; }
        /// <summary>
        /// 通知地址
        /// </summary>
        public string notifyUrl { get; set; }
        /// <summary>
        /// 用户ip
        /// </summary>
        public string ip { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string productName { get; set; }
        /// <summary>
        /// 产品类型
        /// </summary>
        public string productType { get; set; }
        /// <summary>
        /// 产品描述
        /// </summary>
        public string productDesc { get; set; }
        /// <summary>
        /// 返回地址
        /// </summary>
        public string returnUrl { get; set; }
        /// <summary>
        /// 订单超时时间
        /// </summary>
        public int? expireTimeout { get; set; }
        /// <summary>
        /// 用户在微信或者支付宝中的openid
        /// </summary>
        public string openid { get; set; }
        /// <summary>
        /// appid
        /// </summary>
        public string appid { get; set; }
        /// <summary>
        /// appid类型 空：微信公众号1：微信小程序
        /// </summary>
        public int? appIdType { get; set; }
        /// <summary>
        /// 分账说明
        /// </summary>
        public string attach { get; set; }
    }




}
